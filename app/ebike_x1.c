/**
 * @file ebike_x1.c
 * @brief EBIKE X1 应用程序
 */

#include "ebike_x1.h"
#include "eb_core.h"
#include "eb_plugin.h"

// 插件头文件
#include "plugins/plug_uart.h"
#include "plugins/plug_hicar.h"
#include "plugins/plug_bridge.h"

// 前向声明
static void app_init_all_plugins(void);

// 修复ui报错问题
lv_obj_t *ui_imgset_;

// APP全局变量
lv_style_t g_style;
uint32_t g_time_ms = 0;
uint32_t g_time_second = 0;
date_time_t g_date_time = {};

/**
 * @brief 初始化应用任务
 */
void app_init()
{

  // 初始化时间
  sys_date_init();

  // 初始化全局样式
  lv_style_init(&g_style);
  lv_style_set_text_font(&g_style, &PuHui16); // 全局默认字体
  lv_style_set_text_color(&g_style, lv_color_hex(0xFFFFFF));
  lv_obj_add_style(lv_scr_act(), &g_style, LV_PART_MAIN);

  // 初始化多语言模块
  lv_res_t res = lv_i18n_init(lv_i18n_language_pack, LV_LANG_ZH);
  if (res != LV_RES_OK)
  {
    LV_LOG_ERROR("Failed to initialize i18n module");
    return;
  }

  // 初始化事件系统
  eb_init();
  printf("事件系统初始化完成\n");

  // 初始化UI
  ui_init();
  printf("UI初始化完成\n");

  // 初始化插件
  app_init_all_plugins(); 

  // 挂载仪表盘UI
  dashboard_init();

  printf("应用任务初始化完成\n");
}

/**
 * @brief 应用任务处理
 */
void app_handler()
{
  // 多语言刷新
  lv_i18n_refresh_ui();
}

/**
 * @brief 清理应用任务
 */
void app_deinit()
{

  // 清理事件系统
  eb_deinit();

  printf("应用任务已清理\n");
}

static void app_init_all_plugins(void) {

  // 初始化并注册所有插件
  printf("开始初始化插件...\n");
  fflush(stdout);

  // 初始化UART插件
  printf("正在初始化UART插件...\n");
  fflush(stdout);
  if (plug_uart_init("/dev/ttyS5", 115200) != 0) {
    printf("UART插件初始化失败，继续其他插件初始化\n");
    fflush(stdout);
  } else {
    printf("UART插件初始化成功\n");
    fflush(stdout);
  }

  // 初始化HiCar插件
  printf("正在初始化HiCar插件...\n");
  fflush(stdout);
  if (plug_hicar_init() != 0) {
    printf("HiCar插件初始化失败，继续其他插件初始化\n");
    fflush(stdout);
  } else {
    printf("HiCar插件初始化成功\n");
    fflush(stdout);
  }

  // 初始化桥接插件
  printf("正在初始化桥接插件...\n");
  fflush(stdout);
  if (plug_bridge_init() != 0) {
    printf("桥接插件初始化失败，继续其他插件初始化\n");
    fflush(stdout);
  } else {
    printf("桥接插件初始化成功\n");
    fflush(stdout);
  }

  printf("插件注册完成，开始启动插件...\n");
  fflush(stdout);

  // 启动所有已注册的插件
  eb_plug_start_all();

  printf("插件启动完成\n");
  fflush(stdout);
}