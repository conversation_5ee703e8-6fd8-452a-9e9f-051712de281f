/**
 * @file vehicle_data.h
 * @brief 车辆数据管理模块 - 统一管理所有车辆状态数据
 * @version 1.0.0
 * @date 2025-07-22
 */

#ifndef VEHICLE_DATA_H
#define VEHICLE_DATA_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 灯光状态结构体
 */
typedef struct {
    bool left_turn;      /**< 左转向灯状态 */
    bool right_turn;     /**< 右转向灯状态 */
    bool headlight;      /**< 前大灯状态 */
    bool double_flash;   /**< 双闪状态 */
    bool brake_light;    /**< 刹车灯状态 */
    bool reverse_light;  /**< 倒车灯状态 */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_light_data_t;

/**
 * @brief 电池状态结构体
 */
typedef struct {
    uint8_t level;       /**< 电池电量百分比 (0-100) */
    bool is_charging;    /**< 是否正在充电 */
    float voltage;       /**< 电池电压 */
    float current;       /**< 电池电流 */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_battery_data_t;

/**
 * @brief 运行状态结构体
 */
typedef struct {
    uint16_t speed;         /**< 当前速度 km/h */
    uint32_t total_mileage; /**< 总里程 km */
    uint32_t trip_mileage;  /**< 小计里程 km */
    float temperature;      /**< 温度 °C */
    uint32_t power_current; /**< 当前功率 W */
    uint32_t power_average; /**< 平均功率 W */
    uint32_t trip_time;     /**< 行程时间(分钟) */
    bool cruise_enabled;    /**< 巡航开启状态 */
    uint16_t cruise_speed;  /**< 巡航速度 km/h */
    uint32_t last_update;   /**< 最后更新时间戳 */
} vehicle_running_data_t;

/**
 * @brief 车身姿态结构体
 */
typedef struct {
    float pitch;         /**< 俯仰角 */
    float roll;          /**< 横滚角 */
    float acceleration;  /**< 加速度 */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_attitude_data_t;

/**
 * @brief 时间数据结构体
 */
typedef struct {
    uint8_t hour;         /**< 小时 (0-23) */
    uint8_t minute;       /**< 分钟 (0-59) */
    uint8_t second;       /**< 秒 (0-59) */
    uint16_t millisecond; /**< 毫秒 (0-999) */
    uint16_t year;        /**< 年 */
    uint8_t month;        /**< 月 (1-12) */
    uint8_t day;          /**< 日 (1-31) */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_time_data_t;

/**
 * @brief 系统状态结构体
 */
typedef struct {
    char sw_version[20]; /**< 软件版本 */
    char hw_version[20]; /**< 硬件版本 */
    uint32_t uptime;     /**< 系统运行时间 */
    uint32_t last_update; /**< 最后更新时间戳 */
} vehicle_system_data_t;

/**
 * @brief 车辆完整数据结构体
 */
typedef struct {
    vehicle_light_data_t light;      /**< 灯光数据 */
    vehicle_battery_data_t battery;  /**< 电池数据 */
    vehicle_running_data_t running;  /**< 运行数据 */
    vehicle_attitude_data_t attitude; /**< 车身姿态数据 */
    vehicle_system_data_t system;    /**< 系统数据 */
    vehicle_time_data_t time;        /**< 时间数据 */
} vehicle_data_t;

/**
 * @brief 数据变化回调函数类型
 * @param data_type 数据类型标识
 * @param data 变化的数据指针
 * @param user_data 用户数据
 */
typedef void (*vehicle_data_callback_t)(const char* data_type, const void* data, void* user_data);

/**
 * @brief 数据变化通知订阅结构体
 */
typedef struct vehicle_data_subscription {
    char data_type[32];              /**< 订阅的数据类型 */
    vehicle_data_callback_t callback; /**< 回调函数 */
    void* user_data;                 /**< 用户数据 */
    struct vehicle_data_subscription* next; /**< 链表下一个节点 */
} vehicle_data_subscription_t;

// =============================================================================
// 初始化和清理函数
// =============================================================================

/**
 * @brief 初始化车辆数据管理模块
 * @return 0 成功，-1 失败
 */
int vehicle_data_init(void);

/**
 * @brief 清理车辆数据管理模块
 */
void vehicle_data_deinit(void);

// =============================================================================
// 数据访问函数
// =============================================================================

/**
 * @brief 获取完整的车辆数据
 * @return 车辆数据指针（只读）
 */
const vehicle_data_t* vehicle_data_get_all(void);

/**
 * @brief 获取灯光数据
 * @return 灯光数据指针（只读）
 */
const vehicle_light_data_t* vehicle_data_get_light(void);

/**
 * @brief 获取电池数据
 * @return 电池数据指针（只读）
 */
const vehicle_battery_data_t* vehicle_data_get_battery(void);

/**
 * @brief 获取运行数据
 * @return 运行数据指针（只读）
 */
const vehicle_running_data_t* vehicle_data_get_running(void);

/**
 * @brief 获取车身姿态数据
 * @return 车身姿态数据指针（只读）
 */
const vehicle_attitude_data_t* vehicle_data_get_attitude(void);

/**
 * @brief 获取系统数据
 * @return 系统数据指针（只读）
 */
const vehicle_system_data_t* vehicle_data_get_system(void);

/**
 * @brief 获取时间数据
 * @return 时间数据指针（只读）
 */
const vehicle_time_data_t* vehicle_data_get_time(void);

// =============================================================================
// 数据更新函数
// =============================================================================

/**
 * @brief 更新灯光数据
 * @param light 新的灯光数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_light(const vehicle_light_data_t* light);

/**
 * @brief 更新电池数据
 * @param battery 新的电池数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_battery(const vehicle_battery_data_t* battery);

/**
 * @brief 更新运行数据
 * @param running 新的运行数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_running(const vehicle_running_data_t* running);

/**
 * @brief 更新车身姿态数据
 * @param attitude 新的车身姿态数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_attitude(const vehicle_attitude_data_t* attitude);

/**
 * @brief 更新系统数据
 * @param system 新的系统数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_system(const vehicle_system_data_t* system);

/**
 * @brief 更新时间数据
 * @param time 新的时间数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_update_time(const vehicle_time_data_t* time);

// =============================================================================
// 便捷更新函数（单个字段）
// =============================================================================

/**
 * @brief 更新左转向灯状态
 * @param state 新状态
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_left_turn(bool state);

/**
 * @brief 更新右转向灯状态
 * @param state 新状态
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_right_turn(bool state);

/**
 * @brief 更新前大灯状态
 * @param state 新状态
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_headlight(bool state);

/**
 * @brief 更新双闪状态
 * @param state 新状态
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_double_flash(bool state);

/**
 * @brief 更新电池电量
 * @param level 电量百分比 (0-100)
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_battery_level(uint8_t level);

/**
 * @brief 更新充电状态
 * @param is_charging 是否正在充电
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_charging_state(bool is_charging);

/**
 * @brief 更新速度
 * @param speed 速度 km/h
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_speed(uint16_t speed);

/**
 * @brief 更新当前功率
 * @param power 功率 W
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_power(uint32_t power);

/**
 * @brief 更新温度
 * @param temperature 温度 °C
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_temperature(float temperature);

/**
 * @brief 更新巡航状态
 * @param enabled 是否开启巡航
 * @param speed 巡航速度 km/h
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_cruise(bool enabled, uint16_t speed);

/**
 * @brief 更新行程时间
 * @param trip_time 行程时间(分钟)
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_trip_time(uint32_t trip_time);

/**
 * @brief 更新里程
 * @param total_mileage 总里程
 * @param trip_mileage 小计里程
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_mileage(uint32_t total_mileage, uint32_t trip_mileage);

/**
 * @brief 更新系统时间
 * @param hour 小时
 * @param minute 分钟
 * @param second 秒
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_time(uint8_t hour, uint8_t minute, uint8_t second);

/**
 * @brief 更新系统日期
 * @param year 年
 * @param month 月
 * @param day 日
 * @return 0 成功，-1 失败
 */
int vehicle_data_set_date(uint16_t year, uint8_t month, uint8_t day);

// =============================================================================
// 数据变化通知系统
// =============================================================================

/**
 * @brief 订阅数据变化通知
 * @param data_type 数据类型 ("light", "battery", "running", "attitude", "system")
 * @param callback 回调函数
 * @param user_data 用户数据
 * @return 0 成功，-1 失败
 */
int vehicle_data_subscribe(const char* data_type, vehicle_data_callback_t callback, void* user_data);

/**
 * @brief 取消订阅数据变化通知
 * @param data_type 数据类型
 * @param callback 回调函数
 * @return 0 成功，-1 失败
 */
int vehicle_data_unsubscribe(const char* data_type, vehicle_data_callback_t callback);

// =============================================================================
// 兼容性函数（与现有全局变量兼容）
// =============================================================================

/**
 * @brief 同步数据到全局变量（向后兼容）
 */
void vehicle_data_sync_to_globals(void);

/**
 * @brief 从全局变量同步数据
 */
void vehicle_data_sync_from_globals(void);

#ifdef __cplusplus
}
#endif

#endif // VEHICLE_DATA_H
