/**
 * @file vehicle_data.c
 * @brief 车辆数据管理模块实现
 * @version 1.0.0
 * @date 2025-07-22
 */

#include "vehicle_data.h"
#include "../../ebike_x1.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

// =============================================================================
// 内部数据和状态
// =============================================================================

static vehicle_data_t g_vehicle_data = {0};
static vehicle_data_subscription_t* g_subscriptions = NULL;
static bool g_initialized = false;

// =============================================================================
// 内部函数声明
// =============================================================================

static void notify_subscribers(const char* data_type, const void* data);
static uint32_t get_current_timestamp(void);

// =============================================================================
// 初始化和清理函数
// =============================================================================

int vehicle_data_init(void) {
    if (g_initialized) {
        return 0; // 已经初始化
    }
    
    // 初始化数据结构
    memset(&g_vehicle_data, 0, sizeof(vehicle_data_t));
    
    // 设置默认值
    strcpy(g_vehicle_data.system.sw_version, "v1.0.0");
    strcpy(g_vehicle_data.system.hw_version, "hw1.0");
    
    // 从现有全局变量同步初始数据
    vehicle_data_sync_from_globals();
    
    g_initialized = true;
    printf("车辆数据管理模块初始化完成\n");
    return 0;
}

void vehicle_data_deinit(void) {
    if (!g_initialized) {
        return;
    }
    
    // 清理订阅链表
    vehicle_data_subscription_t* current = g_subscriptions;
    while (current) {
        vehicle_data_subscription_t* next = current->next;
        free(current);
        current = next;
    }
    g_subscriptions = NULL;
    
    g_initialized = false;
    printf("车辆数据管理模块已清理\n");
}

// =============================================================================
// 数据访问函数
// =============================================================================

const vehicle_data_t* vehicle_data_get_all(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data;
}

const vehicle_light_data_t* vehicle_data_get_light(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.light;
}

const vehicle_battery_data_t* vehicle_data_get_battery(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.battery;
}

const vehicle_running_data_t* vehicle_data_get_running(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.running;
}

const vehicle_attitude_data_t* vehicle_data_get_attitude(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.attitude;
}

const vehicle_system_data_t* vehicle_data_get_system(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.system;
}

const vehicle_time_data_t* vehicle_data_get_time(void) {
    if (!g_initialized) {
        return NULL;
    }
    return &g_vehicle_data.time;
}

// =============================================================================
// 数据更新函数
// =============================================================================

int vehicle_data_update_light(const vehicle_light_data_t* light) {
    if (!g_initialized || !light) {
        return -1;
    }
    
    // 检查是否有变化
    bool changed = memcmp(&g_vehicle_data.light, light, sizeof(vehicle_light_data_t)) != 0;
    
    // 更新数据
    g_vehicle_data.light = *light;
    g_vehicle_data.light.last_update = get_current_timestamp();
    
    // 同步到全局变量
    vehicle_data_sync_to_globals();
    
    // 通知订阅者
    if (changed) {
        notify_subscribers("light", &g_vehicle_data.light);
    }
    
    return 0;
}

int vehicle_data_update_battery(const vehicle_battery_data_t* battery) {
    if (!g_initialized || !battery) {
        return -1;
    }
    
    bool changed = memcmp(&g_vehicle_data.battery, battery, sizeof(vehicle_battery_data_t)) != 0;
    
    g_vehicle_data.battery = *battery;
    g_vehicle_data.battery.last_update = get_current_timestamp();
    
    vehicle_data_sync_to_globals();
    
    if (changed) {
        notify_subscribers("battery", &g_vehicle_data.battery);
    }
    
    return 0;
}

int vehicle_data_update_running(const vehicle_running_data_t* running) {
    if (!g_initialized || !running) {
        return -1;
    }
    
    bool changed = memcmp(&g_vehicle_data.running, running, sizeof(vehicle_running_data_t)) != 0;
    
    g_vehicle_data.running = *running;
    g_vehicle_data.running.last_update = get_current_timestamp();
    
    vehicle_data_sync_to_globals();
    
    if (changed) {
        notify_subscribers("running", &g_vehicle_data.running);
    }
    
    return 0;
}

int vehicle_data_update_attitude(const vehicle_attitude_data_t* attitude) {
    if (!g_initialized || !attitude) {
        return -1;
    }
    
    bool changed = memcmp(&g_vehicle_data.attitude, attitude, sizeof(vehicle_attitude_data_t)) != 0;
    
    g_vehicle_data.attitude = *attitude;
    g_vehicle_data.attitude.last_update = get_current_timestamp();
    
    vehicle_data_sync_to_globals();
    
    if (changed) {
        notify_subscribers("attitude", &g_vehicle_data.attitude);
    }
    
    return 0;
}

int vehicle_data_update_system(const vehicle_system_data_t* system) {
    if (!g_initialized || !system) {
        return -1;
    }
    
    bool changed = memcmp(&g_vehicle_data.system, system, sizeof(vehicle_system_data_t)) != 0;
    
    g_vehicle_data.system = *system;
    g_vehicle_data.system.last_update = get_current_timestamp();
    
    vehicle_data_sync_to_globals();
    
    if (changed) {
        notify_subscribers("system", &g_vehicle_data.system);
    }
    
    return 0;
}

int vehicle_data_update_time(const vehicle_time_data_t* time) {
    if (!g_initialized || !time) {
        return -1;
    }

    bool changed = memcmp(&g_vehicle_data.time, time, sizeof(vehicle_time_data_t)) != 0;

    g_vehicle_data.time = *time;
    g_vehicle_data.time.last_update = get_current_timestamp();

    vehicle_data_sync_to_globals();

    if (changed) {
        notify_subscribers("time", &g_vehicle_data.time);
    }

    return 0;
}

// =============================================================================
// 便捷更新函数
// =============================================================================

int vehicle_data_set_left_turn(bool state) {
    if (!g_initialized) {
        return -1;
    }
    
    if (g_vehicle_data.light.left_turn != state) {
        g_vehicle_data.light.left_turn = state;
        g_vehicle_data.light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("light", &g_vehicle_data.light);
    }
    
    return 0;
}

int vehicle_data_set_right_turn(bool state) {
    if (!g_initialized) {
        return -1;
    }
    
    if (g_vehicle_data.light.right_turn != state) {
        g_vehicle_data.light.right_turn = state;
        g_vehicle_data.light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("light", &g_vehicle_data.light);
    }
    
    return 0;
}

int vehicle_data_set_headlight(bool state) {
    if (!g_initialized) {
        return -1;
    }
    
    if (g_vehicle_data.light.headlight != state) {
        g_vehicle_data.light.headlight = state;
        g_vehicle_data.light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("light", &g_vehicle_data.light);
    }
    
    return 0;
}

int vehicle_data_set_double_flash(bool state) {
    if (!g_initialized) {
        return -1;
    }
    
    if (g_vehicle_data.light.double_flash != state) {
        g_vehicle_data.light.double_flash = state;
        g_vehicle_data.light.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("light", &g_vehicle_data.light);
    }
    
    return 0;
}

int vehicle_data_set_battery_level(uint8_t level) {
    printf("[VEHICLE_DATA] 设置电池电量: %d%%, 初始化状态: %s\n",
           level, g_initialized ? "已初始化" : "未初始化");

    if (!g_initialized || level > 100) {
        printf("[VEHICLE_DATA] 电池电量设置失败: %s\n",
               !g_initialized ? "模块未初始化" : "电量值无效");
        return -1;
    }

    if (g_vehicle_data.battery.level != level) {
        printf("[VEHICLE_DATA] 电池电量变化: %d%% -> %d%%\n",
               g_vehicle_data.battery.level, level);
        g_vehicle_data.battery.level = level;
        g_vehicle_data.battery.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("battery", &g_vehicle_data.battery);
        printf("[VEHICLE_DATA] 电池数据变化通知已发送\n");
    } else {
        printf("[VEHICLE_DATA] 电池电量无变化: %d%%\n", level);
    }

    return 0;
}

int vehicle_data_set_charging_state(bool is_charging) {
    if (!g_initialized) {
        return -1;
    }
    
    if (g_vehicle_data.battery.is_charging != is_charging) {
        g_vehicle_data.battery.is_charging = is_charging;
        g_vehicle_data.battery.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("battery", &g_vehicle_data.battery);
    }
    
    return 0;
}

int vehicle_data_set_speed(uint16_t speed) {
    if (!g_initialized) {
        return -1;
    }
    
    if (g_vehicle_data.running.speed != speed) {
        g_vehicle_data.running.speed = speed;
        g_vehicle_data.running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("running", &g_vehicle_data.running);
    }
    
    return 0;
}

int vehicle_data_set_power(uint32_t power) {
    if (!g_initialized) {
        return -1;
    }

    if (g_vehicle_data.running.power_current != power) {
        g_vehicle_data.running.power_current = power;
        g_vehicle_data.running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("running", &g_vehicle_data.running);
    }

    return 0;
}

int vehicle_data_set_temperature(float temperature) {
    if (!g_initialized) {
        return -1;
    }

    if (g_vehicle_data.running.temperature != temperature) {
        g_vehicle_data.running.temperature = temperature;
        g_vehicle_data.running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("running", &g_vehicle_data.running);
    }

    return 0;
}

int vehicle_data_set_cruise(bool enabled, uint16_t speed) {
    if (!g_initialized) {
        return -1;
    }

    bool changed = false;
    if (g_vehicle_data.running.cruise_enabled != enabled) {
        g_vehicle_data.running.cruise_enabled = enabled;
        changed = true;
    }
    if (g_vehicle_data.running.cruise_speed != speed) {
        g_vehicle_data.running.cruise_speed = speed;
        changed = true;
    }

    if (changed) {
        g_vehicle_data.running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("running", &g_vehicle_data.running);
    }

    return 0;
}

int vehicle_data_set_trip_time(uint32_t trip_time) {
    if (!g_initialized) {
        return -1;
    }

    if (g_vehicle_data.running.trip_time != trip_time) {
        g_vehicle_data.running.trip_time = trip_time;
        g_vehicle_data.running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("running", &g_vehicle_data.running);
    }

    return 0;
}

int vehicle_data_set_mileage(uint32_t total_mileage, uint32_t trip_mileage) {
    if (!g_initialized) {
        return -1;
    }

    bool changed = false;
    if (g_vehicle_data.running.total_mileage != total_mileage) {
        g_vehicle_data.running.total_mileage = total_mileage;
        changed = true;
    }
    if (g_vehicle_data.running.trip_mileage != trip_mileage) {
        g_vehicle_data.running.trip_mileage = trip_mileage;
        changed = true;
    }

    if (changed) {
        g_vehicle_data.running.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("running", &g_vehicle_data.running);
    }

    return 0;
}

int vehicle_data_set_time(uint8_t hour, uint8_t minute, uint8_t second) {
    if (!g_initialized || hour > 23 || minute > 59 || second > 59) {
        return -1;
    }

    bool changed = false;
    if (g_vehicle_data.time.hour != hour) {
        g_vehicle_data.time.hour = hour;
        changed = true;
    }
    if (g_vehicle_data.time.minute != minute) {
        g_vehicle_data.time.minute = minute;
        changed = true;
    }
    if (g_vehicle_data.time.second != second) {
        g_vehicle_data.time.second = second;
        changed = true;
    }

    if (changed) {
        g_vehicle_data.time.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("time", &g_vehicle_data.time);
    }

    return 0;
}

int vehicle_data_set_date(uint16_t year, uint8_t month, uint8_t day) {
    if (!g_initialized || month < 1 || month > 12 || day < 1 || day > 31) {
        return -1;
    }

    bool changed = false;
    if (g_vehicle_data.time.year != year) {
        g_vehicle_data.time.year = year;
        changed = true;
    }
    if (g_vehicle_data.time.month != month) {
        g_vehicle_data.time.month = month;
        changed = true;
    }
    if (g_vehicle_data.time.day != day) {
        g_vehicle_data.time.day = day;
        changed = true;
    }

    if (changed) {
        g_vehicle_data.time.last_update = get_current_timestamp();
        vehicle_data_sync_to_globals();
        notify_subscribers("time", &g_vehicle_data.time);
    }

    return 0;
}

// =============================================================================
// 数据变化通知系统
// =============================================================================

int vehicle_data_subscribe(const char* data_type, vehicle_data_callback_t callback, void* user_data) {
    if (!g_initialized || !data_type || !callback) {
        return -1;
    }

    // 创建新的订阅节点
    vehicle_data_subscription_t* subscription = malloc(sizeof(vehicle_data_subscription_t));
    if (!subscription) {
        return -1;
    }

    strncpy(subscription->data_type, data_type, sizeof(subscription->data_type) - 1);
    subscription->data_type[sizeof(subscription->data_type) - 1] = '\0';
    subscription->callback = callback;
    subscription->user_data = user_data;
    subscription->next = g_subscriptions;

    g_subscriptions = subscription;

    printf("订阅数据变化通知: %s\n", data_type);
    return 0;
}

int vehicle_data_unsubscribe(const char* data_type, vehicle_data_callback_t callback) {
    if (!g_initialized || !data_type || !callback) {
        return -1;
    }

    vehicle_data_subscription_t** current = &g_subscriptions;
    while (*current) {
        if (strcmp((*current)->data_type, data_type) == 0 && (*current)->callback == callback) {
            vehicle_data_subscription_t* to_remove = *current;
            *current = (*current)->next;
            free(to_remove);
            printf("取消订阅数据变化通知: %s\n", data_type);
            return 0;
        }
        current = &((*current)->next);
    }

    return -1; // 未找到匹配的订阅
}

// =============================================================================
// 兼容性函数
// =============================================================================

void vehicle_data_sync_to_globals(void) {
    if (!g_initialized) {
        return;
    }

    // 同步灯光数据到全局变量
    // g_has_left = g_vehicle_data.light.left_turn ? 1 : 0;
    // g_has_right = g_vehicle_data.light.right_turn ? 1 : 0;
    // g_f_light_on = g_vehicle_data.light.headlight ? 1 : 0;
    // g_fd_light_on = g_vehicle_data.light.double_flash ? 1 : 0;
    // g_fe_light_on = g_vehicle_data.light.brake_light ? 1 : 0;

    // // 同步电池数据
    // g_battery_level = g_vehicle_data.battery.level;
    // g_is_charging = g_vehicle_data.battery.is_charging ? 1 : 0;

    // // 同步运行数据
    // g_speed = g_vehicle_data.running.speed;
    // g_total_mileage = g_vehicle_data.running.total_mileage;
    // g_sub_mileage = g_vehicle_data.running.trip_mileage;
    // g_temperature = g_vehicle_data.running.temperature;

    // // 同步车身姿态数据
    // g_run_pitch = g_vehicle_data.attitude.pitch;
    // g_run_roll = g_vehicle_data.attitude.roll;
    // g_run_acc = g_vehicle_data.attitude.acceleration;

    // // 同步时间数据
    // g_time_hour = g_vehicle_data.time.hour;
    // g_time_minute = g_vehicle_data.time.minute;
    // g_time_second = g_vehicle_data.time.second;
    // g_time_ms = g_vehicle_data.time.millisecond / 100; // 转换为100ms单位

    // // 同步系统数据
    // strncpy(g_sw_ver, g_vehicle_data.system.sw_version, sizeof(g_sw_ver) - 1);
    // strncpy(g_hw_ver, g_vehicle_data.system.hw_version, sizeof(g_hw_ver) - 1);
}

void vehicle_data_sync_from_globals(void) {
    if (!g_initialized) {
        return;
    }

    uint32_t timestamp = get_current_timestamp();

    // // 从全局变量同步灯光数据
    // g_vehicle_data.light.left_turn = g_has_left ? true : false;
    // g_vehicle_data.light.right_turn = g_has_right ? true : false;
    // g_vehicle_data.light.headlight = g_f_light_on ? true : false;
    // g_vehicle_data.light.double_flash = g_fd_light_on ? true : false;
    // g_vehicle_data.light.brake_light = g_fe_light_on ? true : false;
    // g_vehicle_data.light.last_update = timestamp;

    // // 从全局变量同步电池数据
    // g_vehicle_data.battery.level = g_battery_level;
    // g_vehicle_data.battery.is_charging = g_is_charging ? true : false;
    // g_vehicle_data.battery.last_update = timestamp;

    // // 从全局变量同步运行数据
    // g_vehicle_data.running.speed = g_speed;
    // g_vehicle_data.running.total_mileage = g_total_mileage;
    // g_vehicle_data.running.trip_mileage = g_sub_mileage;
    // g_vehicle_data.running.temperature = g_temperature;
    // g_vehicle_data.running.last_update = timestamp;

    // // 从全局变量同步车身姿态数据
    // g_vehicle_data.attitude.pitch = g_run_pitch;
    // g_vehicle_data.attitude.roll = g_run_roll;
    // g_vehicle_data.attitude.acceleration = g_run_acc;
    // g_vehicle_data.attitude.last_update = timestamp;

    // // 从全局变量同步时间数据
    // g_vehicle_data.time.hour = g_time_hour;
    // g_vehicle_data.time.minute = g_time_minute;
    // g_vehicle_data.time.second = g_time_second;
    // g_vehicle_data.time.millisecond = g_time_ms * 100; // 从100ms单位转换
    // g_vehicle_data.time.last_update = timestamp;

    // // 从全局变量同步系统数据
    // strncpy(g_vehicle_data.system.sw_version, g_sw_ver, sizeof(g_vehicle_data.system.sw_version) - 1);
    // strncpy(g_vehicle_data.system.hw_version, g_hw_ver, sizeof(g_vehicle_data.system.hw_version) - 1);
    g_vehicle_data.system.last_update = timestamp;
}

// =============================================================================
// 内部函数实现
// =============================================================================

static void notify_subscribers(const char* data_type, const void* data) {
    printf("[VEHICLE_DATA] 通知订阅者: 数据类型=%s\n", data_type);

    int subscriber_count = 0;
    vehicle_data_subscription_t* current = g_subscriptions;
    while (current) {
        if (strcmp(current->data_type, data_type) == 0) {
            printf("[VEHICLE_DATA] 调用订阅者回调: %s\n", data_type);
            current->callback(data_type, data, current->user_data);
            subscriber_count++;
        }
        current = current->next;
    }

    printf("[VEHICLE_DATA] 通知完成: %s, 订阅者数量=%d\n", data_type, subscriber_count);
}

static uint32_t get_current_timestamp(void) {
    // 使用现有的全局时间变量
    return g_time_second * 1000 + g_time_ms;
}
