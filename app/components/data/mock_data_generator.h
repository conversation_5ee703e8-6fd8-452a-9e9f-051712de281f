/**
 * @file mock_data_generator.h
 * @brief 模拟数据生成器 - 用于测试dashboard UI数据实时响应
 * @version 1.0.0
 * @date 2025-07-22
 */

#ifndef MOCK_DATA_GENERATOR_H
#define MOCK_DATA_GENERATOR_H

#include "lvgl/lvgl.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// =============================================================================
// 编译控制宏
// =============================================================================

// 启用模拟数据生成器（可通过编译参数控制）
#ifndef ENABLE_MOCK_DATA
#define ENABLE_MOCK_DATA 0  // 默认关闭，测试时开启
#endif

// =============================================================================
// 数据结构定义
// =============================================================================

/**
 * @brief 测试场景类型枚举
 */
typedef enum {
    MOCK_SCENARIO_BATTERY = 0,      /**< 电池状态测试场景 */
    MOCK_SCENARIO_SPEED_POWER,      /**< 速度和功率测试场景 */
    MOCK_SCENARIO_LIGHTS,           /**< 灯光状态测试场景 */
    MOCK_SCENARIO_TEMPERATURE_TIME, /**< 温度和时间测试场景 */
    MOCK_SCENARIO_COMPREHENSIVE,    /**< 综合测试场景 */
    MOCK_SCENARIO_COUNT             /**< 场景总数 */
} mock_scenario_type_t;

/**
 * @brief 测试场景执行函数类型
 * @param step 当前执行步骤
 * @param elapsed_ms 场景开始后经过的毫秒数
 */
typedef void (*mock_scenario_execute_t)(uint32_t step, uint32_t elapsed_ms);

/**
 * @brief 测试场景结构体
 */
typedef struct {
    const char* name;                    /**< 场景名称 */
    uint32_t duration_ms;                /**< 场景持续时间(毫秒) */
    uint32_t update_interval_ms;         /**< 更新间隔(毫秒) */
    mock_scenario_execute_t execute;     /**< 执行函数 */
    const char* description;             /**< 场景描述 */
} mock_test_scenario_t;

/**
 * @brief 模拟数据生成器状态
 */
typedef enum {
    MOCK_STATE_STOPPED = 0,    /**< 停止状态 */
    MOCK_STATE_RUNNING,        /**< 运行状态 */
    MOCK_STATE_PAUSED          /**< 暂停状态 */
} mock_generator_state_t;

/**
 * @brief 模拟数据生成器结构体
 */
typedef struct {
    bool enabled;                        /**< 是否启用模拟 */
    mock_generator_state_t state;        /**< 当前状态 */
    uint32_t current_scenario;           /**< 当前场景索引 */
    uint32_t scenario_step;              /**< 当前场景步骤 */
    uint32_t scenario_start_time;        /**< 场景开始时间 */
    lv_timer_t *timer;                   /**< 定时器 */
    uint32_t total_updates;              /**< 总更新次数 */
    uint32_t error_count;                /**< 错误计数 */
} mock_data_generator_t;

/**
 * @brief 模拟数据统计信息
 */
typedef struct {
    uint32_t total_scenarios_run;        /**< 运行的场景总数 */
    uint32_t total_data_updates;         /**< 数据更新总次数 */
    uint32_t battery_updates;            /**< 电池数据更新次数 */
    uint32_t speed_updates;              /**< 速度数据更新次数 */
    uint32_t light_updates;              /**< 灯光数据更新次数 */
    uint32_t temperature_updates;        /**< 温度数据更新次数 */
    uint32_t time_updates;               /**< 时间数据更新次数 */
    uint32_t error_count;                /**< 错误总数 */
    uint32_t start_time;                 /**< 开始时间 */
} mock_data_statistics_t;

// =============================================================================
// 公共接口函数
// =============================================================================

/**
 * @brief 初始化模拟数据生成器
 * @return 0 成功，-1 失败
 */
int mock_data_generator_init(void);

/**
 * @brief 清理模拟数据生成器
 */
void mock_data_generator_deinit(void);

/**
 * @brief 启动模拟数据生成器
 * @param scenario_type 场景类型，-1表示循环所有场景
 * @return 0 成功，-1 失败
 */
int mock_data_generator_start(int scenario_type);

/**
 * @brief 停止模拟数据生成器
 */
void mock_data_generator_stop(void);

/**
 * @brief 暂停模拟数据生成器
 */
void mock_data_generator_pause(void);

/**
 * @brief 恢复模拟数据生成器
 */
void mock_data_generator_resume(void);

/**
 * @brief 切换到下一个场景
 * @return 0 成功，-1 失败
 */
int mock_data_generator_next_scenario(void);

/**
 * @brief 获取当前状态
 * @return 当前状态
 */
mock_generator_state_t mock_data_generator_get_state(void);

/**
 * @brief 获取当前场景信息
 * @param scenario_type 输出当前场景类型
 * @param elapsed_ms 输出场景已运行时间
 * @return 0 成功，-1 失败
 */
int mock_data_generator_get_current_scenario(uint32_t *scenario_type, uint32_t *elapsed_ms);

/**
 * @brief 获取统计信息
 * @return 统计信息指针（只读）
 */
const mock_data_statistics_t* mock_data_generator_get_statistics(void);

/**
 * @brief 重置统计信息
 */
void mock_data_generator_reset_statistics(void);

/**
 * @brief 打印当前状态和统计信息
 */
void mock_data_generator_print_status(void);

// =============================================================================
// 场景管理函数
// =============================================================================

/**
 * @brief 获取场景总数
 * @return 场景总数
 */
uint32_t mock_get_scenario_count(void);

/**
 * @brief 获取场景信息
 * @param scenario_type 场景类型
 * @return 场景信息指针，失败返回NULL
 */
const mock_test_scenario_t* mock_get_scenario_info(mock_scenario_type_t scenario_type);

/**
 * @brief 获取场景名称
 * @param scenario_type 场景类型
 * @return 场景名称字符串
 */
const char* mock_get_scenario_name(mock_scenario_type_t scenario_type);

// =============================================================================
// 数据注入函数（内部使用）
// =============================================================================

/**
 * @brief 注入电池数据
 * @param level 电量百分比 (0-100)
 * @param is_charging 是否充电
 */
void mock_inject_battery_data(uint8_t level, bool is_charging);

/**
 * @brief 注入速度和功率数据
 * @param speed 速度 km/h
 * @param power 功率 W
 */
void mock_inject_speed_power_data(uint16_t speed, uint32_t power);

/**
 * @brief 注入灯光数据
 * @param left_turn 左转灯
 * @param right_turn 右转灯
 * @param headlight 前灯
 * @param double_flash 双闪
 */
void mock_inject_light_data(bool left_turn, bool right_turn, bool headlight, bool double_flash);

/**
 * @brief 注入温度数据
 * @param temperature 温度 °C
 */
void mock_inject_temperature_data(float temperature);

/**
 * @brief 注入时间数据
 * @param hour 小时
 * @param minute 分钟
 * @param second 秒
 */
void mock_inject_time_data(uint8_t hour, uint8_t minute, uint8_t second);

/**
 * @brief 注入里程数据
 * @param total_mileage 总里程
 * @param trip_mileage 小计里程
 */
void mock_inject_mileage_data(uint32_t total_mileage, uint32_t trip_mileage);

/**
 * @brief 注入巡航数据
 * @param enabled 是否启用巡航
 * @param speed 巡航速度
 */
void mock_inject_cruise_data(bool enabled, uint16_t speed);

// =============================================================================
// 便捷宏定义
// =============================================================================

#if ENABLE_MOCK_DATA
    #define MOCK_INIT()                 mock_data_generator_init()
    #define MOCK_START(scenario)        mock_data_generator_start(scenario)
    #define MOCK_STOP()                 mock_data_generator_stop()
    #define MOCK_DEINIT()               mock_data_generator_deinit()
    #define MOCK_PRINT_STATUS()         mock_data_generator_print_status()
#else
    #define MOCK_INIT()                 (0)
    #define MOCK_START(scenario)        (0)
    #define MOCK_STOP()                 do {} while(0)
    #define MOCK_DEINIT()               do {} while(0)
    #define MOCK_PRINT_STATUS()         do {} while(0)
#endif

#ifdef __cplusplus
}
#endif

#endif // MOCK_DATA_GENERATOR_H
