/**
 * @file mock_data_generator.c
 * @brief 模拟数据生成器实现
 * @version 1.0.0
 * @date 2025-07-22
 */

#include "mock_data_generator.h"
#include "vehicle_data.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

#define ENABLE_MOCK_DATA 1

#if ENABLE_MOCK_DATA

// =============================================================================
// 内部变量
// =============================================================================

static mock_data_generator_t g_mock_generator = {0};
static mock_data_statistics_t g_mock_statistics = {0};
static bool g_mock_initialized = false;

// =============================================================================
// 场景执行函数声明
// =============================================================================

static void mock_scenario_battery(uint32_t step, uint32_t elapsed_ms);
static void mock_scenario_speed_power(uint32_t step, uint32_t elapsed_ms);
static void mock_scenario_lights(uint32_t step, uint32_t elapsed_ms);
static void mock_scenario_temperature_time(uint32_t step, uint32_t elapsed_ms);
static void mock_scenario_comprehensive(uint32_t step, uint32_t elapsed_ms);

// =============================================================================
// 场景定义
// =============================================================================

static const mock_test_scenario_t g_test_scenarios[MOCK_SCENARIO_COUNT] = {
    {
        .name = "电池状态测试",
        .duration_ms = 30000,  // 30秒
        .update_interval_ms = 200,
        .execute = mock_scenario_battery,
        .description = "测试电池电量变化和充电状态切换"
    },
    {
        .name = "速度功率测试",
        .duration_ms = 40000,  // 40秒
        .update_interval_ms = 150,
        .execute = mock_scenario_speed_power,
        .description = "测试速度和功率数据变化"
    },
    {
        .name = "灯光状态测试",
        .duration_ms = 25000,  // 25秒
        .update_interval_ms = 500,
        .execute = mock_scenario_lights,
        .description = "测试各种灯光状态切换和闪烁效果"
    },
    {
        .name = "温度时间测试",
        .duration_ms = 20000,  // 20秒
        .update_interval_ms = 300,
        .execute = mock_scenario_temperature_time,
        .description = "测试温度变化和时间更新"
    },
    {
        .name = "综合测试",
        .duration_ms = 60000,  // 60秒
        .update_interval_ms = 100,
        .execute = mock_scenario_comprehensive,
        .description = "综合测试所有数据类型的变化"
    }
};

// =============================================================================
// 内部函数声明
// =============================================================================

static void mock_timer_callback(lv_timer_t *timer);
static void mock_switch_to_scenario(uint32_t scenario_type);
static void mock_update_statistics(const char* data_type);
static uint32_t mock_get_current_time(void);

// =============================================================================
// 公共接口函数实现
// =============================================================================

int mock_data_generator_init(void) {
    if (g_mock_initialized) {
        return 0; // 已经初始化
    }
    
    // 初始化生成器状态
    memset(&g_mock_generator, 0, sizeof(mock_data_generator_t));
    memset(&g_mock_statistics, 0, sizeof(mock_data_statistics_t));
    
    g_mock_generator.enabled = true;
    g_mock_generator.state = MOCK_STATE_STOPPED;
    g_mock_generator.current_scenario = 0;
    
    // 记录开始时间
    g_mock_statistics.start_time = mock_get_current_time();
    
    g_mock_initialized = true;
    printf("模拟数据生成器初始化完成\n");
    return 0;
}

void mock_data_generator_deinit(void) {
    if (!g_mock_initialized) {
        return;
    }
    
    // 停止生成器
    mock_data_generator_stop();
    
    // 清理状态
    memset(&g_mock_generator, 0, sizeof(mock_data_generator_t));
    
    g_mock_initialized = false;
    printf("模拟数据生成器已清理\n");
}

int mock_data_generator_start(int scenario_type) {
    if (!g_mock_initialized || !g_mock_generator.enabled) {
        return -1;
    }
    
    // 停止现有定时器
    if (g_mock_generator.timer) {
        lv_timer_del(g_mock_generator.timer);
        g_mock_generator.timer = NULL;
    }
    
    // 设置场景
    if (scenario_type >= 0 && scenario_type < MOCK_SCENARIO_COUNT) {
        g_mock_generator.current_scenario = scenario_type;
    } else {
        g_mock_generator.current_scenario = 0; // 从第一个场景开始
    }
    
    // 切换到指定场景
    mock_switch_to_scenario(g_mock_generator.current_scenario);
    
    // 创建定时器
    const mock_test_scenario_t *scenario = &g_test_scenarios[g_mock_generator.current_scenario];
    g_mock_generator.timer = lv_timer_create(mock_timer_callback, scenario->update_interval_ms, NULL);
    
    if (!g_mock_generator.timer) {
        printf("模拟数据生成器启动失败: 定时器创建失败\n");
        return -1;
    }
    
    g_mock_generator.state = MOCK_STATE_RUNNING;
    printf("模拟数据生成器已启动，场景: %s\n", scenario->name);
    return 0;
}

void mock_data_generator_stop(void) {
    if (!g_mock_initialized) {
        return;
    }
    
    // 删除定时器
    if (g_mock_generator.timer) {
        lv_timer_del(g_mock_generator.timer);
        g_mock_generator.timer = NULL;
    }
    
    g_mock_generator.state = MOCK_STATE_STOPPED;
    printf("模拟数据生成器已停止\n");
}

void mock_data_generator_pause(void) {
    if (!g_mock_initialized || g_mock_generator.state != MOCK_STATE_RUNNING) {
        return;
    }
    
    if (g_mock_generator.timer) {
        lv_timer_pause(g_mock_generator.timer);
    }
    
    g_mock_generator.state = MOCK_STATE_PAUSED;
    printf("模拟数据生成器已暂停\n");
}

void mock_data_generator_resume(void) {
    if (!g_mock_initialized || g_mock_generator.state != MOCK_STATE_PAUSED) {
        return;
    }
    
    if (g_mock_generator.timer) {
        lv_timer_resume(g_mock_generator.timer);
    }
    
    g_mock_generator.state = MOCK_STATE_RUNNING;
    printf("模拟数据生成器已恢复\n");
}

int mock_data_generator_next_scenario(void) {
    if (!g_mock_initialized || g_mock_generator.state == MOCK_STATE_STOPPED) {
        return -1;
    }
    
    // 切换到下一个场景
    g_mock_generator.current_scenario = (g_mock_generator.current_scenario + 1) % MOCK_SCENARIO_COUNT;
    
    // 重新启动
    return mock_data_generator_start(g_mock_generator.current_scenario);
}

mock_generator_state_t mock_data_generator_get_state(void) {
    if (!g_mock_initialized) {
        return MOCK_STATE_STOPPED;
    }
    return g_mock_generator.state;
}

int mock_data_generator_get_current_scenario(uint32_t *scenario_type, uint32_t *elapsed_ms) {
    if (!g_mock_initialized || !scenario_type || !elapsed_ms) {
        return -1;
    }
    
    *scenario_type = g_mock_generator.current_scenario;
    *elapsed_ms = mock_get_current_time() - g_mock_generator.scenario_start_time;
    return 0;
}

const mock_data_statistics_t* mock_data_generator_get_statistics(void) {
    if (!g_mock_initialized) {
        return NULL;
    }
    return &g_mock_statistics;
}

void mock_data_generator_reset_statistics(void) {
    if (!g_mock_initialized) {
        return;
    }
    
    memset(&g_mock_statistics, 0, sizeof(mock_data_statistics_t));
    g_mock_statistics.start_time = mock_get_current_time();
    printf("模拟数据统计信息已重置\n");
}

void mock_data_generator_print_status(void) {
    if (!g_mock_initialized) {
        printf("模拟数据生成器未初始化\n");
        return;
    }
    
    const char* state_names[] = {"停止", "运行", "暂停"};
    const mock_test_scenario_t *scenario = &g_test_scenarios[g_mock_generator.current_scenario];
    uint32_t elapsed = mock_get_current_time() - g_mock_generator.scenario_start_time;
    
    printf("=== 模拟数据生成器状态 ===\n");
    printf("状态: %s\n", state_names[g_mock_generator.state]);
    printf("当前场景: %s\n", scenario->name);
    printf("场景进度: %d/%d ms\n", elapsed, scenario->duration_ms);
    printf("总更新次数: %d\n", g_mock_generator.total_updates);
    printf("错误次数: %d\n", g_mock_generator.error_count);
    printf("========================\n");
}

// =============================================================================
// 场景管理函数实现
// =============================================================================

uint32_t mock_get_scenario_count(void) {
    return MOCK_SCENARIO_COUNT;
}

const mock_test_scenario_t* mock_get_scenario_info(mock_scenario_type_t scenario_type) {
    if (scenario_type >= MOCK_SCENARIO_COUNT) {
        return NULL;
    }
    return &g_test_scenarios[scenario_type];
}

const char* mock_get_scenario_name(mock_scenario_type_t scenario_type) {
    if (scenario_type >= MOCK_SCENARIO_COUNT) {
        return "未知场景";
    }
    return g_test_scenarios[scenario_type].name;
}

// =============================================================================
// 数据注入函数实现
// =============================================================================

void mock_inject_battery_data(uint8_t level, bool is_charging) {
    printf("[MOCK] 注入电池数据: 电量=%d%%, 充电=%s\n", level, is_charging ? "是" : "否");

    int ret1 = vehicle_data_set_battery_level(level);
    int ret2 = vehicle_data_set_charging_state(is_charging);

    printf("[MOCK] 电池数据注入结果: 电量=%s, 充电=%s\n",
           ret1 == 0 ? "成功" : "失败", ret2 == 0 ? "成功" : "失败");

    if (ret1 == 0) {
        g_mock_statistics.battery_updates++;
        mock_update_statistics("battery");
    }

    if (ret2 == 0) {
        mock_update_statistics("battery");
    }
}

void mock_inject_speed_power_data(uint16_t speed, uint32_t power) {
    if (vehicle_data_set_speed(speed) == 0) {
        g_mock_statistics.speed_updates++;
        mock_update_statistics("running");
    }
    
    if (vehicle_data_set_power(power) == 0) {
        mock_update_statistics("running");
    }
}

void mock_inject_light_data(bool left_turn, bool right_turn, bool headlight, bool double_flash) {
    vehicle_data_set_left_turn(left_turn);
    vehicle_data_set_right_turn(right_turn);
    vehicle_data_set_headlight(headlight);
    vehicle_data_set_double_flash(double_flash);
    
    g_mock_statistics.light_updates++;
    mock_update_statistics("light");
}

void mock_inject_temperature_data(float temperature) {
    if (vehicle_data_set_temperature(temperature) == 0) {
        g_mock_statistics.temperature_updates++;
        mock_update_statistics("running");
    }
}

void mock_inject_time_data(uint8_t hour, uint8_t minute, uint8_t second) {
    if (vehicle_data_set_time(hour, minute, second) == 0) {
        g_mock_statistics.time_updates++;
        mock_update_statistics("time");
    }
}

void mock_inject_mileage_data(uint32_t total_mileage, uint32_t trip_mileage) {
    if (vehicle_data_set_mileage(total_mileage, trip_mileage) == 0) {
        mock_update_statistics("running");
    }
}

void mock_inject_cruise_data(bool enabled, uint16_t speed) {
    if (vehicle_data_set_cruise(enabled, speed) == 0) {
        mock_update_statistics("running");
    }
}

// =============================================================================
// 内部函数实现
// =============================================================================

static void mock_timer_callback(lv_timer_t *timer) {
    if (!g_mock_initialized || g_mock_generator.state != MOCK_STATE_RUNNING) {
        return;
    }

    const mock_test_scenario_t *scenario = &g_test_scenarios[g_mock_generator.current_scenario];
    uint32_t elapsed_ms = mock_get_current_time() - g_mock_generator.scenario_start_time;

    // 检查场景是否结束
    if (elapsed_ms >= scenario->duration_ms) {
        printf("场景 '%s' 执行完成，切换到下一个场景\n", scenario->name);
        g_mock_statistics.total_scenarios_run++;
        mock_data_generator_next_scenario();
        return;
    }

    // 执行当前场景
    if (scenario->execute) {
        scenario->execute(g_mock_generator.scenario_step, elapsed_ms);
        g_mock_generator.scenario_step++;
        g_mock_generator.total_updates++;
    }
}

static void mock_switch_to_scenario(uint32_t scenario_type) {
    if (scenario_type >= MOCK_SCENARIO_COUNT) {
        return;
    }

    g_mock_generator.current_scenario = scenario_type;
    g_mock_generator.scenario_step = 0;
    g_mock_generator.scenario_start_time = mock_get_current_time();

    const mock_test_scenario_t *scenario = &g_test_scenarios[scenario_type];
    printf("切换到场景: %s (持续时间: %d秒)\n", scenario->name, scenario->duration_ms / 1000);
}

static void mock_update_statistics(const char* data_type) {
    g_mock_statistics.total_data_updates++;

    // 这里可以根据需要添加更详细的统计
    (void)data_type; // 避免未使用警告
}

static uint32_t mock_get_current_time(void) {
    return lv_tick_get();
}

// =============================================================================
// 场景执行函数实现
// =============================================================================

static void mock_scenario_battery(uint32_t step, uint32_t elapsed_ms) {
    // 场景1：电池电量变化 (30秒)
    // 0-10s:  电量100% → 50%，无充电
    // 10-20s: 电量50% → 20%，无充电
    // 20-25s: 电量20%，开始充电（闪烁效果）
    // 25-30s: 电量20% → 80%，充电中

    printf("[MOCK] 执行电池场景: 步骤=%d, 时间=%dms\n", step, elapsed_ms);

    uint8_t battery_level;
    bool is_charging = false;

    if (elapsed_ms < 10000) {
        // 0-10s: 100% → 50%
        battery_level = 100 - (elapsed_ms * 50 / 10000);
    } else if (elapsed_ms < 20000) {
        // 10-20s: 50% → 20%
        battery_level = 50 - ((elapsed_ms - 10000) * 30 / 10000);
    } else if (elapsed_ms < 25000) {
        // 20-25s: 保持20%，开始充电
        battery_level = 20;
        is_charging = true;
    } else {
        // 25-30s: 20% → 80%，充电中
        battery_level = 20 + ((elapsed_ms - 25000) * 60 / 5000);
        is_charging = true;
    }

    mock_inject_battery_data(battery_level, is_charging);
}

static void mock_scenario_speed_power(uint32_t step, uint32_t elapsed_ms) {
    // 场景2：速度和功率变化 (40秒)
    // 0-10s:  速度0 → 30km/h，功率0 → 500W
    // 10-20s: 速度30 → 60km/h，功率500 → 1200W
    // 20-30s: 速度60 → 30km/h，功率1200 → 600W
    // 30-40s: 速度30 → 0km/h，功率600 → 0W

    uint16_t speed;
    uint32_t power;

    if (elapsed_ms < 10000) {
        // 0-10s: 加速阶段1
        speed = elapsed_ms * 30 / 10000;
        power = elapsed_ms * 500 / 10000;
    } else if (elapsed_ms < 20000) {
        // 10-20s: 加速阶段2
        speed = 30 + ((elapsed_ms - 10000) * 30 / 10000);
        power = 500 + ((elapsed_ms - 10000) * 700 / 10000);
    } else if (elapsed_ms < 30000) {
        // 20-30s: 减速阶段1
        speed = 60 - ((elapsed_ms - 20000) * 30 / 10000);
        power = 1200 - ((elapsed_ms - 20000) * 600 / 10000);
    } else {
        // 30-40s: 减速阶段2
        speed = 30 - ((elapsed_ms - 30000) * 30 / 10000);
        power = 600 - ((elapsed_ms - 30000) * 600 / 10000);
    }

    mock_inject_speed_power_data(speed, power);
}

static void mock_scenario_lights(uint32_t step, uint32_t elapsed_ms) {
    // 场景3：灯光状态变化 (25秒)
    // 0-5s:   左转灯闪烁
    // 5-10s:  右转灯闪烁
    // 10-15s: 双闪灯闪烁
    // 15-20s: 前灯开启
    // 20-25s: 所有灯光关闭

    bool left_turn = false;
    bool right_turn = false;
    bool headlight = false;
    bool double_flash = false;

    // 闪烁效果：每500ms切换一次
    bool blink_state = (elapsed_ms % 1000) < 500;

    if (elapsed_ms < 5000) {
        // 左转灯闪烁
        left_turn = blink_state;
    } else if (elapsed_ms < 10000) {
        // 右转灯闪烁
        right_turn = blink_state;
    } else if (elapsed_ms < 15000) {
        // 双闪灯闪烁
        double_flash = blink_state;
    } else if (elapsed_ms < 20000) {
        // 前灯开启
        headlight = true;
    }
    // 20-25s: 所有灯光关闭（默认值）

    mock_inject_light_data(left_turn, right_turn, headlight, double_flash);
}

static void mock_scenario_temperature_time(uint32_t step, uint32_t elapsed_ms) {
    // 场景4：温度和时间变化 (20秒)
    // 0-10s:  温度-5°C → 25°C
    // 10-20s: 温度25°C → 45°C
    // 同时:   时间快速变化（每秒+1分钟）

    float temperature;

    if (elapsed_ms < 10000) {
        // 0-10s: -5°C → 25°C
        temperature = -5.0f + (elapsed_ms * 30.0f / 10000);
    } else {
        // 10-20s: 25°C → 45°C
        temperature = 25.0f + ((elapsed_ms - 10000) * 20.0f / 10000);
    }

    // 时间快速变化：每秒+1分钟
    uint32_t total_minutes = elapsed_ms / 1000;
    uint8_t hour = (12 + total_minutes / 60) % 24;
    uint8_t minute = total_minutes % 60;
    uint8_t second = (elapsed_ms / 100) % 60;

    mock_inject_temperature_data(temperature);
    mock_inject_time_data(hour, minute, second);
}

static void mock_scenario_comprehensive(uint32_t step, uint32_t elapsed_ms) {
    // 场景5：综合测试 (60秒)
    // 模拟真实驾驶场景的数据变化

    // 电池：缓慢下降，偶尔充电
    uint8_t battery_level = 100 - (elapsed_ms / 2000);  // 每2秒下降1%
    bool is_charging = (elapsed_ms % 15000) < 3000;     // 每15秒充电3秒

    // 速度：正弦波变化，模拟加减速
    float speed_factor = sin(elapsed_ms * 3.14159f / 15000);  // 15秒周期
    uint16_t speed = (uint16_t)(30 + speed_factor * 25);      // 5-55 km/h

    // 功率：与速度相关
    uint32_t power = speed * 15 + (speed * speed / 10);

    // 温度：缓慢上升
    float temperature = 20.0f + (elapsed_ms * 15.0f / 60000);  // 20°C → 35°C

    // 灯光：随机闪烁
    bool left_turn = (elapsed_ms % 3000) < 500;
    bool right_turn = (elapsed_ms % 4000) < 500;
    bool headlight = elapsed_ms > 10000;  // 10秒后开启前灯
    bool double_flash = (elapsed_ms % 8000) < 1000;  // 偶尔双闪

    // 时间：正常流逝
    uint32_t total_seconds = elapsed_ms / 1000;
    uint8_t hour = (14 + total_seconds / 3600) % 24;
    uint8_t minute = (total_seconds / 60) % 60;
    uint8_t second = total_seconds % 60;

    // 里程：缓慢增加
    uint32_t total_mileage = 12345 + elapsed_ms / 10000;  // 每10秒增加1km
    uint32_t trip_mileage = elapsed_ms / 30000;           // 每30秒增加1km

    // 巡航：偶尔启用
    bool cruise_enabled = (elapsed_ms % 20000) < 8000;   // 每20秒启用8秒
    uint16_t cruise_speed = 50;

    // 注入所有数据
    mock_inject_battery_data(battery_level, is_charging);
    mock_inject_speed_power_data(speed, power);
    mock_inject_light_data(left_turn, right_turn, headlight, double_flash);
    mock_inject_temperature_data(temperature);
    mock_inject_time_data(hour, minute, second);
    mock_inject_mileage_data(total_mileage, trip_mileage);
    mock_inject_cruise_data(cruise_enabled, cruise_speed);
}

#else // !ENABLE_MOCK_DATA

// 空实现，当禁用模拟数据时
int mock_data_generator_init(void) { return 0; }
void mock_data_generator_deinit(void) {}
int mock_data_generator_start(int scenario_type) { (void)scenario_type; return 0; }
void mock_data_generator_stop(void) {}
void mock_data_generator_pause(void) {}
void mock_data_generator_resume(void) {}
int mock_data_generator_next_scenario(void) { return 0; }
mock_generator_state_t mock_data_generator_get_state(void) { return MOCK_STATE_STOPPED; }
int mock_data_generator_get_current_scenario(uint32_t *scenario_type, uint32_t *elapsed_ms) {
    (void)scenario_type; (void)elapsed_ms; return -1;
}
const mock_data_statistics_t* mock_data_generator_get_statistics(void) { return NULL; }
void mock_data_generator_reset_statistics(void) {}
void mock_data_generator_print_status(void) {}
uint32_t mock_get_scenario_count(void) { return 0; }
const mock_test_scenario_t* mock_get_scenario_info(mock_scenario_type_t scenario_type) {
    (void)scenario_type; return NULL;
}
const char* mock_get_scenario_name(mock_scenario_type_t scenario_type) {
    (void)scenario_type; return "禁用";
}

#endif // ENABLE_MOCK_DATA
