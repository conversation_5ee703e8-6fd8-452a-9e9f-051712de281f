/**
 * @file ui_component_manager.h
 * @brief UI组件管理器 - 统一管理dashboard UI组件访问
 * @version 1.0.0
 * @date 2025-07-22
 */

#ifndef UI_COMPONENT_MANAGER_H
#define UI_COMPONENT_MANAGER_H

#include "../../lvgl/lvgl.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// =============================================================================
// 规范化的组件ID定义
// =============================================================================

// 状态栏组件ID
#define DASHBOARD_COMP_STATUSBAR_TEMPERATURE    UI_COMP_STATUSBAR2_LEFT_REAL_STA_TEMP
#define DASHBOARD_COMP_STATUSBAR_TIME          UI_COMP_STATUSBAR2_LEFT_REAL_STA_TIME
#define DASHBOARD_COMP_STATUSBAR_POWER         UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_POWER_POWER_LABEL
#define DASHBOARD_COMP_STATUSBAR_TRIP          UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_TRIP_TRIP_INFO
#define DASHBOARD_COMP_STATUSBAR_BATTERY_ICON  UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY_BAT_ICON
#define DASHBOARD_COMP_STATUSBAR_BATTERY_LABEL UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY_BAT_LABEL
#define DASHBOARD_COMP_STATUSBAR_ODOMETER      UI_COMP_STATUSBAR2_RIGHT_ODO_LABEL

// 主页组件ID
#define DASHBOARD_COMP_HOME_BATTERY_ICON       UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO_BATTERY_ICON
#define DASHBOARD_COMP_HOME_BATTERY_LABEL      UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO_BATTERY_LABEL
#define DASHBOARD_COMP_HOME_BATTERY_BAR        UI_COMP_HOME_HOME_RIGHT_BATTERY_VAL
#define DASHBOARD_COMP_HOME_SPEED_VALUE        UI_COMP_HOME_HOME_RIGHT_RUN_SPEED_STA_RUN_SPEED_VALUE

// 灯光组件ID
#define DASHBOARD_COMP_HOME_LIGHT_LEFT         UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_L_STA_MAIN_RUN1
#define DASHBOARD_COMP_HOME_LIGHT_RIGHT        UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_R_STA_ICON_R_6
#define DASHBOARD_COMP_HOME_LIGHT_DOUBLE_FLASH UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FD_STA_ICON_R_8
#define DASHBOARD_COMP_HOME_LIGHT_FRONT        UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_F_STA_ICON_R_9
#define DASHBOARD_COMP_HOME_LIGHT_BRAKE        UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FE_STA_ICON_R_10

// 姿态组件ID
#define DASHBOARD_COMP_HOME_PITCH_ANGLE        UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_PITCH_ANGLE
#define DASHBOARD_COMP_HOME_ROLL_ANGLE         UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_ROLL_ANGLE
#define DASHBOARD_COMP_HOME_ACCELERATION       UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_ACCELERATION

// 巡航组件ID
#define DASHBOARD_COMP_HOME_CRUISE_PARENT      UI_COMP_HOME_HOME_RIGHT_RUN_NOS
#define DASHBOARD_COMP_HOME_CRUISE_ICON        UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_ICON_STA_MAIN_NOS_ICON
#define DASHBOARD_COMP_HOME_CRUISE_SPEED       UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_SPEED_BG_STA_NOS_SPEED_VALUE

// 设置页面组件ID
#define DASHBOARD_COMP_SETTING_DATE            UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_RIGHT_LIST_ITEM_SUB
#define DASHBOARD_COMP_SETTING_TOTAL_MILEAGE   UI_COMP_SETTINGINFO_INFO_LEFT_INFO_LEFT_LABEL_VAL
#define DASHBOARD_COMP_SETTING_SUB_MILEAGE     UI_COMP_SETTINGINFO_INFO_RIGHT_INFO_RIGHT_LABEL_VAL
#define DASHBOARD_COMP_SETTING_SW_VERSION      UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL
#define DASHBOARD_COMP_SETTING_HW_VERSION      UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL

// =============================================================================
// 数据结构定义
// =============================================================================

/**
 * @brief UI组件描述结构体
 */
typedef struct {
    lv_obj_t *parent;        /**< 父组件对象 */
    uint32_t component_id;   /**< 组件ID */
    lv_obj_t *cached_obj;    /**< 缓存的组件对象 */
    bool is_cached;          /**< 是否已缓存 */
    const char *name;        /**< 组件名称（用于调试） */
} ui_component_t;

/**
 * @brief Dashboard UI组件集合
 */
typedef struct {
    // 状态栏组件
    ui_component_t statusbar_temperature;
    ui_component_t statusbar_time;
    ui_component_t statusbar_power;
    ui_component_t statusbar_trip;
    ui_component_t statusbar_battery_icon;
    ui_component_t statusbar_battery_label;
    ui_component_t statusbar_odometer;
    
    // 主页电池组件
    ui_component_t home_battery_icon;
    ui_component_t home_battery_label;
    ui_component_t home_battery_bar;
    
    // 主页运行组件
    ui_component_t home_speed_value;
    ui_component_t home_cruise_parent;
    ui_component_t home_cruise_icon;
    ui_component_t home_cruise_speed;
    
    // 灯光组件
    ui_component_t home_light_left;
    ui_component_t home_light_right;
    ui_component_t home_light_double_flash;
    ui_component_t home_light_front;
    ui_component_t home_light_brake;
    
    // 姿态组件
    ui_component_t home_pitch_angle;
    ui_component_t home_roll_angle;
    ui_component_t home_acceleration;
    
    // 设置页面组件
    ui_component_t setting_date;
    ui_component_t setting_total_mileage;
    ui_component_t setting_sub_mileage;
    ui_component_t setting_sw_version;
    ui_component_t setting_hw_version;
} dashboard_ui_components_t;

/**
 * @brief 组件更新回调函数类型
 * @param component 组件对象
 * @param user_data 用户数据
 */
typedef void (*ui_component_update_callback_t)(lv_obj_t *component, void *user_data);

// =============================================================================
// 公共接口函数
// =============================================================================

/**
 * @brief 初始化UI组件管理器
 * @return 0 成功，-1 失败
 */
int ui_component_manager_init(void);

/**
 * @brief 清理UI组件管理器
 */
void ui_component_manager_deinit(void);

/**
 * @brief 获取Dashboard UI组件集合
 * @return 组件集合指针（只读）
 */
const dashboard_ui_components_t* ui_component_manager_get_components(void);

/**
 * @brief 获取指定的UI组件对象
 * @param component 组件描述结构体
 * @return 组件对象指针，失败返回NULL
 */
lv_obj_t* ui_component_get_object(ui_component_t *component);

/**
 * @brief 缓存所有UI组件
 * @return 0 成功，-1 失败
 */
int ui_component_cache_all(void);

/**
 * @brief 清除所有组件缓存
 */
void ui_component_clear_cache(void);

/**
 * @brief 检查组件是否有效
 * @param component 组件描述结构体
 * @return true 有效，false 无效
 */
bool ui_component_is_valid(const ui_component_t *component);

// =============================================================================
// 便捷访问宏定义
// =============================================================================

#define UI_GET_COMPONENT(comp_name) \
    ui_component_get_object(&(ui_component_manager_get_components()->comp_name))

#define UI_UPDATE_LABEL(comp_name, text) \
    do { \
        lv_obj_t *obj = UI_GET_COMPONENT(comp_name); \
        UI_COMP_MGR_DEBUG_PRINT("更新标签 %s: 对象=%p, 文本=%s", #comp_name, obj, text); \
        if (obj) { \
            lv_label_set_text(obj, text); \
            UI_COMP_MGR_DEBUG_PRINT("标签 %s 更新成功\n", #comp_name); \
        } else { \
            UI_COMP_MGR_DEBUG_PRINT("标签 %s 更新失败: 对象为空", #comp_name); \
        } \
    } while(0)

#define UI_UPDATE_BAR(comp_name, value) \
    do { \
        lv_obj_t *obj = UI_GET_COMPONENT(comp_name); \
        UI_COMP_MGR_DEBUG_PRINT("更新进度条 %s: 对象=%p, 值=%d", #comp_name, obj, value); \
        if (obj) { \
            lv_bar_set_value(obj, value, LV_ANIM_OFF); \
            UI_COMP_MGR_DEBUG_PRINT("进度条 %s 更新成功", #comp_name); \
        } else { \
            UI_COMP_MGR_DEBUG_PRINT("进度条 %s 更新失败: 对象为空", #comp_name); \
        } \
    } while(0)

#define UI_SET_HIDDEN(comp_name, hidden) \
    do { \
        lv_obj_t *obj = UI_GET_COMPONENT(comp_name); \
        if (obj) { \
            if (hidden) lv_obj_add_flag(obj, LV_OBJ_FLAG_HIDDEN); \
            else lv_obj_clear_flag(obj, LV_OBJ_FLAG_HIDDEN); \
        } \
    } while(0)

#define UI_SET_BLINK(comp_name, blink_condition) \
    UI_SET_HIDDEN(comp_name, !(blink_condition))

// =============================================================================
// 调试和诊断函数
// =============================================================================

/**
 * @brief 打印所有组件状态
 */
void ui_component_print_status(void);

/**
 * @brief 验证所有组件的有效性
 * @return 有效组件数量
 */
int ui_component_validate_all(void);

/**
 * @brief 获取组件统计信息
 * @param total_components 总组件数
 * @param cached_components 已缓存组件数
 * @param valid_components 有效组件数
 */
void ui_component_get_stats(int *total_components, int *cached_components, int *valid_components);

// =============================================================================
// 调试宏
// =============================================================================

#ifdef UI_COMP_MGR_DEBUG
#include <stdio.h>
#define UI_COMP_MGR_DEBUG_PRINT(fmt, ...) \
    printf("[UI_COMP_MGR_DEBUG] " fmt "\n", ##__VA_ARGS__)
#else
#define UI_COMP_MGR_DEBUG_PRINT(fmt, ...)
#endif

#ifdef __cplusplus
}
#endif

#endif // UI_COMPONENT_MANAGER_H
