/**
 * @file dashboard.c
 * @brief 仪表盘组件 - 重构版本，使用数据驱动架构
 * @version 2.0.0
 * @date 2025-07-22
 */

#include "app/ebike_x1.h"
#include "ui_component_manager.h"
#include "data/vehicle_data.h"
#include "./data/mock_data_generator.h"
#include "../include/date_utils.h"
#include <stdio.h>
#include <string.h>

// =============================================================================
// 内部变量
// =============================================================================

static bool g_dashboard_initialized = false;
static lv_timer_t *g_dashboard_timer = NULL;
static uint32_t g_blink_counter = 0;

// =============================================================================
// 内部函数声明
// =============================================================================

static void dashboard_timer_callback(lv_timer_t *timer);
static void dashboard_data_change_callback(const char* data_type, const void* data, void* user_data);

// UI更新函数
static void dashboard_update_time(void);
static void dashboard_update_battery(void);
static void dashboard_update_speed(void);
static void dashboard_update_power(void);
static void dashboard_update_odometer(void);
static void dashboard_update_temperature(void);
static void dashboard_update_trip(void);
static void dashboard_update_lights(void);
static void dashboard_update_cruise(void);
static void dashboard_update_attitude(void);
static void dashboard_update_system_info(void);

// 辅助函数
static void dashboard_handle_blink_effects(void);
static void dashboard_format_time_string(char *buffer, size_t size, const vehicle_time_data_t *time_data);
static void dashboard_format_date_string(char *buffer, size_t size, const vehicle_time_data_t *time_data);

// =============================================================================
// 公共接口函数
// =============================================================================

int dashboard_init(void) {
    if (g_dashboard_initialized) {
        return 0; // 已经初始化
    }

    // 初始化车辆数据管理模块
    if (vehicle_data_init() != 0) {
        printf("仪表盘初始化失败: 车辆数据管理模块初始化失败\n");
        return -1;
    }

    // 初始化UI组件管理器
    if (ui_component_manager_init() != 0) {
        printf("仪表盘初始化失败: UI组件管理器初始化失败\n");
        return -1;
    }
    
    // 缓存所有UI组件
    if (ui_component_cache_all() != 0) {
        printf("仪表盘初始化警告: 部分UI组件缓存失败\n");
    }
    
    // 注册数据变化回调
    vehicle_data_subscribe("battery", dashboard_data_change_callback, NULL);
    vehicle_data_subscribe("running", dashboard_data_change_callback, NULL);
    vehicle_data_subscribe("light", dashboard_data_change_callback, NULL);
    vehicle_data_subscribe("attitude", dashboard_data_change_callback, NULL);
    vehicle_data_subscribe("time", dashboard_data_change_callback, NULL);
    vehicle_data_subscribe("system", dashboard_data_change_callback, NULL);
    
    // 创建定时器（100ms周期）
    g_dashboard_timer = lv_timer_create(dashboard_timer_callback, 100, NULL);
    if (!g_dashboard_timer) {
        printf("仪表盘初始化失败: 定时器创建失败\n");
        return -1;
    }
    
    // 初始化所有UI显示
    dashboard_update_time();
    dashboard_update_battery();
    dashboard_update_speed();
    dashboard_update_power();
    dashboard_update_odometer();
    dashboard_update_temperature();
    dashboard_update_trip();
    dashboard_update_lights();
    dashboard_update_cruise();
    dashboard_update_attitude();
    dashboard_update_system_info();

    // 初始化模拟数据生成器（用于测试）
    if (MOCK_INIT() == 0) {
        printf("模拟数据生成器初始化成功\n");
        // 启动综合测试场景（循环所有场景）
        if (MOCK_START(-1) == 0) {
            printf("模拟数据生成器已启动，开始UI响应测试\n");
        }
    }

    g_dashboard_initialized = true;
    printf("仪表盘初始化完成\n");
    return 0;
}

void dashboard_deinit(void) {
    if (!g_dashboard_initialized) {
        return;
    }
    
    // 删除定时器
    if (g_dashboard_timer) {
        lv_timer_del(g_dashboard_timer);
        g_dashboard_timer = NULL;
    }
    
    // 取消数据订阅
    vehicle_data_unsubscribe("battery", dashboard_data_change_callback);
    vehicle_data_unsubscribe("running", dashboard_data_change_callback);
    vehicle_data_unsubscribe("light", dashboard_data_change_callback);
    vehicle_data_unsubscribe("attitude", dashboard_data_change_callback);
    vehicle_data_unsubscribe("time", dashboard_data_change_callback);
    vehicle_data_unsubscribe("system", dashboard_data_change_callback);
    
    // 清理模拟数据生成器
    MOCK_STOP();
    MOCK_DEINIT();

    // 清理UI组件管理器
    ui_component_manager_deinit();

    // 清理车辆数据管理模块
    vehicle_data_deinit();

    g_dashboard_initialized = false;
    printf("仪表盘已清理\n");
}

// =============================================================================
// 定时器回调函数
// =============================================================================

static void dashboard_timer_callback(lv_timer_t *timer) {
    if (!g_dashboard_initialized) {
        return;
    }
    
    g_blink_counter++;
    
    // 处理闪烁效果（每500ms切换一次）
    if (g_blink_counter % 5 == 0) {
        dashboard_handle_blink_effects();
    }
    
    // 定期更新时间显示（每秒更新一次）
    if (g_blink_counter % 10 == 0) {
      if (!date_get_time(&g_date_time)) {
        // 更新时间
        vehicle_data_set_date(g_date_time.year, g_date_time.month, g_date_time.day);
        vehicle_data_set_time(g_date_time.hour, g_date_time.minute, g_date_time.second);
      }
      dashboard_update_time();
    }
}

// =============================================================================
// 数据变化回调函数
// =============================================================================

static void dashboard_data_change_callback(const char* data_type, const void* data, void* user_data) {
    printf("[DASHBOARD] 收到数据变化回调: 类型=%s, 初始化状态=%s\n",
           data_type ? data_type : "NULL", g_dashboard_initialized ? "已初始化" : "未初始化");

    if (!g_dashboard_initialized || !data_type) {
        printf("[DASHBOARD] 回调被忽略: %s\n",
               !g_dashboard_initialized ? "dashboard未初始化" : "数据类型为空");
        return;
    }

    // 根据数据类型触发相应的UI更新
    if (strcmp(data_type, "battery") == 0) {
        printf("[DASHBOARD] 更新电池UI\n");
        dashboard_update_battery();
    } else if (strcmp(data_type, "running") == 0) {
        printf("[DASHBOARD] 更新运行数据UI\n");
        dashboard_update_speed();
        dashboard_update_power();
        dashboard_update_odometer();
        dashboard_update_temperature();
        dashboard_update_trip();
        dashboard_update_cruise();
    } else if (strcmp(data_type, "light") == 0) {
        printf("[DASHBOARD] 更新灯光UI\n");
        dashboard_update_lights();
    } else if (strcmp(data_type, "attitude") == 0) {
        printf("[DASHBOARD] 更新姿态UI\n");
        dashboard_update_attitude();
    } else if (strcmp(data_type, "time") == 0) {
        printf("[DASHBOARD] 更新时间UI\n");
        dashboard_update_time();
    } else if (strcmp(data_type, "system") == 0) {
        printf("[DASHBOARD] 更新系统信息UI\n");
        dashboard_update_system_info();
    }

    printf("[DASHBOARD] 仪表盘响应数据变化完成: %s\n", data_type);
}

// =============================================================================
// UI更新函数实现
// =============================================================================

static void dashboard_update_time(void) {
    const vehicle_time_data_t* time_data = vehicle_data_get_time();
    if (!time_data) {
        return;
    }
    
    char time_str[16];
    char date_str[32];
    
    // 格式化时间和日期字符串
    dashboard_format_time_string(time_str, sizeof(time_str), time_data);
    dashboard_format_date_string(date_str, sizeof(date_str), time_data);
    
    // 更新状态栏时间
    UI_UPDATE_LABEL(statusbar_time, time_str);
    
    // 更新设置页面日期
    UI_UPDATE_LABEL(setting_date, date_str);
}

static void dashboard_update_battery(void) {
    printf("[DASHBOARD] 开始更新电池UI\n");

    const vehicle_battery_data_t* battery_data = vehicle_data_get_battery();
    if (!battery_data) {
        printf("[DASHBOARD] 获取电池数据失败\n");
        return;
    }

    printf("[DASHBOARD] 电池数据: 电量=%d%%, 充电=%s\n",
           battery_data->level, battery_data->is_charging ? "是" : "否");

    char battery_str[8];
    snprintf(battery_str, sizeof(battery_str), "%d%%", battery_data->level);

    // 更新状态栏电池
    printf("[DASHBOARD] 更新状态栏电池标签: %s\n", battery_str);
    UI_UPDATE_LABEL(statusbar_battery_label, battery_str);

    // 更新主页电池
    printf("[DASHBOARD] 更新主页电池标签和进度条: %s, %d%%\n", battery_str, battery_data->level);
    UI_UPDATE_LABEL(home_battery_label, battery_str);
    UI_UPDATE_BAR(home_battery_bar, battery_data->level);

    printf("[DASHBOARD] 电池UI更新完成\n");
    // 充电状态闪烁效果在dashboard_handle_blink_effects中处理
}

static void dashboard_update_speed(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }
    
    char speed_str[8];
    snprintf(speed_str, sizeof(speed_str), "%d", running_data->speed);
    
    // 更新主页速度显示
    UI_UPDATE_LABEL(home_speed_value, speed_str);
}

static void dashboard_update_power(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }
    
    // 使用国际化文本格式化功率显示
    lv_obj_t *power_label = UI_GET_COMPONENT(statusbar_power);
    if (power_label) {
        lv_label_set_text(power_label, 
            lv_i18n_get_text_fmt("POWER", running_data->power_current, running_data->power_average));
    }
}

static void dashboard_update_odometer(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }
    
    // 更新状态栏里程
    lv_obj_t *odo_label = UI_GET_COMPONENT(statusbar_odometer);
    if (odo_label) {
        lv_label_set_text(odo_label, 
            lv_i18n_get_text_fmt("STA_ODO", running_data->total_mileage));
    }
    
    // 更新设置页面里程
    lv_obj_t *total_label = UI_GET_COMPONENT(setting_total_mileage);
    if (total_label) {
        lv_i18n_set_text_fmt(total_label, "ODO", (float)running_data->total_mileage);
    }
    
    lv_obj_t *sub_label = UI_GET_COMPONENT(setting_sub_mileage);
    if (sub_label) {
        lv_i18n_set_text_fmt(sub_label, "SUB_MILEAGE", (float)running_data->trip_mileage);
    }
}

static void dashboard_update_temperature(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }

    // 更新温度显示
    lv_obj_t *temp_label = UI_GET_COMPONENT(statusbar_temperature);
    if (temp_label) {
        lv_label_set_text(temp_label,
            lv_i18n_get_text_fmt("TEMPERATURE", running_data->temperature));
    }
}

static void dashboard_update_trip(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    const vehicle_time_data_t* time_data = vehicle_data_get_time();
    if (!running_data || !time_data) {
        return;
    }

    // 计算行程时间
    uint32_t trip_minutes = running_data->trip_time;
    uint32_t trip_hours = trip_minutes / 60;
    trip_minutes = trip_minutes % 60;

    // 更新行程显示
    lv_obj_t *trip_label = UI_GET_COMPONENT(statusbar_trip);
    if (trip_label) {
        lv_label_set_text(trip_label,
            lv_i18n_get_text_fmt("TRIP", trip_hours, trip_minutes, "h", running_data->trip_time));
    }
}

static void dashboard_update_lights(void) {
    const vehicle_light_data_t* light_data = vehicle_data_get_light();
    if (!light_data) {
        return;
    }

    // 灯光状态的闪烁效果在dashboard_handle_blink_effects中处理
    // 这里只更新基本的显示状态

    // 前灯状态
    UI_SET_HIDDEN(home_light_front, !light_data->headlight);

    // 刹车灯状态
    UI_SET_HIDDEN(home_light_brake, !light_data->brake_light);

    // 双闪灯状态
    UI_SET_HIDDEN(home_light_double_flash, !light_data->double_flash);
}

static void dashboard_update_cruise(void) {
    const vehicle_running_data_t* running_data = vehicle_data_get_running();
    if (!running_data) {
        return;
    }

    // 巡航状态显示
    lv_obj_t *cruise_parent = UI_GET_COMPONENT(home_cruise_parent);
    if (cruise_parent) {
        if (running_data->cruise_enabled) {
            lv_obj_set_height(cruise_parent, 90);

            // 更新巡航速度
            char cruise_speed_str[8];
            snprintf(cruise_speed_str, sizeof(cruise_speed_str), "%02d", running_data->cruise_speed);
            UI_UPDATE_LABEL(home_cruise_speed, cruise_speed_str);
        } else {
            lv_obj_set_height(cruise_parent, 0);
        }
    }

    // 巡航图标的闪烁效果在dashboard_handle_blink_effects中处理
}

static void dashboard_update_attitude(void) {
    const vehicle_attitude_data_t* attitude_data = vehicle_data_get_attitude();
    if (!attitude_data) {
        return;
    }

    // 更新车身姿态显示
    lv_obj_t *pitch_label = UI_GET_COMPONENT(home_pitch_angle);
    if (pitch_label) {
        lv_label_set_text(pitch_label,
            lv_i18n_get_text_fmt("PITCH_ANGLE", attitude_data->pitch));
    }

    lv_obj_t *roll_label = UI_GET_COMPONENT(home_roll_angle);
    if (roll_label) {
        lv_label_set_text(roll_label,
            lv_i18n_get_text_fmt("ROLL_ANGLE", attitude_data->roll));
    }

    lv_obj_t *acc_label = UI_GET_COMPONENT(home_acceleration);
    if (acc_label) {
        lv_label_set_text(acc_label,
            lv_i18n_get_text_fmt("ACCELERATION", attitude_data->acceleration));
    }
}

static void dashboard_update_system_info(void) {
    const vehicle_system_data_t* system_data = vehicle_data_get_system();
    if (!system_data) {
        return;
    }

    // 更新软件版本
    lv_obj_t *sw_label = UI_GET_COMPONENT(setting_sw_version);
    if (sw_label) {
        if (strlen(system_data->sw_version) > 0) {
            lv_i18n_set_text_fmt(sw_label, "SW_INFO", system_data->sw_version);
        } else {
            lv_label_set_text(sw_label, "--");
        }
    }

    // 更新硬件版本
    lv_obj_t *hw_label = UI_GET_COMPONENT(setting_hw_version);
    if (hw_label) {
        if (strlen(system_data->hw_version) > 0) {
            lv_i18n_set_text_fmt(hw_label, "HW_INFO", system_data->hw_version);
        } else {
            lv_label_set_text(hw_label, "--");
        }
    }
}

// =============================================================================
// 辅助函数实现
// =============================================================================

static void dashboard_handle_blink_effects(void) {
    const vehicle_battery_data_t* battery_data = vehicle_data_get_battery();
    const vehicle_light_data_t* light_data = vehicle_data_get_light();
    const vehicle_running_data_t* running_data = vehicle_data_get_running();

    bool blink_state = (g_blink_counter % 10) < 5; // 500ms周期闪烁

    // 充电状态闪烁
    if (battery_data && battery_data->is_charging) {
        UI_SET_HIDDEN(statusbar_battery_icon, !blink_state);
        UI_SET_HIDDEN(home_battery_icon, !blink_state);
    } else {
        UI_SET_HIDDEN(statusbar_battery_icon, false);
        UI_SET_HIDDEN(home_battery_icon, false);
    }

    // 转向灯闪烁
    if (light_data) {
        if (light_data->left_turn) {
            UI_SET_HIDDEN(home_light_left, !blink_state);
        } else {
            UI_SET_HIDDEN(home_light_left, true);
        }

        if (light_data->right_turn) {
            UI_SET_HIDDEN(home_light_right, !blink_state);
        } else {
            UI_SET_HIDDEN(home_light_right, true);
        }
    }

    // 巡航图标闪烁
    if (running_data && running_data->cruise_enabled) {
        UI_SET_HIDDEN(home_cruise_icon, !blink_state);
    } else {
        UI_SET_HIDDEN(home_cruise_icon, true);
    }
}

static void dashboard_format_time_string(char *buffer, size_t size, const vehicle_time_data_t *time_data) {
    if (!buffer || !time_data) {
        return;
    }

    snprintf(buffer, size, "%02d:%02d", time_data->hour, time_data->minute);
}

static void dashboard_format_date_string(char *buffer, size_t size, const vehicle_time_data_t *time_data) {
    if (!buffer || !time_data) {
        return;
    }

    snprintf(buffer, size, "%04d-%02d-%02d %02d:%02d:%02d",
             time_data->year, time_data->month, time_data->day,
             time_data->hour, time_data->minute, time_data->second);
}
