/**
 * @file wifi_utils.h
 * @brief WiFi工具函数头文件
 * @version 1.0.0
 * @date 2025-06-06
 */

#ifndef WIFI_UTILS_H
#define WIFI_UTILS_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief WiFi网络信息结构体
 */
typedef struct {
    char ssid[33];        /**< SSID最大32字符 + 结束符 */
    int signal_strength;  /**< 信号强度，单位dBm */
    char bssid[18];       /**< MAC地址格式 XX:XX:XX:XX:XX:XX + 结束符 */
    char encryption[20];  /**< 加密类型 */
} wifi_network_t;

/**
 * @brief WiFi状态结构体
 */
typedef struct {
    int connected;        /**< 1表示已连接，0表示未连接 */
    char ssid[33];        /**< 当前连接的SSID */
    int signal_strength;  /**< 当前信号强度 */
    char ip_address[16];  /**< IP地址 */
} wifi_status_t;

/**
 * @brief WiFi错误码枚举
 */
typedef enum {
    WIFI_ERROR_NONE = 0,         /**< 无错误 */
    WIFI_ERROR_INIT_FAILED,      /**< 初始化失败 */
    WIFI_ERROR_SCAN_FAILED,      /**< 扫描失败 */
    WIFI_ERROR_CONNECT_FAILED,   /**< 连接失败 */
    WIFI_ERROR_TIMEOUT,          /**< 操作超时 */
    WIFI_ERROR_INVALID_PARAM,    /**< 无效参数 */
    WIFI_ERROR_SYSTEM,           /**< 系统错误 */
    WIFI_ERROR_PERMISSION,       /**< 权限错误 */
    WIFI_ERROR_UNKNOWN           /**< 未知错误 */
} wifi_error_t;

/**
 * @brief 初始化WiFi接口
 * @param[in] interface 接口名称，如"wlan0"
 * @return 成功返回WIFI_ERROR_NONE，失败返回对应错误码
 */
wifi_error_t wifi_init(const char *interface);

/**
 * @brief 扫描WiFi网络
 * @return 成功返回WIFI_ERROR_NONE，失败返回对应错误码
 */
wifi_error_t wifi_scan(void);

/**
 * @brief 获取WiFi扫描结果
 * @param[out] networks 网络列表数组
 * @param[in] size 数组大小
 * @param[out] count 实际扫描到的网络数量
 * @return 成功返回WIFI_ERROR_NONE，失败返回对应错误码
 */
wifi_error_t wifi_get_scan_results(wifi_network_t *networks, int size, int *count);

/**
 * @brief 连接WiFi网络
 * @param[in] ssid 网络SSID
 * @param[in] password 网络密码
 * @param[in] encryption 加密类型，如"WPA-PSK"、"WPA2-PSK"等
 * @return 成功返回WIFI_ERROR_NONE，失败返回对应错误码
 */
wifi_error_t wifi_connect(const char *ssid, const char *password, const char *encryption);

/**
 * @brief 断开WiFi连接
 * @return 成功返回WIFI_ERROR_NONE，失败返回对应错误码
 */
wifi_error_t wifi_disconnect(void);

/**
 * @brief 获取当前WiFi连接状态
 * @param[out] status 状态结构体指针
 * @return 成功返回WIFI_ERROR_NONE，失败返回对应错误码
 */
wifi_error_t wifi_get_status(wifi_status_t *status);

/**
 * @brief 保存WiFi配置
 * @return 成功返回WIFI_ERROR_NONE，失败返回对应错误码
 */
wifi_error_t wifi_save_config(void);

/**
 * @brief 清理WiFi资源
 * @return 成功返回WIFI_ERROR_NONE，失败返回对应错误码
 */
wifi_error_t wifi_cleanup(void);

#ifdef __cplusplus
}
#endif

#endif /* WIFI_UTILS_H */ 