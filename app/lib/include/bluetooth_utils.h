/**
 * @file bluetooth_utils.h
 * @brief 蓝牙工具函数头文件
 * @version 1.0.0
 * @date 2025-06-07
 */

#ifndef BLUETOOTH_UTILS_H
#define BLUETOOTH_UTILS_H

#include <stdint.h>

/**
 * @brief 蓝牙设备信息结构体
 */
typedef struct {
    char address[18];        /**< 蓝牙MAC地址 */
    char name[64];           /**< 设备名称 */
    int rssi;                /**< 信号强度 */
    int paired;              /**< 是否已配对 */
    int connected;           /**< 是否已连接 */
    int trusted;             /**< 是否已信任 */
} bluetooth_device_t;

/**
 * @brief 蓝牙错误码
 */
typedef enum {
    BT_ERROR_NONE = 0,           /**< 成功 */
    BT_ERROR_INIT_FAILED,        /**< 初始化失败 */
    BT_ERROR_INVALID_PARAM,      /**< 无效参数 */
    BT_ERROR_NOT_INITIALIZED,    /**< 未初始化 */
    BT_ERROR_TIMEOUT,            /**< 操作超时 */
    BT_ERROR_SCAN_FAILED,        /**< 扫描失败 */
    BT_ERROR_PAIR_FAILED,        /**< 配对失败 */
    BT_ERROR_CONNECT_FAILED,     /**< 连接失败 */
    BT_ERROR_SYSTEM,             /**< 系统错误 */
    BT_ERROR_PERMISSION,         /**< 权限错误 */
    BT_ERROR_DEVICE_NOT_FOUND    /**< 设备未找到 */
} bluetooth_error_t;

/**
 * @brief 初始化蓝牙
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_init(void);

/**
 * @brief 开启蓝牙
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_power_on(void);

/**
 * @brief 关闭蓝牙
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_power_off(void);

/**
 * @brief 设置蓝牙可发现模式
 * @param[in] discoverable 1表示开启，0表示关闭
 * @param[in] timeout 可发现超时时间（秒），0表示永不超时
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_set_discoverable(int discoverable, int timeout);

/**
 * @brief 设置蓝牙可配对模式
 * @param[in] pairable 1表示开启，0表示关闭
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_set_pairable(int pairable);

/**
 * @brief 扫描蓝牙设备
 * @param[in] timeout 扫描超时时间（秒），0表示使用默认值
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_scan(int timeout);

/**
 * @brief 停止扫描蓝牙设备
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_scan_stop(void);

/**
 * @brief 获取扫描到的蓝牙设备列表
 * @param[out] devices 设备列表数组
 * @param[in] size 数组大小
 * @param[out] count 实际扫描到的设备数量
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_get_devices(bluetooth_device_t *devices, int size, int *count);

/**
 * @brief 获取已配对的蓝牙设备列表
 * @param[out] devices 设备列表数组
 * @param[in] size 数组大小
 * @param[out] count 实际配对的设备数量
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_get_paired_devices(bluetooth_device_t *devices, int size, int *count);

/**
 * @brief 与蓝牙设备配对
 * @param[in] address 设备MAC地址
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_pair(const char *address);

/**
 * @brief 取消与蓝牙设备的配对
 * @param[in] address 设备MAC地址
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_unpair(const char *address);

/**
 * @brief 连接蓝牙设备
 * @param[in] address 设备MAC地址
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_connect(const char *address);

/**
 * @brief 断开蓝牙设备连接
 * @param[in] address 设备MAC地址
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_disconnect(const char *address);

/**
 * @brief 信任蓝牙设备
 * @param[in] address 设备MAC地址
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_trust(const char *address);

/**
 * @brief 取消信任蓝牙设备
 * @param[in] address 设备MAC地址
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_untrust(const char *address);

/**
 * @brief 获取蓝牙设备信息
 * @param[in] address 设备MAC地址
 * @param[out] device 设备信息结构体
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_get_device_info(const char *address, bluetooth_device_t *device);

/**
 * @brief 获取蓝牙控制器信息
 * @param[out] controller_name 控制器名称缓冲区
 * @param[in] name_size 名称缓冲区大小
 * @param[out] address 地址缓冲区
 * @param[in] addr_size 地址缓冲区大小
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_get_controller_info(char *controller_name, int name_size, 
                                               char *address, int addr_size);

/**
 * @brief 设置自动接受配对请求
 * @param[in] auto_accept 1表示自动接受，0表示手动确认
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_set_auto_accept_pairing(int auto_accept);

/**
 * @brief 清理蓝牙资源
 * @return 成功返回BT_ERROR_NONE，失败返回对应错误码
 */
bluetooth_error_t bluetooth_cleanup(void);

/**
 * @brief 获取错误信息
 * @param[in] error 错误码
 * @return 错误描述字符串
 */
const char *bluetooth_error_to_string(bluetooth_error_t error);

#endif /* BLUETOOTH_UTILS_H */ 