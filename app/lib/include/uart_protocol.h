/**
 * @file uart_protocol.h
 * @brief UART串口通讯协议公共API头文件
 * @version 1.1.0
 * @date 2025-07-31 21:52
 */

#ifndef UART_PROTOCOL_H
#define UART_PROTOCOL_H

#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include "serial_utils.h"

// ====== 协议帧相关定义 ======
#define FRAME_HEADER_1    0xAA
#define FRAME_HEADER_2    0xBB
#define NEED_ACK          0x00
#define NO_ACK            0x01
#define ID_POWER_SYNC     0x01  /**< 上电同步信息 */
#define ID_UPGRADE        0x02  /**< 升级 */
#define ID_HEARTBEAT      0x03  /**< 心跳包 */
#define ID_FACTORY_RESET  0x04  /**< 工厂复位 */
#define ID_MCU_VERSION    0x05  /**< MCU版本号 */
#define ID_LIGHT_SCREEN_CTRL 0x56  /**< 灯光和屏幕控制 */
#define ID_SCREEN_CTRL    0x80  /**< 屏幕控制(已弃用) */
#define ID_SET_TIME       0x81  /**< 设置时间 */
#define ID_CLEAR_MILEAGE  0x82  /**< 清除里程 */
#define ID_SET_TOTAL_MILE 0x83  /**< 设置总里程 */
#define ID_SPEED          0x60  /**< 车速 */
#define ID_RPM            0x61  /**< 转速 */
#define ID_GEAR           0x62  /**< 挡位 */
#define ID_LIGHT          0x63  /**< 灯光、警示灯(已弃用，使用ID_LIGHT_SCREEN_CTRL) */
#define ID_REMAIN_RANGE   0x64  /**< 剩余续航里程 */
#define ID_MILEAGE        0x65  /**< 总里程和小计里程 */
#define ID_TIME           0x66  /**< 时间 */
#define ID_CONTROLLER_STATUS 0x67  /**< 中控控制器状态 */
#define ID_ACK            0x55  /**< 应答位 */
#define MAX_FRAME_SIZE    256   /**< 最大帧大小 */

// ====== 中控控制器相关定义 ======
#define CONTROLLER_STATUS_DATA_LEN  18  /**< 中控控制器数据长度 */

// ====== 灯光控制相关定义 ======
#define LIGHT_CTRL_DOUBLE_FLASH_ON   0x01  /**< 打开双闪 */
#define LIGHT_CTRL_DOUBLE_FLASH_OFF  0x02  /**< 关闭双闪 */
#define LIGHT_CTRL_LEFT_TURN_ON      0x03  /**< 打开左转向 */
#define LIGHT_CTRL_RIGHT_TURN_ON     0x04  /**< 打开右转灯 */
#define LIGHT_CTRL_TURN_OFF          0x05  /**< 关闭转向 */
#define LIGHT_CTRL_HEADLIGHT_ON      0x06  /**< 打开车灯 */

// ====== 屏幕控制相关定义 ======
#define SCREEN_CTRL_OFF              0x01  /**< 关闭屏幕 */
#define SCREEN_CTRL_ON               0x02  /**< 打开屏幕 */

// ====== 中控控制器状态位定义 ======
// 控制器状态1 (Data0)
#define CTRL_STATUS1_ONE_KEY_REPAIR    (1 << 0)  /**< 一键修复 */
#define CTRL_STATUS1_TCS_STATUS        (1 << 1)  /**< TCS状态 */
#define CTRL_STATUS1_LOW_POWER_EXTEND  (1 << 2)  /**< 低电量延长续航状态 */
#define CTRL_STATUS1_DSR_SENSE         (1 << 3)  /**< DSR感应 */
#define CTRL_STATUS1_BRAKE_STATUS      (1 << 4)  /**< 制动状态 */
#define CTRL_STATUS1_P_GEAR_SIGNAL     (1 << 5)  /**< P挡信号 */
#define CTRL_STATUS1_WORK_MODE_MASK    (0x03 << 6)  /**< 工作模式掩码 */
#define CTRL_STATUS1_WORK_MODE_ECO     (0x01 << 6)  /**< ECO模式 */
#define CTRL_STATUS1_WORK_MODE_POWER   (0x02 << 6)  /**< POWER模式 */
#define CTRL_STATUS1_WORK_MODE_CRUISE  (0x03 << 6)  /**< CRUISE模式 */

// 控制器状态2 (Data1)
#define CTRL_STATUS2_CRUISE_STATUS     (1 << 0)  /**< 巡航状态 */
#define CTRL_STATUS2_FUNCTION1_STATUS  (1 << 1)  /**< 功能1状态 */
#define CTRL_STATUS2_PUSH_STATUS       (1 << 2)  /**< 推车状态 */
#define CTRL_STATUS2_REVERSE_STATUS    (1 << 3)  /**< 倒车状态 */
#define CTRL_STATUS2_OVERTAKE          (1 << 4)  /**< 超车 */
#define CTRL_STATUS2_SIDE_STAND        (1 << 5)  /**< 边撑 */
#define CTRL_STATUS2_LONG_PRESS_P      (1 << 6)  /**< 长按P挡 */
#define CTRL_STATUS2_BRAKE_FAULT       (1 << 7)  /**< 刹车故障 */

// 电池电压使用状态 (Data2)
#define BATTERY_VOLTAGE_MASK           0x0F      /**< 电压选择掩码 */
#define BATTERY_VOLTAGE_36V            0x01      /**< 36V */
#define BATTERY_VOLTAGE_48V            0x02      /**< 48V */
#define BATTERY_VOLTAGE_60V            0x03      /**< 60V */
#define BATTERY_VOLTAGE_72V            0x04      /**< 72V */
#define BATTERY_VOLTAGE_84V            0x05      /**< 84V */
#define BATTERY_VOLTAGE_96V            0x06      /**< 96V */
#define BATTERY_DOUBLE_UNDERVOLT       (1 << 5)  /**< 双欠压选择 */
#define BATTERY_VEHICLE_TYPE           (1 << 6)  /**< 车型：0电自1电摩 */

// 故障代码1 (Data8)
#define FAULT_CODE1_HIGH_TEMP          (1 << 0)  /**< 高温保护 */
#define FAULT_CODE1_STALL_PROTECT      (1 << 1)  /**< 堵转保护 */
#define FAULT_CODE1_OVERCURRENT        (1 << 2)  /**< 过流保护 */
#define FAULT_CODE1_UNDERVOLT          (1 << 3)  /**< 欠压保护 */
#define FAULT_CODE1_THROTTLE_FAULT     (1 << 4)  /**< 转把故障 */
#define FAULT_CODE1_MOTOR_HALL_FAULT   (1 << 5)  /**< 电机霍尔故障 */
#define FAULT_CODE1_MOTOR_PHASE_LOSS   (1 << 6)  /**< 电机缺相 */
#define FAULT_CODE1_CONTROLLER_FAULT   (1 << 7)  /**< 控制器故障 */

// 使能开关 (Data13)
#define ENABLE_SEAT_SENSE              (1 << 0)  /**< 坐垫感应开关 */
#define ENABLE_SIDE_STAND_SENSE        (1 << 1)  /**< 边撑感应开关 */
#define ENABLE_DRIVING_HABIT           (1 << 2)  /**< 驾驶习惯开关 */
#define ENABLE_REVERSE_FUNCTION        (1 << 3)  /**< 倒车功能开关 */
#define ENABLE_PUSH_FUNCTION           (1 << 4)  /**< 推车功能开关 */
#define ENABLE_LOW_POWER_EXTEND        (1 << 5)  /**< 低电量延长续航功能开关 */
#define ENABLE_SOFT_START              (1 << 6)  /**< 软/硬启动：0硬启动1软启动 */
#define ENABLE_FUNCTION1               (1 << 7)  /**< 功能1使能 */

// 控制器请求 (Data14)
#define CTRL_REQUEST_SUBCMD_MASK       0x7F      /**< 查询/设置子命令掩码 */
#define CTRL_REQUEST_TYPE              (1 << 7)  /**< 请求命令：0查询1设置 */

// 整车状态 (Data15)
#define VEHICLE_STATUS_CHARGING        (1 << 0)  /**< 充电检测 */
#define VEHICLE_STATUS_SPEED_ALERT     (1 << 1)  /**< 电自超速提示音 */
#define VEHICLE_STATUS_SLOPE_ASSIST_MASK (0x03 << 2)  /**< 坡道辅助掩码 */
#define VEHICLE_STATUS_SLOPE_ASSIST_OFF  (0x00 << 2)  /**< 坡道辅助未运行 */
#define VEHICLE_STATUS_SLOPE_ASSIST_ON   (0x01 << 2)  /**< 坡道辅助运行中 */
#define VEHICLE_STATUS_SLOPE_ASSIST_COUNTDOWN (0x02 << 2)  /**< 坡道辅助计时 */
#define VEHICLE_STATUS_OVERTAKE_FLAG   (1 << 4)  /**< 超车标识 */
#define VEHICLE_STATUS_DSR_PASSENGER   (1 << 5)  /**< DSR乘人 */
#define VEHICLE_STATUS_SPEEDING_PROTECT (1 << 6)  /**< 飞车保护 */
#define VEHICLE_STATUS_SIDE_STAND_P    (1 << 7)  /**< 边撑时解P */

// 颠簸等级 (Data17)
#define BUMP_LEVEL_SMOOTH              0x01      /**< 平稳 */
#define BUMP_LEVEL_LIGHT               0x02      /**< 轻微颠簸 */
#define BUMP_LEVEL_SEVERE              0x03      /**< 严重颠簸 */

// 特殊值定义
#define INVALID_VALUE_FE               0xFE      /**< 异常值 */
#define INVALID_VALUE_FF               0xFF      /**< 无效值 */
#define INVALID_VALUE_FFFE             0xFFFE    /**< 16位异常值 */
#define INVALID_VALUE_FFFF             0xFFFF    /**< 16位无效值 */

// 偏移值定义
#define TEMP_OFFSET                    40        /**< 温度偏移值 */
#define CURRENT_OFFSET                 1000      /**< 电流偏移值(0.1A单位) */

/**
 * @brief 中控控制器状态数据结构
 */
typedef struct {
    uint8_t controller_status1;    /**< Data0: 控制器状态1 */
    uint8_t controller_status2;    /**< Data1: 控制器状态2 */
    uint8_t battery_voltage_status; /**< Data2: 电池电压使用状态 */
    uint8_t controller_temp;       /**< Data3: 控制器温度(偏移-40) */
    uint16_t bus_voltage;          /**< Data4-5: 母线电压(0.1V单位) */
    uint16_t bus_current;          /**< Data6-7: 母线电流(偏移-100, 0.1A单位) */
    uint8_t fault_code1;           /**< Data8: 故障代码1 */
    uint8_t fault_code2;           /**< Data9: 故障代码2(预留) */
    uint8_t lead_acid_soc;         /**< Data10: 铅酸SOC(1-100%, 0xFF不支持) */
    uint16_t throttle_voltage;     /**< Data11-12: 转把电压(1mV单位, 0-5000) */
    uint8_t enable_switches;       /**< Data13: 使能开关 */
    uint8_t controller_request;    /**< Data14: 控制器请求 */
    uint8_t vehicle_status;        /**< Data15: 整车状态 */
    uint8_t weight;                /**< Data16: 体重(kg) */
    uint8_t bump_level;            /**< Data17: 颠簸等级 */
} controller_status_t;

/**
 * @brief 帧解析回调函数类型
 * @param id 命令ID
 * @param ack 是否需要应答
 * @param data 数据指针
 * @param data_len 数据长度
 * @param user_data 用户数据指针
 */
typedef void (*frame_callback_t)(uint8_t id, uint8_t ack, const uint8_t *data, uint8_t data_len, void *user_data);

/**
 * @brief 计算校验和
 * @param data 数据缓冲区
 * @param length 数据长度
 * @return 校验和
 */
uint16_t uart_protocol_calculate_checksum(const uint8_t *data, uint16_t length);

/**
 * @brief 发送协议帧
 * @param fd 串口文件描述符
 * @param need_ack 是否需要应答
 * @param id 命令ID
 * @param data 数据指针
 * @param data_len 数据长度
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_frame(serial_handle_t *handle, uint8_t need_ack, uint8_t id, const uint8_t *data, uint8_t data_len);

/**
 * @brief 解析协议帧
 * @param data 接收到的数据
 * @param len 数据长度
 * @param callback 解析回调函数
 * @param user_data 用户数据指针
 * @return 解析的帧数量
 */
int uart_protocol_parse_frame(const uint8_t *data, int len, frame_callback_t callback, void *user_data);

/**
 * @brief 发送应答帧
 * @param fd 串口文件描述符
 * @param id 需要应答的命令ID
 * @param status 应答状态，0成功，非0失败
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_ack(serial_handle_t *handle, uint8_t id, uint8_t status);

/**
 * @brief 发送上电同步信息
 * @param fd 串口文件描述符
 * @param need_ack 是否需要应答
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_power_sync(serial_handle_t *handle, uint8_t need_ack);

/**
 * @brief 发送心跳包
 * @param fd 串口文件描述符
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_heartbeat(serial_handle_t *handle);

/**
 * @brief 发送MCU版本号
 * @param fd 串口文件描述符
 * @param version 版本字符串
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_mcu_version(serial_handle_t *handle, const char *version);

/**
 * @brief 发送车速信息
 * @param fd 串口文件描述符
 * @param speed 车速值(km/h)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_speed(serial_handle_t *handle, uint8_t speed);

/**
 * @brief 发送转速信息
 * @param fd 串口文件描述符
 * @param rpm 转速值(RPM)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_rpm(serial_handle_t *handle, uint16_t rpm);

/**
 * @brief 发送挡位信息
 * @param fd 串口文件描述符
 * @param gear 挡位值
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_gear(serial_handle_t *handle, uint8_t gear);

/**
 * @brief 发送灯光控制命令
 * @param fd 串口文件描述符
 * @param need_ack 是否需要应答
 * @param ctrl 控制值(参见LIGHT_*定义)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_light_ctrl(serial_handle_t *handle, uint8_t need_ack, uint8_t ctrl);

/**
 * @brief 发送剩余续航里程
 * @param fd 串口文件描述符
 * @param range 续航里程(km)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_remain_range(serial_handle_t *handle, uint8_t range);

/**
 * @brief 发送时间信息
 * @param fd 串口文件描述符
 * @param year 年(从2000年开始)
 * @param month 月
 * @param day 日
 * @param hour 时
 * @param minute 分
 * @param second 秒
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_time(serial_handle_t *handle, uint8_t year, uint8_t month, uint8_t day, 
                           uint8_t hour, uint8_t minute, uint8_t second);

/**
 * @brief 发送里程信息
 * @param fd 串口文件描述符
 * @param total_mileage 总里程(km)
 * @param trip_mileage 小计里程(km)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_mileage(serial_handle_t *handle, uint32_t total_mileage, uint32_t trip_mileage);

/**
 * @brief 发送工厂复位命令
 * @param fd 串口文件描述符
 * @param need_ack 是否需要应答
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_factory_reset(serial_handle_t *handle, uint8_t need_ack);

/**
 * @brief 发送屏幕控制命令
 * @param fd 串口文件描述符
 * @param need_ack 是否需要应答
 * @param ctrl 控制值(0:关闭, 1:打开)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_screen_ctrl(serial_handle_t *handle, uint8_t need_ack, uint8_t ctrl);

/**
 * @brief 发送灯光和屏幕控制命令
 * @param fd 串口文件描述符
 * @param need_ack 是否需要应答
 * @param light_ctrl 灯光控制值(参见LIGHT_CTRL_*定义)
 * @param screen_ctrl 屏幕控制值(参见SCREEN_CTRL_*定义)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_light_screen_ctrl(serial_handle_t *handle, uint8_t need_ack, uint8_t light_ctrl, uint8_t screen_ctrl);

/**
 * @brief 发送中控控制器状态信息
 * @param fd 串口文件描述符
 * @param controller_data 中控控制器状态数据(18字节)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_controller_status(serial_handle_t *handle, const uint8_t *controller_data);

/**
 * @brief 发送中控控制器状态信息(结构体版本)
 * @param fd 串口文件描述符
 * @param status 中控控制器状态结构体指针
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_controller_status_struct(serial_handle_t *handle, const controller_status_t *status);

/**
 * @brief 解析中控控制器状态数据
 * @param data 原始数据指针(18字节)
 * @param status 解析后的状态结构体指针
 * @return 0成功，负值失败
 */
int uart_protocol_parse_controller_status(const uint8_t *data, controller_status_t *status);

/**
 * @brief 将中控控制器状态结构体转换为原始数据
 * @param status 状态结构体指针
 * @param data 输出的原始数据缓冲区(至少18字节)
 * @return 0成功，负值失败
 */
int uart_protocol_pack_controller_status(const controller_status_t *status, uint8_t *data);

#endif /* UART_PROTOCOL_H */