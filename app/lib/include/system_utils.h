/**
 * @file system_utils.h
 * @brief LVGL系统功能调用工具类，用于在LVGL应用中方便地执行系统命令和获取系统信息
 * @version 1.0.0
 * @date 2025-06-08 14:04
 */

#ifndef SYSTEM_UTILS_H
#define SYSTEM_UTILS_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include <stddef.h>

// 日志级别定义
#define SYSTEM_LOG_LEVEL_ERROR 0
#define SYSTEM_LOG_LEVEL_WARN  1
#define SYSTEM_LOG_LEVEL_INFO  2
#define SYSTEM_LOG_LEVEL_DEBUG 3

/**
 * @brief 设置日志级别
 * @param[in] level 日志级别 (0:ERROR, 1:WARN, 2:INFO, 3:DEBUG)
 */
void system_log_set_level(int level);

/**
 * @brief 设置日志文件路径
 * @param[in] path 日志文件路径
 */
void system_log_set_path(const char *path);

/**
 * @brief 内部函数：写入日志
 * @param[in] level 日志级别
 * @param[in] func 函数名称
 * @param[in] line 行号
 * @param[in] fmt 格式化字符串
 */
void system_log(int level, const char *func, int line, const char *fmt, ...);

/**
 * @brief 命令执行结果回调函数类型
 * @param exit_code 命令执行的退出码
 * @param output 命令的输出内容
 * @param user_data 用户自定义数据
 */
typedef void (*system_command_callback_t)(int exit_code, const char *output, void *user_data);

/**
 * @brief 系统命令执行结果结构体
 */
typedef struct {
    int exit_code;       /* 命令退出码 */
    char *output;        /* 命令输出内容 */
    size_t output_size;  /* 输出内容的大小 */
} system_command_result_t;

/**
 * @brief 系统信息结构体
 */
typedef struct {
    char os_version[128];        /* 操作系统版本 */
    char kernel_version[64];     /* 内核版本 */
    char hostname[64];           /* 主机名 */
    char cpu_model[128];         /* CPU型号 */
    char total_memory[32];       /* 总内存 */
    char free_memory[32];        /* 可用内存 */
    char uptime[32];             /* 系统运行时间 */
    char load_average[32];       /* 系统负载 */
    char ip_address[32];         /* IP地址 */
    char mac_address[32];        /* MAC地址 */
    char disk_space[64];         /* 磁盘空间 */
} system_info_t;

/**
 * @brief 异步回调函数类型
 */
typedef void (*system_async_func_t)(void *user_data);

/**
 * @brief 异步回调数据结构体
 */
typedef struct {
    system_async_func_t func;  /* 回调函数 */
    void *user_data;       /* 用户数据 */
} system_async_call_data_t;

/**
 * @brief 初始化系统工具库
 * @return 成功返回0，失败返回负数错误码
 */
int system_utils_init(void);

/**
 * @brief 清理系统工具库资源
 */
void system_utils_cleanup(void);

/**
 * @brief 同步执行系统命令，等待命令执行完成并返回结果
 * @param command 要执行的命令
 * @param result 命令执行结果，函数内部会分配内存，使用完毕后需调用system_command_result_free释放
 * @return 成功返回0，失败返回负数错误码
 */
int system_execute_command_sync(const char *command, system_command_result_t *result);

/**
 * @brief 异步执行系统命令，不等待命令执行完成，通过回调函数返回结果
 * @param command 要执行的命令
 * @param callback 命令执行完成后的回调函数
 * @param user_data 用户自定义数据，会传递给回调函数
 * @return 成功返回0，失败返回负数错误码
 */
int system_execute_command_async(const char *command, system_command_callback_t callback, void *user_data);

/**
 * @brief 释放命令执行结果结构体中的资源
 * @param result 命令执行结果结构体
 */
void system_command_result_free(system_command_result_t *result);

/**
 * @brief 获取系统信息
 * @param info 用于存储系统信息的结构体
 * @return 成功返回0，失败返回负数错误码
 */
int system_get_info(system_info_t *info);

/**
 * @brief 执行系统关机命令
 * @param delay_seconds 延迟时间（秒），0表示立即关机
 * @return 成功返回0，失败返回负数错误码
 */
int system_shutdown(int delay_seconds);

/**
 * @brief 执行系统重启命令
 * @param delay_seconds 延迟时间（秒），0表示立即重启
 * @return 成功返回0，失败返回负数错误码
 */
int system_reboot(int delay_seconds);

/**
 * @brief 在后台启动一个服务或程序
 * @param program 要启动的程序路径
 * @param args 程序参数数组，以NULL结尾
 * @return 成功返回进程ID，失败返回负数错误码
 */
int system_start_service(const char *program, char *const args[]);

/**
 * @brief 停止一个服务或程序
 * @param service_name 服务名称
 * @return 成功返回0，失败返回负数错误码
 */
int system_stop_service(const char *service_name);

/**
 * @brief 安装软件包
 * @param package_name 软件包名称
 * @return 成功返回0，失败返回负数错误码
 */
int system_install_package(const char *package_name);

/**
 * @brief 卸载软件包
 * @param package_name 软件包名称
 * @return 成功返回0，失败返回负数错误码
 */
int system_uninstall_package(const char *package_name);

/**
 * @brief 检查服务或进程是否在运行
 * @param service_name 服务或进程名称
 * @return 运行中返回true，未运行返回false
 */
bool system_is_service_running(const char *service_name);

/**
 * @brief 获取当前系统时间
 * @param time_str 用于存储时间字符串的缓冲区
 * @param size 缓冲区大小
 * @return 成功返回0，失败返回负数错误码
 */
int system_get_current_time(char *time_str, size_t size);

/**
 * @brief 获取系统错误码对应的错误信息
 * @param error_code 错误码
 * @return 错误信息字符串
 */
const char *system_get_error_string(int error_code);

/**
 * @brief 打开串口设备
 * @param port_path 串口设备路径
 * @param baud_rate 波特率
 * @return 成功返回文件描述符，失败返回负数错误码
 */
int system_serial_open(const char *port_path, int baud_rate);

/**
 * @brief 关闭串口设备
 * @param fd 串口文件描述符
 * @return 成功返回0，失败返回负数错误码
 */
int system_serial_close(int fd);

/**
 * @brief 从串口读取数据
 * @param fd 串口文件描述符
 * @param buffer 数据缓冲区
 * @param size 缓冲区大小
 * @return 成功返回读取的字节数，失败返回负数错误码
 */
int system_serial_read(int fd, char *buffer, size_t size);

/**
 * @brief 向串口写入数据
 * @param fd 串口文件描述符
 * @param data 要写入的数据
 * @param size 数据大小
 * @return 成功返回写入的字节数，失败返回负数错误码
 */
int system_serial_write(int fd, const char *data, size_t size);

/**
 * @brief 在系统中执行异步调用
 * @param func 要执行的函数
 * @param data 用户数据指针
 * @return 成功返回0，失败返回负数错误码
 */
int system_async_call(void (*func)(void *), void *data);

#ifdef __cplusplus
}
#endif

#endif /* SYSTEM_UTILS_H */ 