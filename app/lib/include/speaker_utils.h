/**
 * @file speaker_utils.h
 * @brief 扬声器工具函数头文件
 * @version 1.0.0
 * @date 2025-06-07
 */

#ifndef SPEAKER_UTILS_H
#define SPEAKER_UTILS_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 扬声器错误码
 */
typedef enum {
    SPEAKER_ERROR_NONE = 0,         /**< 成功 */
    SPEAKER_ERROR_INIT_FAILED,      /**< 初始化失败 */
    SPEAKER_ERROR_INVALID_PARAM,    /**< 无效参数 */
    SPEAKER_ERROR_GPIO_FAILED,      /**< GPIO操作失败 */
    SPEAKER_ERROR_PLAY_FAILED,      /**< 播放失败 */
    SPEAKER_ERROR_VOLUME_FAILED,    /**< 音量设置失败 */
    SPEAKER_ERROR_FILE_NOT_FOUND,   /**< 文件未找到 */
    SPEAKER_ERROR_SYSTEM,           /**< 系统错误 */
} speaker_error_t;

/**
 * @brief 扬声器音频格式
 */
typedef enum {
    SPEAKER_FORMAT_S16_LE = 0,      /**< 16位小端格式 */
    SPEAKER_FORMAT_S24_LE,          /**< 24位小端格式 */
    SPEAKER_FORMAT_S32_LE,          /**< 32位小端格式 */
} speaker_format_t;

/**
 * @brief 扬声器初始化
 * @return 成功返回SPEAKER_ERROR_NONE，失败返回对应错误码
 */
speaker_error_t speaker_init(void);

/**
 * @brief 扬声器静音控制
 * @param[in] mute 1表示静音，0表示取消静音
 * @return 成功返回SPEAKER_ERROR_NONE，失败返回对应错误码
 */
speaker_error_t speaker_set_mute(int mute);

/**
 * @brief 设置扬声器音量
 * @param[in] volume 音量值(0-63)
 * @return 成功返回SPEAKER_ERROR_NONE，失败返回对应错误码
 */
speaker_error_t speaker_set_volume(int volume);

/**
 * @brief 获取当前扬声器音量
 * @param[out] volume 音量值指针
 * @return 成功返回SPEAKER_ERROR_NONE，失败返回对应错误码
 */
speaker_error_t speaker_get_volume(int *volume);

/**
 * @brief 播放音频文件
 * @param[in] filename 音频文件路径
 * @param[in] format 音频格式
 * @param[in] sample_rate 采样率
 * @param[in] verbose 是否显示详细信息(0-3，0表示不显示，3表示最详细)
 * @return 成功返回SPEAKER_ERROR_NONE，失败返回对应错误码
 */
speaker_error_t speaker_play_file(const char *filename, speaker_format_t format, 
                                 int sample_rate, int verbose);

/**
 * @brief 停止播放
 * @return 成功返回SPEAKER_ERROR_NONE，失败返回对应错误码
 */
speaker_error_t speaker_stop(void);

/**
 * @brief 清理扬声器资源
 * @return 成功返回SPEAKER_ERROR_NONE，失败返回对应错误码
 */
speaker_error_t speaker_cleanup(void);

/**
 * @brief 获取错误信息
 * @param[in] error 错误码
 * @return 错误描述字符串
 */
const char *speaker_error_to_string(speaker_error_t error);

#ifdef __cplusplus
}
#endif

#endif /* SPEAKER_UTILS_H */ 