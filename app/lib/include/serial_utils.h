/**
 * @file serial_utils.h
 * @brief 串口通信工具函数头文件
 * @version 1.0.0
 * @date 2025-06-08
 */

#ifndef SERIAL_UTILS_H
#define SERIAL_UTILS_H

#ifdef __cplusplus
extern "C" {
#endif


#include <stdint.h>
#include <stddef.h>
#include <pthread.h>

/**
 * @brief 串口错误码
 */
typedef enum {
    SERIAL_ERROR_NONE = 0,           /**< 成功 */
    SERIAL_ERROR_OPEN_FAILED,        /**< 打开串口失败 */
    SERIAL_ERROR_INVALID_PARAM,      /**< 无效参数 */
    SERIAL_ERROR_CONFIG_FAILED,      /**< 配置串口失败 */
    SERIAL_ERROR_WRITE_FAILED,       /**< 写入失败 */
    SERIAL_ERROR_READ_FAILED,        /**< 读取失败 */
    SERIAL_ERROR_THREAD_FAILED,      /**< 线程创建失败 */
    SERIAL_ERROR_TIMEOUT,            /**< 超时 */
    SERIAL_ERROR_SYSTEM,             /**< 系统错误 */
    SERIAL_ERROR_NOT_OPENED,         /**< 设备未打开 */
} serial_error_t;

/**
 * @brief 串口句柄
 */
typedef struct {
    int fd;                     /**< 串口文件描述符 */
    char device[64];            /**< 串口设备路径 */
    int baud_rate;              /**< 波特率 */
    int data_bits;              /**< 数据位 */
    char parity;                /**< 校验位 */
    int stop_bits;              /**< 停止位 */
    int flow_control;           /**< 流控制 */
    pthread_t recv_thread;      /**< 接收线程ID */
    int thread_running;         /**< 线程运行标志 */
    void (*callback)(const char *, int);  /**< 接收回调函数 */
} serial_handle_t;

/**
 * @brief 初始化串口设备
 * @param device 串口设备路径
 * @param baud_rate 波特率
 * @param data_bits 数据位
 * @param parity 校验位 ('N'-无校验, 'E'-偶校验, 'O'-奇校验)
 * @param stop_bits 停止位 (1或2)
 * @param flow_control 流控制 (0-无, 1-硬件, 2-软件)
 * @param handle 串口句柄指针的指针
 * @return 成功返回SERIAL_ERROR_NONE，失败返回对应错误码
 */
serial_error_t serial_init(const char *device, int baud_rate, int data_bits, 
                          char parity, int stop_bits, int flow_control, 
                          serial_handle_t **handle);

/**
 * @brief 设置数据接收回调函数
 * @param handle 串口句柄
 * @param callback 回调函数
 * @return 成功返回SERIAL_ERROR_NONE，失败返回对应错误码
 */
serial_error_t serial_set_callback(serial_handle_t *handle, 
                                  void (*callback)(const char *data, int len));

/**
 * @brief 启动数据接收线程
 * @param handle 串口句柄
 * @return 成功返回SERIAL_ERROR_NONE，失败返回对应错误码
 */
serial_error_t serial_start_receive(serial_handle_t *handle);

/**
 * @brief 停止数据接收线程
 * @param handle 串口句柄
 * @return 成功返回SERIAL_ERROR_NONE，失败返回对应错误码
 */
serial_error_t serial_stop_receive(serial_handle_t *handle);

/**
 * @brief 发送字符串数据
 * @param handle 串口句柄
 * @param data 要发送的字符串
 * @return 成功返回SERIAL_ERROR_NONE，失败返回对应错误码
 */
serial_error_t serial_send_string(serial_handle_t *handle, const char *data);

/**
 * @brief 发送十六进制数据
 * @param handle 串口句柄
 * @param hex_string 十六进制字符串，如"48 65 6C 6C 6F"
 * @return 成功返回SERIAL_ERROR_NONE，失败返回对应错误码
 */
serial_error_t serial_send_hex(serial_handle_t *handle, const char *hex_string);

/**
 * @brief 发送二进制数据
 * @param handle 串口句柄
 * @param data 要发送的二进制数据
 * @param len 数据长度
 * @return 成功返回SERIAL_ERROR_NONE，失败返回对应错误码
 */
serial_error_t serial_send_binary(serial_handle_t *handle, const char *data, int len);

/**
 * @brief 读取串口数据
 * @param handle 串口句柄
 * @param buffer 用于存储读取数据的缓冲区
 * @param buffer_size 缓冲区大小
 * @param bytes_read 实际读取的字节数
 * @param timeout_ms 超时时间(毫秒)，0表示不等待，负数表示无限等待
 * @return 成功返回SERIAL_ERROR_NONE，失败返回对应错误码
 */
serial_error_t serial_read(serial_handle_t *handle, char *buffer, int buffer_size, 
                          int *bytes_read, int timeout_ms);

/**
 * @brief 关闭串口设备并释放资源
 * @param handle 串口句柄
 * @return 成功返回SERIAL_ERROR_NONE，失败返回对应错误码
 */
serial_error_t serial_close(serial_handle_t *handle);

/**
 * @brief 获取错误码对应的错误描述字符串
 * @param error 错误码
 * @return 错误描述字符串
 */
const char *serial_error_to_string(serial_error_t error);

#ifdef __cplusplus
}
#endif

#endif /* SERIAL_UTILS_H */ 