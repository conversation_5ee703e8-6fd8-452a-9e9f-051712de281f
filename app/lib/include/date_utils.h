/**
 * @file date_utils.h
 * @brief 日期工具函数头文件
 * @version 1.1.0
 * @date 2025-06-06
 */

#ifndef DATE_UTILS_H
#define DATE_UTILS_H

#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 日期时间结构体
 */
typedef struct {
    int year;    /**< 年份 */
    int month;   /**< 月份 (1-12) */
    int day;     /**< 日 (1-31) */
    int hour;    /**< 小时 (0-23) */
    int minute;  /**< 分钟 (0-59) */
    int second;  /**< 秒 (0-59) */
} date_time_t;

/**
 * @brief 错误码枚举
 */
typedef enum {
    DATE_ERROR_NONE = 0,         /**< 无错误 */
    DATE_ERROR_INVALID_PARAM,    /**< 无效参数 */
    DATE_ERROR_SYSTEM,           /**< 系统错误 */
    DATE_ERROR_PERMISSION,       /**< 权限错误 */
    DATE_ERROR_NETWORK,          /**< 网络错误 */
    DATE_ERROR_UNKNOWN           /**< 未知错误 */
} date_error_t;

/**
 * @brief 获取当前系统时间
 * @param[out] datetime 指向日期时间结构体的指针，用于存储获取的时间
 * @return 成功返回DATE_ERROR_NONE，失败返回对应错误码
 */
date_error_t date_get_time(date_time_t *datetime);

/**
 * @brief 设置系统时间
 * @param[in] datetime 指向日期时间结构体的指针，包含要设置的时间
 * @return 成功返回DATE_ERROR_NONE，失败返回对应错误码
 */
date_error_t date_set_time(const date_time_t *datetime);

/**
 * @brief 获取当前系统时间的Unix时间戳
 * @param[out] timestamp 指向time_t的指针，用于存储获取的时间戳
 * @return 成功返回DATE_ERROR_NONE，失败返回对应错误码
 */
date_error_t date_get_timestamp(time_t *timestamp);

/**
 * @brief 将Unix时间戳转换为日期时间
 * @param[in] timestamp Unix时间戳
 * @param[out] datetime 指向日期时间结构体的指针，用于存储转换后的时间
 * @return 成功返回DATE_ERROR_NONE，失败返回对应错误码
 */
date_error_t date_timestamp_to_datetime(time_t timestamp, date_time_t *datetime);

/**
 * @brief 格式化时间为字符串
 * @param[in] datetime 指向日期时间结构体的指针
 * @param[in] format 格式字符串，与strftime兼容
 * @param[out] str 输出字符串缓冲区
 * @param[in] size 缓冲区大小
 * @return 成功返回DATE_ERROR_NONE，失败返回对应错误码
 */
date_error_t date_format_time(const date_time_t *datetime, const char *format, 
                             char *str, size_t size);

/**
 * @brief 设置系统时区
 * @param[in] timezone 时区字符串，如"Asia/Shanghai"
 * @return 成功返回DATE_ERROR_NONE，失败返回对应错误码
 */
date_error_t date_set_timezone(const char *timezone);

/**
 * @brief 从网络获取时间并同步系统时间
 * @param[in] ntp_server NTP服务器地址，如"ntp.aliyun.com"，NULL则使用默认服务器
 * @param[out] datetime 如果不为NULL，则存储获取的时间
 * @return 成功返回DATE_ERROR_NONE，失败返回对应错误码
 */
date_error_t date_sync_from_network(const char *ntp_server, date_time_t *datetime);

/**
 * @brief 检查系统时间是否有效（非1970年附近）
 * @return 1表示有效，0表示无效
 */
int date_is_time_valid(void);

/**
 * @brief 自动校正系统时间
 * 如果系统时间无效或接近1970年，则尝试从网络同步时间
 * @return 成功返回DATE_ERROR_NONE，失败返回对应错误码
 */
date_error_t date_auto_correct(void);

#ifdef __cplusplus
}
#endif

#endif /* DATE_UTILS_H */ 