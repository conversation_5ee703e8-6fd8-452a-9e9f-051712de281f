/**
 * @file uart_protocol.c
 * @brief UART串口通讯协议公共API实现
 * @version 1.1.0
 * @date 2025-07-31 21:52
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <unistd.h>
#include <time.h>
#include <termios.h>  // 添加termios.h头文件，解决tcdrain未声明的问题

#include "uart_protocol.h"

/**
 * @brief 计算校验和
 * @param data 数据缓冲区
 * @param length 数据长度
 * @return 校验和
 */
uint16_t uart_protocol_calculate_checksum(const uint8_t *data, uint16_t length) {
    uint32_t checksum = 0;
    for (uint16_t i = 0; i < length; i++) {
        checksum += data[i];
        checksum += ((checksum & 0xFF) << 8) + 0x100;
        checksum = (checksum ^ (checksum >> 16)) & 0xFFFF;
    }
    return (uint16_t)checksum;
}

/**
 * @brief 发送协议帧
 * @param *handle 串口文件描述符
 * @param need_ack 是否需要应答
 * @param id 命令ID
 * @param data 数据指针
 * @param data_len 数据长度
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_frame(serial_handle_t *handle, uint8_t need_ack, uint8_t id, const uint8_t *data, uint8_t data_len) {
    // 获取串口句柄
    if (handle == NULL || handle-> fd < 0) {
        return -1;
    }
    
    uint8_t frame[MAX_FRAME_SIZE];
    uint8_t frame_len = 0;
    uint8_t checksum_data[MAX_FRAME_SIZE] = {0}; // 初始化为0，避免未初始化警告
    uint16_t checksum;

    // 构建帧头和数据
    frame[frame_len++] = FRAME_HEADER_1;
    frame[frame_len++] = FRAME_HEADER_2;
    frame[frame_len++] = 1 + 1 + data_len + 2; // 长度 = 应答位 + ID + 数据 + 校验和(2字节)
    frame[frame_len++] = need_ack;
    frame[frame_len++] = id;
    
    if (data && data_len > 0) {
        memcpy(&frame[frame_len], data, data_len);
        frame_len += data_len;
    }
    
    // 计算校验和
    checksum_data[0] = need_ack;
    checksum_data[1] = id;
    if (data && data_len > 0) {
        memcpy(&checksum_data[2], data, data_len);
    }
    
    checksum = uart_protocol_calculate_checksum(checksum_data, 2 + data_len);
    frame[frame_len++] = (checksum >> 8) & 0xFF;  // 校验和高位
    frame[frame_len++] = checksum & 0xFF;        // 校验和低位
    
    // 使用serial_utils发送数据
    serial_error_t ret = serial_send_binary(handle, (const char *)frame, frame_len);
    
    // 打印发送的帧数据（调试用）
    printf("[PROTO] 发送帧: ");
    for (int i = 0; i < frame_len; i++) {
        printf("%02X ", frame[i]);
    }
    printf("\n");
    
    // 返回发送结果
    return (ret == SERIAL_ERROR_NONE) ? frame_len : -1;
}

/**
 * @brief 解析协议帧
 * @param data 接收到的数据
 * @param len 数据长度
 * @param callback 解析回调函数
 * @param user_data 用户数据指针
 * @return 解析的帧数量
 */
int uart_protocol_parse_frame(const uint8_t *data, int len, frame_callback_t callback, void *user_data) {
    if (data == NULL || len <= 0 || callback == NULL) {
        return 0;
    }
    
    int offset = 0;
    int frames_parsed = 0;
    
    // 循环解析可能的多个帧
    while (offset + 7 <= len) { // 确保至少有帧头和长度字段(2+1+1+1+2)
        // 查找帧头
        while (offset + 7 <= len) {
            if (data[offset] == FRAME_HEADER_1 && data[offset+1] == FRAME_HEADER_2) {
                break;  // 找到帧头
            }
            offset++;
        }

        if (offset + 7 > len) {
            // 没有找到完整帧头
            break;
        }

        uint8_t frame_len = data[offset+2];
        
        // 检查是否有完整的帧
        if (offset + 3 + frame_len > len) {
            printf("[PROTO] 帧不完整，剩余数据长度不足\n");
            break;
        }

        uint8_t ack = data[offset+3];
        uint8_t id = data[offset+4];
        uint8_t data_len = frame_len - 4;  // 4 = 应答位(1) + ID(1) + 校验和(2)

        // 验证校验和
        uint16_t received_checksum = ((uint16_t)data[offset + 3 + frame_len - 2] << 8) |
                                    data[offset + 3 + frame_len - 1];

        uint8_t checksum_data[MAX_FRAME_SIZE];
        checksum_data[0] = ack;
        checksum_data[1] = id;
        if (data_len > 0) {
            memcpy(&checksum_data[2], &data[offset+5], data_len);
        }

        uint16_t calculated_checksum = uart_protocol_calculate_checksum(checksum_data, 2 + data_len);

        if (received_checksum != calculated_checksum) {
            printf("[PROTO] 校验和错误: 接收=0x%04X, 计算=0x%04X, ID=0x%02X\n",
                   received_checksum, calculated_checksum, id);
            // 继续处理下一帧，不跳过当前帧
            offset += 3 + frame_len;
            continue;
        }

        // 打印解析的帧信息（调试用）
        printf("[PROTO] 解析帧: ID=0x%02X, ACK=%d, 数据长度=%d\n", id, ack, data_len);

        // 调用回调函数处理帧
        if (callback) {
            callback(id, ack, &data[offset+5], data_len, user_data);
        }
        
        // 移动到下一帧
        offset += 3 + frame_len;
        frames_parsed++;
    }
    
    return frames_parsed;
}

/**
 * @brief 发送应答帧
 * @param *handle 串口文件描述符
 * @param id 需要应答的命令ID
 * @param status 应答状态，0成功，非0失败
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_ack(serial_handle_t *handle, uint8_t id, uint8_t status) {
    uint8_t data[2] = {id, status};
    return uart_protocol_send_frame(handle, NO_ACK, ID_ACK, data, 2);
}

/**
 * @brief 发送上电同步信息
 * @param *handle 串口文件描述符
 * @param need_ack 是否需要应答
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_power_sync(serial_handle_t *handle, uint8_t need_ack) {
    uint8_t data[1] = {0x01};  // 同步标志
    return uart_protocol_send_frame(handle, need_ack, ID_POWER_SYNC, data, 1);
}

/**
 * @brief 发送心跳包
 * @param *handle 串口文件描述符
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_heartbeat(serial_handle_t *handle) {
    uint8_t data[1] = {0x01};  // 心跳标志
    return uart_protocol_send_frame(handle, NO_ACK, ID_HEARTBEAT, data, 1);
}

/**
 * @brief 发送MCU版本号
 * @param *handle 串口文件描述符
 * @param version 版本字符串
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_mcu_version(serial_handle_t *handle, const char *version) {
    if (version == NULL) {
        return -1;
    }
    
    size_t len = strlen(version);
    if (len > 20) {
        len = 20;  // 限制最大长度
    }
    
    return uart_protocol_send_frame(handle, NO_ACK, ID_MCU_VERSION, (const uint8_t *)version, len);
}

/**
 * @brief 发送车速信息
 * @param *handle 串口文件描述符
 * @param speed 车速值(km/h)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_speed(serial_handle_t *handle, uint8_t speed) {
    uint8_t data[2];
    data[0] = (speed * 2) & 0xFF;  // 车速值乘以2
    data[1] = 0;  // 保留字节
    return uart_protocol_send_frame(handle, NO_ACK, ID_SPEED, data, 2);
}

/**
 * @brief 发送转速信息
 * @param *handle 串口文件描述符
 * @param rpm 转速值(RPM)
 * @return 发送的字节数，错误返回负值
 * @note v13版本：Data0(Bit0~Bit7) + Data1(Bit8~Bit15)，获取数据后需根据轮胎型号计算
 */
int uart_protocol_send_rpm(serial_handle_t *handle, uint16_t rpm) {
    uint8_t data[2];
    data[0] = rpm & 0xFF;         // Data0: Bit0~Bit7 (低字节)
    data[1] = (rpm >> 8) & 0xFF;  // Data1: Bit8~Bit15 (高字节)
    return uart_protocol_send_frame(handle, NO_ACK, ID_RPM, data, 2);
}

/**
 * @brief 发送挡位信息
 * @param *handle 串口文件描述符
 * @param gear 挡位值
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_gear(serial_handle_t *handle, uint8_t gear) {
    uint8_t data[1] = {gear & 0xFF};
    return uart_protocol_send_frame(handle, NO_ACK, ID_GEAR, data, 1);
}

/**
 * @brief 发送剩余续航里程
 * @param *handle 串口文件描述符
 * @param range 续航里程(km)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_remain_range(serial_handle_t *handle, uint8_t range) {
    uint8_t data[2] = {range & 0xFF, 0};  // 第二个字节为保留字节
    return uart_protocol_send_frame(handle, NO_ACK, ID_REMAIN_RANGE, data, 2);
}

/**
 * @brief 发送时间信息
 * @param *handle 串口文件描述符
 * @param year 年(从2000年开始)
 * @param month 月
 * @param day 日
 * @param hour 时
 * @param minute 分
 * @param second 秒
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_time(serial_handle_t *handle, uint8_t year, uint8_t month, uint8_t day, 
                           uint8_t hour, uint8_t minute, uint8_t second) {
    uint8_t data[6];
    data[0] = year;
    data[1] = month;
    data[2] = day;
    data[3] = hour;
    data[4] = minute;
    data[5] = second;
    return uart_protocol_send_frame(handle, NO_ACK, ID_TIME, data, 6);
}

/**
 * @brief 发送里程信息
 * @param *handle 串口文件描述符
 * @param total_mileage 总里程(km)
 * @param trip_mileage 小计里程(km)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_mileage(serial_handle_t *handle, uint32_t total_mileage, uint32_t trip_mileage) {
    uint8_t data[8];
    // 总里程
    data[0] = (total_mileage >> 24) & 0xFF;
    data[1] = (total_mileage >> 16) & 0xFF;
    data[2] = (total_mileage >> 8) & 0xFF;
    data[3] = total_mileage & 0xFF;
    // 小计里程
    data[4] = (trip_mileage >> 24) & 0xFF;
    data[5] = (trip_mileage >> 16) & 0xFF;
    data[6] = (trip_mileage >> 8) & 0xFF;
    data[7] = trip_mileage & 0xFF;
    return uart_protocol_send_frame(handle, NO_ACK, ID_MILEAGE, data, 8);
}

/**
 * @brief 发送工厂复位命令
 * @param *handle 串口文件描述符
 * @param need_ack 是否需要应答
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_factory_reset(serial_handle_t *handle, uint8_t need_ack) {
    uint8_t data[1] = {0x01};  // 复位标志
    return uart_protocol_send_frame(handle, need_ack, ID_FACTORY_RESET, data, 1);
}

/**
 * @brief 发送屏幕控制命令
 * @param *handle 串口文件描述符
 * @param need_ack 是否需要应答
 * @param ctrl 控制值(0:关闭, 1:打开)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_screen_ctrl(serial_handle_t *handle, uint8_t need_ack, uint8_t ctrl) {
    // 将0/1控制值转换为协议中的屏幕控制常量
    uint8_t screen_value = (ctrl == 0) ? SCREEN_CTRL_OFF : SCREEN_CTRL_ON;
    uint8_t data[2] = {0x00, screen_value};  // 第一个字节设为0，不控制灯光
    return uart_protocol_send_frame(handle, need_ack, ID_LIGHT_SCREEN_CTRL, data, 2);
}

/**
 * @brief 发送灯光控制命令
 * @param *handle 串口文件描述符
 * @param need_ack 是否需要应答
 * @param ctrl 控制值(参见LIGHT_CTRL_*定义)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_light_ctrl(serial_handle_t *handle, uint8_t need_ack, uint8_t ctrl) {
    uint8_t data[2] = {ctrl, 0x00};  // 第二个字节设为0，不控制屏幕
    return uart_protocol_send_frame(handle, need_ack, ID_LIGHT_SCREEN_CTRL, data, 2);
}

/**
 * @brief 发送灯光和屏幕控制命令
 * @param *handle 串口文件描述符
 * @param need_ack 是否需要应答
 * @param light_ctrl 灯光控制值(参见LIGHT_CTRL_*定义)
 * @param screen_ctrl 屏幕控制值(参见SCREEN_CTRL_*定义)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_light_screen_ctrl(serial_handle_t *handle, uint8_t need_ack, uint8_t light_ctrl, uint8_t screen_ctrl) {
    uint8_t data[2] = {light_ctrl, screen_ctrl};
    return uart_protocol_send_frame(handle, need_ack, ID_LIGHT_SCREEN_CTRL, data, 2);
}

/**
 * @brief 发送中控控制器状态信息
 * @param *handle 串口文件描述符
 * @param controller_data 中控控制器状态数据(18字节)
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_controller_status(serial_handle_t *handle, const uint8_t *controller_data) {
    if (controller_data == NULL) {
        return -1;
    }

    return uart_protocol_send_frame(handle, NO_ACK, ID_CONTROLLER_STATUS, controller_data, CONTROLLER_STATUS_DATA_LEN);
}

/**
 * @brief 发送中控控制器状态信息(结构体版本)
 * @param *handle 串口文件描述符
 * @param status 中控控制器状态结构体指针
 * @return 发送的字节数，错误返回负值
 */
int uart_protocol_send_controller_status_struct(serial_handle_t *handle, const controller_status_t *status) {
    if (status == NULL) {
        return -1;
    }

    uint8_t data[CONTROLLER_STATUS_DATA_LEN];
    int ret = uart_protocol_pack_controller_status(status, data);
    if (ret < 0) {
        return ret;
    }

    return uart_protocol_send_frame(handle, NO_ACK, ID_CONTROLLER_STATUS, data, CONTROLLER_STATUS_DATA_LEN);
}

/**
 * @brief 解析中控控制器状态数据
 * @param data 原始数据指针(18字节)
 * @param status 解析后的状态结构体指针
 * @return 0成功，负值失败
 */
int uart_protocol_parse_controller_status(const uint8_t *data, controller_status_t *status) {
    if (data == NULL || status == NULL) {
        return -1;
    }

    // 解析各个字段
    status->controller_status1 = data[0];
    status->controller_status2 = data[1];
    status->battery_voltage_status = data[2];
    status->controller_temp = data[3];

    // 组合16位数据：母线电压 (Data4-5)
    status->bus_voltage = ((uint16_t)data[5] << 8) | data[4];

    // 组合16位数据：母线电流 (Data6-7)
    status->bus_current = ((uint16_t)data[7] << 8) | data[6];

    status->fault_code1 = data[8];
    status->fault_code2 = data[9];
    status->lead_acid_soc = data[10];

    // 组合16位数据：转把电压 (Data11-12)
    status->throttle_voltage = ((uint16_t)data[12] << 8) | data[11];

    status->enable_switches = data[13];
    status->controller_request = data[14];
    status->vehicle_status = data[15];
    status->weight = data[16];
    status->bump_level = data[17];

    return 0;
}

/**
 * @brief 将中控控制器状态结构体转换为原始数据
 * @param status 状态结构体指针
 * @param data 输出的原始数据缓冲区(至少18字节)
 * @return 0成功，负值失败
 */
int uart_protocol_pack_controller_status(const controller_status_t *status, uint8_t *data) {
    if (status == NULL || data == NULL) {
        return -1;
    }

    // 打包各个字段
    data[0] = status->controller_status1;
    data[1] = status->controller_status2;
    data[2] = status->battery_voltage_status;
    data[3] = status->controller_temp;

    // 拆分16位数据：母线电压 (Data4-5)
    data[4] = status->bus_voltage & 0xFF;        // 低字节
    data[5] = (status->bus_voltage >> 8) & 0xFF; // 高字节

    // 拆分16位数据：母线电流 (Data6-7)
    data[6] = status->bus_current & 0xFF;        // 低字节
    data[7] = (status->bus_current >> 8) & 0xFF; // 高字节

    data[8] = status->fault_code1;
    data[9] = status->fault_code2;
    data[10] = status->lead_acid_soc;

    // 拆分16位数据：转把电压 (Data11-12)
    data[11] = status->throttle_voltage & 0xFF;        // 低字节
    data[12] = (status->throttle_voltage >> 8) & 0xFF; // 高字节

    data[13] = status->enable_switches;
    data[14] = status->controller_request;
    data[15] = status->vehicle_status;
    data[16] = status->weight;
    data[17] = status->bump_level;

    return 0;
}