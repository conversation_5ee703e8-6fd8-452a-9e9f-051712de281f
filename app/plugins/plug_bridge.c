/**
 * @file plug_bridge.c
 * @brief UART-HiCar桥接插件实现（精简重构版）
 * @date 2025-07-30 17:00 GMT+8
 * <AUTHOR> (全栈开发者)
 * @note 完全重构版本，移除所有app_event依赖，只保留核心桥接功能
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// hicar_lite事件系统
#include "hicar_lite/include/eb_core.h"
#include "hicar_lite/include/eb_types.h"

// 本地插件依赖
#include "plug_uart.h"
#include "plug_hicar.h"

// 事件ID定义
#define EVENT_COMM_DATA_RECEIVED    0x1001  // UART数据接收
#define EVENT_BIZ_HICAR_COMMAND     0x2001  // HiCar命令
#define EVENT_BIZ_LIGHT_CHANGED     0x2002  // 灯光状态变化
#define EVENT_BIZ_SCREEN_CONTROL    0x2003  // 屏幕控制

// UART消息ID定义（精简版）
#define UART_MSG_ACK                0x00
#define UART_MSG_LIGHT_CTRL         0x56

// 灯光控制命令定义（精简版）
#define LIGHT_CTRL_LEFT_TURN_ON     0x01
#define LIGHT_CTRL_RIGHT_TURN_ON    0x02
#define LIGHT_CTRL_TURN_OFF         0x03
#define LIGHT_CTRL_HEADLIGHT_ON     0x04
#define LIGHT_CTRL_DOUBLE_FLASH_ON  0x05
#define LIGHT_CTRL_DOUBLE_FLASH_OFF 0x06

// 屏幕控制命令定义（精简版）
#define SCREEN_CTRL_ON              0x01
#define SCREEN_CTRL_OFF             0x02

// 命令映射结构体（精简版）
typedef struct {
    uint8_t uart_param;             // UART命令参数
    voice_ctrl_command_t hicar_cmd; // 对应的HiCar命令
} cmd_mapping_t;

// 命令映射表（精简版）
static const cmd_mapping_t cmd_map[] = {
    {LIGHT_CTRL_LEFT_TURN_ON, VC_CMD_LTS_ON},
    {LIGHT_CTRL_RIGHT_TURN_ON, VC_CMD_RTS_ON},
    {LIGHT_CTRL_TURN_OFF, VC_CMD_TS_OFF},
    {LIGHT_CTRL_HEADLIGHT_ON, VC_CMD_LIGHT_ON},
    {LIGHT_CTRL_DOUBLE_FLASH_ON, VC_CMD_FLASH_ON},
    {LIGHT_CTRL_DOUBLE_FLASH_OFF, VC_CMD_FLASH_OFF},
    {SCREEN_CTRL_ON, VC_CMD_SCREEN_ON},
    {SCREEN_CTRL_OFF, VC_CMD_SCREEN_OFF},
    {0xFF, 0xFF}  // 结束标记
};

// 桥接插件私有数据（精简版）
typedef struct {
    int initialized;                // 初始化标志
    // 状态变量（保留用于状态跟踪）
    uint8_t left_turn_state;
    uint8_t right_turn_state;
    uint8_t headlight_state;
    uint8_t screen_state;
    uint8_t flash_state;
} bridge_plugin_private_t;

static bridge_plugin_private_t g_bridge_plugin = {0};

// UART数据帧结构（精简版）
typedef struct {
    uint8_t id;
    uint8_t data[64];
    uint8_t data_len;
} uart_frame_data_t;

/**
 * @brief 查找UART参数对应的HiCar命令
 */
static voice_ctrl_command_t find_hicar_cmd(uint8_t uart_param) {
    for (int i = 0; cmd_map[i].uart_param != 0xFF; i++) {
        if (cmd_map[i].uart_param == uart_param) {
            return cmd_map[i].hicar_cmd;
        }
    }
    return 0xFF; // 未找到
}

/**
 * @brief 查找HiCar命令对应的UART参数
 */
static uint8_t find_uart_param(voice_ctrl_command_t hicar_cmd) {
    for (int i = 0; cmd_map[i].uart_param != 0xFF; i++) {
        if (cmd_map[i].hicar_cmd == hicar_cmd) {
            return cmd_map[i].uart_param;
        }
    }
    return 0xFF; // 未找到
}

/**
 * @brief 更新状态变量
 */
static void update_state(voice_ctrl_command_t cmd) {
    switch (cmd) {
        case VC_CMD_LTS_ON:
            g_bridge_plugin.left_turn_state = 1;
            break;
        case VC_CMD_RTS_ON:
            g_bridge_plugin.right_turn_state = 1;
            break;
        case VC_CMD_TS_OFF:
            g_bridge_plugin.left_turn_state = 0;
            g_bridge_plugin.right_turn_state = 0;
            break;
        case VC_CMD_LIGHT_ON:
            g_bridge_plugin.headlight_state = 1;
            break;
        case VC_CMD_LIGHT_OFF:
            g_bridge_plugin.headlight_state = 0;
            break;
        case VC_CMD_SCREEN_ON:
            g_bridge_plugin.screen_state = 1;
            break;
        case VC_CMD_SCREEN_OFF:
            g_bridge_plugin.screen_state = 0;
            break;
        case VC_CMD_FLASH_ON:
            g_bridge_plugin.flash_state = 1;
            g_bridge_plugin.left_turn_state = g_bridge_plugin.right_turn_state = 1;
            break;
        case VC_CMD_FLASH_OFF:
            g_bridge_plugin.flash_state = 0;
            g_bridge_plugin.left_turn_state = g_bridge_plugin.right_turn_state = 0;
            break;
        default:
            break;
    }
}

/**
 * @brief UART事件处理函数（精简版）
 */
static void uart_event_handler(uint16_t id, void *data, void *ctx) {
    if (!data || id != EVENT_COMM_DATA_RECEIVED) return;
    
    uart_frame_data_t *frame = (uart_frame_data_t *)data;
    printf("桥接: 收到UART数据，ID=0x%02X，长度=%d\n", frame->id, frame->data_len);
    
    // 只处理灯光控制命令
    if (frame->id == UART_MSG_LIGHT_CTRL && frame->data_len >= 1) {
        uint8_t light_param = frame->data[0];
        voice_ctrl_command_t hicar_cmd = find_hicar_cmd(light_param);
        
        if (hicar_cmd != 0xFF) {
            printf("桥接: UART->HiCar 映射 0x%02X -> %d\n", light_param, hicar_cmd);
            
            // 更新状态
            update_state(hicar_cmd);
            
            // 发布HiCar事件
            voice_ctrl_command_t *event_data = malloc(sizeof(voice_ctrl_command_t));
            if (event_data) {
                *event_data = hicar_cmd;
                if (!eb_publish(EVENT_BIZ_LIGHT_CHANGED, event_data)) {
                    free(event_data);
                }
            }
        }
    }
}

/**
 * @brief HiCar事件处理函数（精简版）
 */
static void hicar_event_handler(uint16_t id, void *data, void *ctx) {
    if (!data || id != EVENT_BIZ_HICAR_COMMAND) return;
    
    voice_ctrl_command_t *cmd = (voice_ctrl_command_t *)data;
    printf("桥接: 收到HiCar命令 %d\n", *cmd);
    
    // 查找对应的UART参数
    uint8_t uart_param = find_uart_param(*cmd);
    if (uart_param != 0xFF) {
        printf("桥接: HiCar->UART 映射 %d -> 0x%02X\n", *cmd, uart_param);
        
        // 更新状态
        update_state(*cmd);
        
        // 发送UART命令
        uint8_t data_buf[1] = {uart_param};
        plug_uart_send(UART_MSG_LIGHT_CTRL, 0, data_buf, 1);
    }
}

/**
 * @brief 桥接插件内部初始化函数
 */
static void bridge_plugin_init(void) {
    printf("桥接插件内部初始化\n");
    
    // 初始化状态变量
    memset(&g_bridge_plugin, 0, sizeof(g_bridge_plugin));
    
    // 订阅事件
    if (!eb_subscribe(EVENT_COMM_DATA_RECEIVED, uart_event_handler, NULL) ||
        !eb_subscribe(EVENT_BIZ_HICAR_COMMAND, hicar_event_handler, NULL)) {
        printf("桥接插件事件订阅失败\n");
        return;
    }
    
    g_bridge_plugin.initialized = 1;
    printf("桥接插件内部初始化完成\n");
}

/**
 * @brief 桥接插件内部清理函数
 */
static void bridge_plugin_deinit(void) {
    printf("桥接插件内部清理\n");
    
    if (!g_bridge_plugin.initialized) {
        return;
    }
    
    // 取消订阅事件
    eb_unsubscribe(EVENT_COMM_DATA_RECEIVED, uart_event_handler);
    eb_unsubscribe(EVENT_BIZ_HICAR_COMMAND, hicar_event_handler);
    
    g_bridge_plugin.initialized = 0;
    printf("桥接插件内部清理完成\n");
}

/**
 * @brief 初始化UART-HiCar桥接插件
 */
int plug_bridge_init(void) {
    printf("初始化UART-HiCar桥接插件\n");
    
    // 注册插件到事件系统
    if (!eb_plug_register("bridge", bridge_plugin_init, bridge_plugin_deinit)) {
        printf("桥接插件注册失败\n");
        return -1;
    }
    
    printf("UART-HiCar桥接插件初始化完成\n");
    return 0;
}

/**
 * @brief 清理UART-HiCar桥接插件
 */
int plug_bridge_deinit(void) {
    printf("清理UART-HiCar桥接插件\n");
    
    // 卸载插件
    if (!eb_plug_unregister("bridge")) {
        printf("桥接插件卸载失败\n");
        return -1;
    }
    
    printf("UART-HiCar桥接插件清理完成\n");
    return 0;
}

/**
 * @brief 检查桥接插件是否已初始化
 */
int plug_bridge_is_initialized(void) {
    return g_bridge_plugin.initialized;
}

/**
 * @brief 获取桥接插件状态
 */
void plug_bridge_get_state(uint8_t *left_turn, uint8_t *right_turn, 
                          uint8_t *headlight, uint8_t *screen, uint8_t *flash) {
    if (left_turn) *left_turn = g_bridge_plugin.left_turn_state;
    if (right_turn) *right_turn = g_bridge_plugin.right_turn_state;
    if (headlight) *headlight = g_bridge_plugin.headlight_state;
    if (screen) *screen = g_bridge_plugin.screen_state;
    if (flash) *flash = g_bridge_plugin.flash_state;
}
