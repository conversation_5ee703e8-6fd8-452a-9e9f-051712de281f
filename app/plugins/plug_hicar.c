/**
 * @file plug_hicar.c
 * @brief HiCar插件实现，基于hicar_lite事件系统（精简版）
 * @date 2025-07-30 15:00 GMT+8 (重构: 2025-07-30 16:45 GMT+8)
 * <AUTHOR> (全栈开发者)
 * @note 精简重构版本，移除app_event依赖，只保留核心功能
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// hicar_lite事件系统
#include "hicar_lite/include/eb_core.h"
#include "hicar_lite/include/eb_types.h"

// 事件ID定义
#define EVENT_BIZ_LIGHT_CHANGED    0x2002  // 灯光状态变化
#define EVENT_BIZ_SCREEN_CONTROL   0x2003  // 屏幕控制
#define EVENT_HICAR_STATE_CHANGED  0x2004  // HiCar状态变化

// HiCar命令枚举（精简版，移除app_event依赖）
typedef enum {
    VC_CMD_LIGHT_ON = 1,
    VC_CMD_LIGHT_OFF = 2,
    VC_CMD_LTS_ON = 3,      // 左转向灯开启
    VC_CMD_RTS_ON = 4,      // 右转向灯开启
    VC_CMD_TS_OFF = 5,      // 转向灯关闭
    VC_CMD_FLASH_ON = 6,    // 双闪开启
    VC_CMD_FLASH_OFF = 7,   // 双闪关闭
    VC_CMD_SCREEN_ON = 8,   // 屏幕开启
    VC_CMD_SCREEN_OFF = 9,  // 屏幕关闭
} voice_ctrl_command_t;

// HiCar插件私有数据（精简版）
typedef struct {
    int initialized;                // 初始化标志
    voice_ctrl_command_t last_cmd;  // 最后一个命令
} hicar_plugin_private_t;

static hicar_plugin_private_t g_hicar_plugin = {0};

// 状态报告回调函数类型
typedef void (*car_state_callback_t)(voice_ctrl_command_t state);
static car_state_callback_t g_state_callback = NULL;

// 简化的状态报告函数
static void report_car_state(voice_ctrl_command_t cmd) {
    printf("HiCar状态报告: %d\n", cmd);

    // 保存最后一个命令
    g_hicar_plugin.last_cmd = cmd;

    // 如果有回调函数，则调用
    if (g_state_callback) {
        g_state_callback(cmd);
    }

    // 发布状态变化事件
    voice_ctrl_command_t *event_data = malloc(sizeof(voice_ctrl_command_t));
    if (event_data) {
        *event_data = cmd;
        if (!eb_publish(EVENT_HICAR_STATE_CHANGED, event_data)) {
            free(event_data);
        }
    }
}

// HiCar事件处理器（精简版）
static void hicar_event_handler(uint16_t id, void *data, void *ctx) {
    if (!data) return;

    printf("HiCar插件收到事件: ID=0x%04X\n", id);

    switch (id) {
        case EVENT_BIZ_LIGHT_CHANGED: {
            voice_ctrl_command_t *cmd = (voice_ctrl_command_t *)data;

            switch (*cmd) {
                case VC_CMD_LIGHT_ON:
                    printf("HiCar: 灯光开启\n");
                    break;

                case VC_CMD_LIGHT_OFF:
                    printf("HiCar: 灯光关闭\n");
                    break;

                case VC_CMD_FLASH_ON:
                    printf("HiCar: 双闪开启\n");
                    break;

                case VC_CMD_FLASH_OFF:
                    printf("HiCar: 双闪关闭\n");
                    break;

                case VC_CMD_LTS_ON:
                    printf("HiCar: 左转向灯开启\n");
                    report_car_state(VC_CMD_LTS_ON);
                    break;

                case VC_CMD_RTS_ON:
                    printf("HiCar: 右转向灯开启\n");
                    report_car_state(VC_CMD_RTS_ON);
                    break;

                case VC_CMD_TS_OFF:
                    printf("HiCar: 转向灯关闭\n");
                    report_car_state(VC_CMD_TS_OFF);
                    break;

                default:
                    printf("HiCar: 未知灯光命令: %d\n", *cmd);
                    break;
            }
            break;
        }

        case EVENT_BIZ_SCREEN_CONTROL: {
            voice_ctrl_command_t *cmd = (voice_ctrl_command_t *)data;

            switch (*cmd) {
                case VC_CMD_SCREEN_ON:
                    printf("HiCar: 屏幕开启\n");
                    break;

                case VC_CMD_SCREEN_OFF:
                    printf("HiCar: 屏幕关闭\n");
                    break;

                default:
                    printf("HiCar: 未知屏幕命令: %d\n", *cmd);
                    break;
            }
            break;
        }

        default:
            break;
    }
}

// HiCar插件内部初始化函数
static void hicar_plugin_init(void) {
    printf("HiCar插件内部初始化\n");

    // 订阅事件
    if (!eb_subscribe(EVENT_BIZ_LIGHT_CHANGED, hicar_event_handler, NULL) ||
        !eb_subscribe(EVENT_BIZ_SCREEN_CONTROL, hicar_event_handler, NULL)) {
        printf("HiCar插件事件订阅失败\n");
        return;
    }

    g_hicar_plugin.initialized = 1;
    g_hicar_plugin.last_cmd = 0;
    printf("HiCar插件内部初始化完成\n");
}

// HiCar插件内部清理函数
static void hicar_plugin_deinit(void) {
    printf("HiCar插件内部清理\n");

    if (!g_hicar_plugin.initialized) {
        return;
    }

    // 取消订阅事件
    eb_unsubscribe(EVENT_BIZ_LIGHT_CHANGED, hicar_event_handler);
    eb_unsubscribe(EVENT_BIZ_SCREEN_CONTROL, hicar_event_handler);

    g_hicar_plugin.initialized = 0;
    g_state_callback = NULL;
    printf("HiCar插件内部清理完成\n");
}

// 初始化HiCar插件
int plug_hicar_init(void) {
    printf("初始化HiCar插件\n");

    // 注册插件到事件系统
    if (!eb_plug_register("hicar", hicar_plugin_init, hicar_plugin_deinit)) {
        printf("HiCar插件注册失败\n");
        return -1;
    }

    printf("HiCar插件初始化完成\n");
    return 0;
}

// 清理HiCar插件
int plug_hicar_deinit(void) {
    printf("清理HiCar插件\n");

    // 卸载插件
    if (!eb_plug_unregister("hicar")) {
        printf("HiCar插件卸载失败\n");
        return -1;
    }

    printf("HiCar插件清理完成\n");
    return 0;
}

// 注册状态报告回调函数
int plug_hicar_register_callback(car_state_callback_t callback) {
    if (!g_hicar_plugin.initialized) {
        printf("HiCar插件未初始化\n");
        return -1;
    }

    g_state_callback = callback;
    printf("HiCar状态回调已注册\n");
    return 0;
}

// 发送语音控制命令
int plug_hicar_send_command(voice_ctrl_command_t cmd) {
    if (!g_hicar_plugin.initialized) {
        printf("HiCar插件未初始化\n");
        return -1;
    }

    printf("发送HiCar命令: %d\n", cmd);

    // 根据命令类型选择事件ID
    uint16_t event_id;
    switch (cmd) {
        case VC_CMD_SCREEN_ON:
        case VC_CMD_SCREEN_OFF:
            event_id = EVENT_BIZ_SCREEN_CONTROL;
            break;
        default:
            event_id = EVENT_BIZ_LIGHT_CHANGED;
            break;
    }

    // 创建事件数据
    voice_ctrl_command_t *event_data = malloc(sizeof(voice_ctrl_command_t));
    if (!event_data) {
        printf("内存分配失败\n");
        return -1;
    }

    *event_data = cmd;

    // 发布事件
    if (!eb_publish(event_id, event_data)) {
        printf("命令发布失败\n");
        free(event_data);
        return -1;
    }

    return 0;
}

// 获取最后一个命令
voice_ctrl_command_t plug_hicar_get_last_command(void) {
    return g_hicar_plugin.last_cmd;
}

// 检查插件是否已初始化
int plug_hicar_is_initialized(void) {
    return g_hicar_plugin.initialized;
}

// 测试HiCar功能（精简版）
void plug_hicar_test(void) {
    printf("开始测试HiCar功能\n");

    if (!g_hicar_plugin.initialized) {
        printf("HiCar插件未初始化，无法测试\n");
        return;
    }

    // 测试各种命令
    plug_hicar_send_command(VC_CMD_LIGHT_ON);
    plug_hicar_send_command(VC_CMD_LTS_ON);
    plug_hicar_send_command(VC_CMD_RTS_ON);
    plug_hicar_send_command(VC_CMD_TS_OFF);
    plug_hicar_send_command(VC_CMD_FLASH_ON);
    plug_hicar_send_command(VC_CMD_FLASH_OFF);
    plug_hicar_send_command(VC_CMD_SCREEN_ON);
    plug_hicar_send_command(VC_CMD_SCREEN_OFF);

    printf("HiCar功能测试完成\n");
}
