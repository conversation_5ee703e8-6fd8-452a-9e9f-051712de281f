/**
 * @file plugins_simple_test.c
 * @brief 插件系统简单测试（精简版）
 * @date 2025-07-30 17:10 GMT+8
 * <AUTHOR> (全栈开发者)
 * @note 精简版插件测试，移除app_event依赖
 */

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

// hicar_lite事件系统
#include "hicar_lite/include/eb_core.h"
#include "hicar_lite/include/eb_types.h"

// 插件头文件
#include "plug_uart.h"
#include "plug_hicar.h"
#include "plug_bridge.h"

/**
 * @brief 测试事件回调函数
 */
static void test_event_callback(uint16_t id, void *data, void *ctx) {
    printf("测试回调收到事件: ID=0x%04X\n", id);
}

/**
 * @brief 测试hicar_lite事件系统基本功能
 */
static int test_event_system(void) {
    printf("\n=== 测试hicar_lite事件系统 ===\n");
    
    // 初始化事件总线
    if (!eb_init()) {
        printf("❌ 事件总线初始化失败\n");
        return -1;
    }
    printf("✅ 事件总线初始化成功\n");
    
    // 订阅测试事件
    if (!eb_subscribe(0x1001, test_event_callback, NULL)) {
        printf("❌ 事件订阅失败\n");
        eb_deinit();
        return -1;
    }
    printf("✅ 事件订阅成功\n");
    
    // 发布测试事件
    char test_data[] = "test data";
    if (!eb_publish(0x1001, test_data)) {
        printf("❌ 事件发布失败\n");
        eb_deinit();
        return -1;
    }
    printf("✅ 事件发布成功\n");
    
    // 分发事件
    eb_dispatch();
    printf("✅ 事件分发完成\n");
    
    // 取消订阅
    eb_unsubscribe(0x1001, test_event_callback);
    printf("✅ 取消订阅成功\n");
    
    return 0;
}

/**
 * @brief 测试HiCar插件功能
 */
static int test_hicar_plugin(void) {
    printf("\n=== 测试HiCar插件 ===\n");
    
    // 初始化HiCar插件
    if (plug_hicar_init() != 0) {
        printf("❌ HiCar插件初始化失败\n");
        return -1;
    }
    printf("✅ HiCar插件初始化成功\n");
    
    // 测试HiCar功能
    plug_hicar_test();
    printf("✅ HiCar功能测试完成\n");
    
    // 清理HiCar插件
    if (plug_hicar_deinit() != 0) {
        printf("❌ HiCar插件清理失败\n");
        return -1;
    }
    printf("✅ HiCar插件清理成功\n");
    
    return 0;
}

/**
 * @brief 测试UART插件功能
 */
static int test_uart_plugin(void) {
    printf("\n=== 测试UART插件 ===\n");
    
    // 初始化UART插件（使用虚拟设备）
    if (plug_uart_init("/dev/null", 9600) != 0) {
        printf("❌ UART插件初始化失败\n");
        return -1;
    }
    printf("✅ UART插件初始化成功\n");
    
    // 测试UART发送
    uint8_t test_data[] = {0x01, 0x02, 0x03};
    if (plug_uart_send(0x56, 0, test_data, 3) < 0) {
        printf("❌ UART发送失败\n");
    } else {
        printf("✅ UART发送成功\n");
    }
    
    // 清理UART插件
    if (plug_uart_deinit() != 0) {
        printf("❌ UART插件清理失败\n");
        return -1;
    }
    printf("✅ UART插件清理成功\n");
    
    return 0;
}

/**
 * @brief 测试桥接插件功能
 */
static int test_bridge_plugin(void) {
    printf("\n=== 测试桥接插件 ===\n");
    
    // 初始化桥接插件
    if (plug_bridge_init() != 0) {
        printf("❌ 桥接插件初始化失败\n");
        return -1;
    }
    printf("✅ 桥接插件初始化成功\n");
    
    // 测试状态获取
    uint8_t left, right, head, screen, flash;
    plug_bridge_get_state(&left, &right, &head, &screen, &flash);
    printf("桥接插件状态: 左转=%d, 右转=%d, 大灯=%d, 屏幕=%d, 双闪=%d\n", 
           left, right, head, screen, flash);
    
    // 清理桥接插件
    if (plug_bridge_deinit() != 0) {
        printf("❌ 桥接插件清理失败\n");
        return -1;
    }
    printf("✅ 桥接插件清理成功\n");
    
    return 0;
}

/**
 * @brief 主测试函数
 */
int test_plugins_simple(void) {
    printf("开始插件系统简单测试...\n");
    
    int result = 0;
    
    // 测试事件系统
    if (test_event_system() != 0) {
        printf("❌ 事件系统测试失败\n");
        result = -1;
    }
    
    // 测试HiCar插件
    if (test_hicar_plugin() != 0) {
        printf("❌ HiCar插件测试失败\n");
        result = -1;
    }
    
    // 测试UART插件
    if (test_uart_plugin() != 0) {
        printf("❌ UART插件测试失败\n");
        result = -1;
    }
    
    // 测试桥接插件
    if (test_bridge_plugin() != 0) {
        printf("❌ 桥接插件测试失败\n");
        result = -1;
    }
    
    // 清理事件总线
    eb_deinit();
    
    if (result == 0) {
        printf("\n🎉 所有插件测试通过！\n");
    } else {
        printf("\n❌ 部分测试失败，请检查错误信息\n");
    }
    
    return result;
}

/**
 * @brief 独立测试程序入口（可选）
 */
#ifdef PLUGINS_TEST_STANDALONE
int main(void) {
    return test_plugins_simple();
}
#endif
