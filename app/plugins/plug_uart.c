/**
 * @file plug_uart.c
 * @brief UART插件实现，基于hicar_lite事件系统和uart_api
 * @date 2025-07-30 14:45 GMT+8 (重构: 2025-07-30 18:00 GMT+8)
 * <AUTHOR> (全栈开发者)
 * @note 使用app/api/uart_api.c提供的完整UART协议栈
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

// hicar_lite事件系统
#include "hicar_lite/include/eb_core.h"
#include "hicar_lite/include/eb_types.h"

// UART API
#include "../api/uart_api.h"

// 事件ID定义
#define EVENT_COMM_DATA_RECEIVED    0x1001  // UART数据接收
#define EVENT_SYS_START            0x1000  // 系统启动
#define EVENT_BIZ_VEHICLE_STATUS   0x3001  // 车辆状态
#define EVENT_BIZ_SPEED_INFO       0x3002  // 速度信息

// UART插件私有数据（使用uart_api）
typedef struct {
    uart_protocol_handle_t handle;  // UART协议句柄
    int initialized;                // 初始化标志
    char device_path[64];           // 设备路径
    int baud_rate;                  // 波特率
} uart_plugin_private_t;

static uart_plugin_private_t g_uart_plugin = {0};

// UART数据帧结构（与uart_api兼容）
typedef struct {
    uint8_t id;
    uint8_t data[256];              // 使用标准缓冲区大小
    uint16_t data_len;
} uart_frame_data_t;

// UART消息回调函数（使用uart_api的回调机制）
static void uart_message_callback(uint8_t id, const uint8_t *data, uint16_t data_len, void *user_data) {
    // 检查参数有效性
    if (data_len > 0 && !data) {
        printf("UART消息回调：无效参数，数据为NULL但长度为%d\n", data_len);
        return;
    }

    printf("UART消息回调：ID=0x%02X，长度=%d\n", id, data_len);

    // 根据不同的命令ID设置不同的事件类型
    uint16_t event_id;
    switch (id) {
        case UART_MSG_POWER_SYNC:
            event_id = EVENT_SYS_START;
            break;
        case UART_MSG_HEARTBEAT:
            // 心跳包，可以不创建事件
            return;
        case UART_MSG_GEAR:
            event_id = EVENT_BIZ_VEHICLE_STATUS;
            break;
        case UART_MSG_SPEED:
            event_id = EVENT_BIZ_SPEED_INFO;
            break;
        case UART_MSG_RPM:
            event_id = EVENT_BIZ_SPEED_INFO;  // 转速也归类为速度信息
            break;
        case UART_MSG_CONTROLLER_STATUS:
            event_id = EVENT_BIZ_VEHICLE_STATUS;  // 中控控制器状态归类为车辆状态
            break;
        case UART_MSG_ACK:
            // ACK消息也创建COMM_DATA_RECEIVED事件
            event_id = EVENT_COMM_DATA_RECEIVED;
            break;
        default:
            event_id = EVENT_COMM_DATA_RECEIVED;
            break;
    }

    // 创建事件数据结构体
    uart_frame_data_t *frame_data = malloc(sizeof(uart_frame_data_t));
    if (!frame_data) {
        printf("UART消息回调：内存分配失败\n");
        return;
    }

    // 初始化结构体，避免未初始化的内存
    memset(frame_data, 0, sizeof(uart_frame_data_t));

    frame_data->id = id;
    frame_data->data_len = data_len;

    if (data && data_len > 0) {
        // 确保不会越界
        if (data_len > sizeof(frame_data->data)) {
            data_len = sizeof(frame_data->data);
            frame_data->data_len = data_len;
            printf("UART消息回调：数据长度过长，已截断至%d字节\n", data_len);
        }
        memcpy(frame_data->data, data, data_len);

        // 专门解析0x67中控控制器状态数据
        if (id == UART_MSG_CONTROLLER_STATUS && data_len >= 18) {
            printf("=== 中控控制器状态数据解析 ===\n");
            // 解析控制器状态1 (Data0)
            uint8_t status1 = data[0];
            printf("控制器状态1: 0x%02X\n", status1);
            printf("================================\n");
        }
    }

    // 使用hicar_lite事件系统发布事件
    bool ret = eb_publish(event_id, frame_data);
    if (!ret) {
        printf("事件发布失败：事件ID=0x%04X, UART ID=0x%02X\n", event_id, id);
        free(frame_data);
    } else {
        printf("事件已发布：事件ID=0x%04X, UART ID=0x%02X\n", event_id, id);
    }
}

// UART插件内部初始化函数
static void uart_plugin_init(void) {
    printf("UART插件内部初始化\n");
    // 内部初始化逻辑已在plug_uart_init中完成
}

// UART插件内部清理函数
static void uart_plugin_deinit(void) {
    printf("UART插件内部清理\n");

    if (!g_uart_plugin.initialized) {
        return;
    }

    // 清理UART协议栈
    if (g_uart_plugin.handle) {
        uart_protocol_deinit(g_uart_plugin.handle);
        g_uart_plugin.handle = NULL;
    }

    g_uart_plugin.initialized = 0;
}

// 定时处理函数（使用uart_api）
static void uart_plugin_process(void) {
    if (!g_uart_plugin.initialized || !g_uart_plugin.handle) {
        return;
    }

    // 处理UART协议栈
    uart_protocol_process(g_uart_plugin.handle);

    // 定期发送心跳包
    static int heartbeat_counter = 0;
    heartbeat_counter++;

    if (heartbeat_counter >= 100) { // 每100次调用发送一次心跳
        uart_protocol_send_heartbeat_msg(g_uart_plugin.handle);
        heartbeat_counter = 0;
    }
}

// 简化的UART初始化函数
int plug_uart_init(const char *device, int baud_rate) {
    printf("UART插件初始化：设备=%s, 波特率=%d\n", device, baud_rate);

    if (!device || baud_rate <= 0) {
        printf("无效参数\n");
        return -1;
    }

    // 如果已经初始化，先清理
    if (g_uart_plugin.initialized) {
        plug_uart_deinit();
    }

    // 保存配置
    strncpy(g_uart_plugin.device_path, device, sizeof(g_uart_plugin.device_path) - 1);
    g_uart_plugin.baud_rate = baud_rate;

    // 检查设备文件是否存在
    if (access(device, F_OK) != 0) {
        printf("设备文件不存在: %s，使用模拟模式\n", device);
        // 在模拟模式下，我们仍然注册插件，但不初始化实际的UART
        g_uart_plugin.handle = NULL;
        g_uart_plugin.initialized = 1;

        // 注册插件到hicar_lite事件系统
        if (!eb_plug_register("uart", uart_plugin_init, uart_plugin_deinit)) {
            printf("UART插件注册失败\n");
            g_uart_plugin.initialized = 0;
            return -1;
        }

        printf("UART插件初始化完成（模拟模式）\n");
        return 0;
    }

    // 初始化UART协议栈
    printf("正在初始化UART协议栈...\n");
    fflush(stdout);
    g_uart_plugin.handle = uart_protocol_init(device, baud_rate, uart_message_callback, NULL);
    if (!g_uart_plugin.handle) {
        printf("UART协议栈初始化失败，使用模拟模式\n");
        fflush(stdout);
        // 在模拟模式下，我们仍然注册插件，但不初始化实际的UART
        g_uart_plugin.handle = NULL;
        g_uart_plugin.initialized = 1;

        // 注册插件到hicar_lite事件系统
        if (!eb_plug_register("uart", uart_plugin_init, uart_plugin_deinit)) {
            printf("UART插件注册失败\n");
            g_uart_plugin.initialized = 0;
            return -1;
        }

        printf("UART插件初始化完成（模拟模式）\n");
        fflush(stdout);
        return 0;
    }
    printf("UART协议栈初始化成功\n");
    fflush(stdout);

    g_uart_plugin.initialized = 1;

    // 注册插件到hicar_lite事件系统
    if (!eb_plug_register("uart", uart_plugin_init, uart_plugin_deinit)) {
        printf("UART插件注册失败\n");
        uart_protocol_deinit(g_uart_plugin.handle);
        g_uart_plugin.handle = NULL;
        g_uart_plugin.initialized = 0;
        return -1;
    }

    // 发送上电同步信息
    printf("准备发送上电同步信息\n");
    fflush(stdout);

    uart_error_t ret = uart_protocol_send_power_sync_msg(g_uart_plugin.handle, 0);
    printf("上电同步信息发送调用完成，返回值: %d\n", ret);
    fflush(stdout);

    if (ret != UART_ERROR_NONE) {
        printf("上电同步信息发送失败: %d\n", ret);
        fflush(stdout);
    } else {
        printf("上电同步信息发送成功\n");
        fflush(stdout);
    }

    printf("UART插件初始化完成\n");
    fflush(stdout);
    return 0;
}

// UART发送函数（使用uart_api）
int plug_uart_send(uint8_t id, uint8_t need_ack, const uint8_t *data, uint8_t data_len) {
    printf("UART发送数据: ID=0x%02X, 长度=%d\n", id, data_len);

    if (!g_uart_plugin.initialized || !g_uart_plugin.handle) {
        printf("UART插件未初始化\n");
        return -1;
    }

    // 检查数据有效性
    if (data_len > 0 && !data) {
        printf("无效参数: 数据为NULL但长度为%d\n", data_len);
        return -1;
    }

    // 根据不同的ID调用不同的API函数
    uart_error_t ret = UART_ERROR_NONE;

    switch (id) {
        case UART_MSG_POWER_SYNC:
            printf("发送上电同步消息\n");
            ret = uart_protocol_send_power_sync_msg(g_uart_plugin.handle, need_ack);
            break;
        case UART_MSG_HEARTBEAT:
            printf("发送心跳包\n");
            ret = uart_protocol_send_heartbeat_msg(g_uart_plugin.handle);
            break;
        case UART_MSG_GEAR:
            if (data && data_len > 0) {
                printf("发送挡位消息: %d\n", data[0]);
                ret = uart_protocol_send_gear_msg(g_uart_plugin.handle, data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_SPEED:
            if (data && data_len > 0) {
                printf("发送车速消息: %d km/h\n", data[0]);
                ret = uart_protocol_send_speed_msg(g_uart_plugin.handle, data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_RPM:
            if (data && data_len >= 2) {
                uint16_t rpm = (data[0] << 8) | data[1];  // 高字节在前
                printf("发送转速消息: %d RPM\n", rpm);
                ret = uart_protocol_send_rpm_msg(g_uart_plugin.handle, rpm);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_LIGHT_SCREEN_CTRL:
            if (data && data_len > 0) {
                // 灯光控制参数必须存在
                light_control_cmd_t light_ctrl = (data_len >= 1) ? (light_control_cmd_t)data[0] : 0;
                // 屏幕控制参数可选
                screen_control_cmd_t screen_ctrl = (data_len >= 2) ? (screen_control_cmd_t)data[1] : 0;

                printf("发送灯光和屏幕控制消息: 灯光=%d, 屏幕=%d\n", light_ctrl, screen_ctrl);
                ret = uart_protocol_send_light_screen_ctrl_msg(g_uart_plugin.handle, need_ack,
                                                             light_ctrl, screen_ctrl);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_REMAIN_RANGE:
            if (data && data_len > 0) {
                printf("发送剩余续航里程消息: %d km\n", data[0]);
                ret = uart_protocol_send_remain_range_msg(g_uart_plugin.handle, data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_FACTORY_RESET:
            printf("发送工厂复位消息\n");
            ret = uart_protocol_send_factory_reset_msg(g_uart_plugin.handle, need_ack);
            break;
        case UART_MSG_MCU_VERSION:
            if (data && data_len > 0) {
                // 确保字符串以'\0'结尾
                char *version = (char *)malloc(data_len + 1);
                if (version) {
                    memcpy(version, data, data_len);
                    version[data_len] = '\0';
                    printf("发送MCU版本消息: %s\n", version);
                    ret = uart_protocol_send_mcu_version_msg(g_uart_plugin.handle, version);
                    free(version);
                } else {
                    ret = UART_ERROR_MEMORY;
                }
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        default:
            printf("未支持的消息ID: %02X\n", id);
            return -1;
    }

    printf("UART发送结果: %d\n", ret);
    return (ret == UART_ERROR_NONE) ? data_len : -1;
}

// 清理UART插件（使用uart_api）
int plug_uart_deinit(void) {
    printf("清理UART插件\n");

    if (!g_uart_plugin.initialized) {
        return 0;
    }

    // 卸载插件
    eb_plug_unregister("uart");

    // 清理UART协议栈
    if (g_uart_plugin.handle) {
        uart_protocol_deinit(g_uart_plugin.handle);
        g_uart_plugin.handle = NULL;
    }

    g_uart_plugin.initialized = 0;
    memset(&g_uart_plugin, 0, sizeof(g_uart_plugin));

    printf("UART插件已清理\n");
    return 0;
}

// 获取UART插件状态
int plug_uart_is_initialized(void) {
    return g_uart_plugin.initialized;
}

// 处理插件定时任务（供外部调用）
void plug_uart_process(void) {
    uart_plugin_process();
}
