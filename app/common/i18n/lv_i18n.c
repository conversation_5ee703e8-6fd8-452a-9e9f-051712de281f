/**
 * @file lv_i18n.c
 * @brief LVGL国际化支持模块实现
 */

/*********************
 *      INCLUDES
 *********************/
#include "lv_i18n.h"
#include <stdarg.h>
#include <stdio.h>
#include <string.h>

/*********************
 *      DEFINES
 *********************/

#if LV_I18N_DEBUG_ENABLE
#define LV_I18N_DEBUG(fmt, ...) LV_LOG_USER("[I18N] " fmt, ##__VA_ARGS__)
#else
#define LV_I18N_DEBUG(fmt, ...)
#endif

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void _i18n_text_refresh_cb(lv_obj_t * obj, void * user_data);
static bool _is_obj_label(const lv_obj_t * obj);
static void _set_label_font(lv_obj_t * label, const lv_font_t * font);
static void _dump_lang_resources(void);

/**********************
 *  STATIC VARIABLES
 **********************/
static lv_i18n_lang_t s_current_lang;                          /* 当前语言ID */
static char s_text_buf[LV_I18N_MAX_TEXT_LEN];                  /* 格式化文本缓冲区 */
static const lv_i18n_language_t * s_languages;                 /* 语言配置数组 */
static uint8_t s_language_cnt;                                 /* 语言数量 */
static bool s_is_initialized = false;                          /* 初始化标志 */

/* 外部声明对象类 */
extern const lv_obj_class_t lv_label_class;

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

/**
 * 输出当前语言资源，用于调试
 */
static void _dump_lang_resources(void)
{
    if (!s_is_initialized) return;
    
    const lv_i18n_language_t * lang = &s_languages[s_current_lang];
    LV_I18N_DEBUG("Dumping resources for language: %s (ID: %d)", 
                 lang->language_name, s_current_lang);
    
    const lv_i18n_lang_pack_t * lang_pack = lang->lang_pack;
    
    for (uint32_t i = 0; lang_pack[i].key != NULL; i++) {
        LV_I18N_DEBUG("  [%s] = \"%s\"", lang_pack[i].key, lang_pack[i].value);
    }
}

/**
 * 初始化多语言模块
 * 
 * @param languages 语言配置数组
 * @param default_lang 默认语言ID
 * @return LV_RES_OK: 初始化成功; LV_RES_INV: 初始化失败
 */
lv_res_t lv_i18n_init(const lv_i18n_language_t * languages, lv_i18n_lang_t default_lang)
{
    if (languages == NULL) {
        LV_LOG_ERROR("Language pack cannot be NULL");
        return LV_RES_INV;
    }
    
    /* 计算语言数量 */
    s_language_cnt = 0;
    while (languages[s_language_cnt].language_name != NULL && s_language_cnt < LV_I18N_LANGUAGE_CNT) {
        s_language_cnt++;
    }
    
    if (s_language_cnt == 0) {
        LV_LOG_ERROR("No valid languages found");
        return LV_RES_INV;
    }
    
    if (default_lang >= s_language_cnt) {
        LV_LOG_ERROR("Invalid default language ID");
        return LV_RES_INV;
    }
    
    /* 保存语言配置 */
    s_languages = languages;
    s_current_lang = default_lang;
    s_is_initialized = true;
    
    LV_I18N_DEBUG("多语言模块初始化完成，默认语言: %s (ID: %d)", 
                 languages[default_lang].language_name, default_lang);
    
    /* 输出资源以验证加载 */
    _dump_lang_resources();
    
    return LV_RES_OK;
}

/**
 * 获取当前语言ID
 * 
 * @return 当前语言ID
 */
lv_i18n_lang_t lv_i18n_get_current_lang(void)
{
    return s_current_lang;
}

/**
 * 切换到指定语言
 * 
 * @param lang_id 目标语言ID
 * @return LV_RES_OK: 切换成功; LV_RES_INV: 切换失败
 */
lv_res_t lv_i18n_set_lang(lv_i18n_lang_t lang_id)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("I18N module not initialized");
        return LV_RES_INV;
    }
    
    if (lang_id >= s_language_cnt) {
        LV_LOG_ERROR("Invalid language ID: %d", lang_id);
        return LV_RES_INV;
    }
    
    LV_I18N_DEBUG("切换语言: %s -> %s (ID: %d -> %d)", 
                 s_languages[s_current_lang].language_name, 
                 s_languages[lang_id].language_name,
                 s_current_lang, lang_id);
    
    s_current_lang = lang_id;
    
    /* 输出资源以验证切换 */
    _dump_lang_resources();
    
    /* 刷新UI显示 */
    lv_i18n_refresh_ui();
    
    return LV_RES_OK;
}

/**
 * 获取指定键对应的翻译文本
 * 
 * @param key 文本键
 * @return 当前语言下对应的翻译文本，若未找到则返回键名
 */
const char * lv_i18n_get_text(const char * key)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("I18N module not initialized");
        return key;
    }
    
    if (key == NULL) return "";
    
    LV_I18N_DEBUG("查找文本键: \"%s\" (语言: %s)", 
                 key, s_languages[s_current_lang].language_name);
    
    const lv_i18n_lang_pack_t * lang_pack = s_languages[s_current_lang].lang_pack;
    
    /* 在当前语言资源表中查找键 */
    for (uint32_t i = 0; lang_pack[i].key != NULL; i++) {
        if (strcmp(lang_pack[i].key, key) == 0) {
            LV_I18N_DEBUG("  找到文本: \"%s\"", lang_pack[i].value);
            return lang_pack[i].value;
        }
    }
    
    /* 如果当前语言不是默认语言(英语)，尝试在默认语言中查找 */
    if (s_current_lang != 0) {
        LV_I18N_DEBUG("  在当前语言中未找到，尝试默认语言回退");
        
        const lv_i18n_lang_pack_t * default_lang_pack = s_languages[0].lang_pack;
        for (uint32_t i = 0; default_lang_pack[i].key != NULL; i++) {
            if (strcmp(default_lang_pack[i].key, key) == 0) {
                LV_I18N_DEBUG("  在默认语言中找到: \"%s\"", default_lang_pack[i].value);
                return default_lang_pack[i].value;
            }
        }
    }
    
    LV_I18N_DEBUG("  未找到翻译，使用键名作为文本");
    /* 找不到翻译时返回原始键名 */
    return key;
}

/**
 * 格式化方式获取翻译文本
 * 
 * @param key 文本键
 * @param ... 格式化参数
 * @return 格式化后的翻译文本
 */
const char * lv_i18n_get_text_fmt(const char * key, ...)
{
    const char * text = lv_i18n_get_text(key);
    
    va_list args;
    va_start(args, key);
    vsnprintf(s_text_buf, sizeof(s_text_buf), text, args);
    va_end(args);
    
    LV_I18N_DEBUG("格式化文本: \"%s\" -> \"%s\"", text, s_text_buf);
    
    return s_text_buf;
}

/**
 * 获取当前语言的字体
 * 
 * @param font_size 字体大小类型
 * @return 字体指针
 */
const lv_font_t * lv_i18n_get_font(lv_i18n_font_size_t font_size)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("I18N module not initialized");
        return LV_FONT_DEFAULT;
    }
    
    const lv_i18n_font_t * fonts = &s_languages[s_current_lang].fonts;
    
    switch (font_size) {
        case LV_I18N_FONT_SMALL:
            return fonts->small_font ? fonts->small_font : LV_FONT_DEFAULT;
        case LV_I18N_FONT_LARGE:
            return fonts->large_font ? fonts->large_font : LV_FONT_DEFAULT;
        case LV_I18N_FONT_NORMAL:
        default:
            return fonts->normal_font ? fonts->normal_font : LV_FONT_DEFAULT;
    }
}

/**
 * 设置标签的字体
 */
static void _set_label_font(lv_obj_t * label, const lv_font_t * font)
{
    if (label == NULL || font == NULL) return;
    
    const lv_font_t * current_font = lv_obj_get_style_text_font(label, LV_PART_MAIN);
    
    /* 如果字体相同则不更新 */
    if (current_font == font) return;
    
    LV_I18N_DEBUG("更新标签字体: %p -> %p", current_font, font);
    lv_obj_set_style_text_font(label, font, LV_PART_MAIN);
}

/**
 * 设置标签的语言字体
 * 
 * @param label 标签对象指针
 * @param font_size 字体大小类型
 */
void lv_i18n_set_label_font(lv_obj_t * label, lv_i18n_font_size_t font_size)
{
    if (!s_is_initialized || !_is_obj_label(label)) return;
    
    const lv_font_t * font = lv_i18n_get_font(font_size);
    _set_label_font(label, font);
}

/**
 * 检查对象是否为标签(lv_label)
 * 使用LVGL的类型检查API
 */
static bool _is_obj_label(const lv_obj_t * obj)
{
    if (obj == NULL) return false;
    
    /* 使用LVGL标准API检查对象类型 */
    return lv_obj_check_type(obj, &lv_label_class);
}

/**
 * 尝试处理作为多语言标签的对象
 * 返回true表示成功处理，false表示不是多语言标签
 */
static bool _try_process_i18n_label(lv_obj_t * obj)
{
    /* 检查对象是否为标签 */
    if (!_is_obj_label(obj)) return false;
    
    /* 获取标签文本 */
    const char * old_text = lv_label_get_text(obj);
    
    /* 检查是否是标记为多语言的文本（以#开头） */
    if (old_text && old_text[0] == '#') {
        char key[64];
        strncpy(key, old_text + 1, sizeof(key) - 1);  /* 移除#前缀，获取键名 */
        key[sizeof(key) - 1] = '\0';  /* 确保字符串结束 */
        
        LV_I18N_DEBUG("处理多语言标签: \"%s\"", old_text);
        
        const char * translated = lv_i18n_get_text(key);
        LV_I18N_DEBUG("  更新标签文本: \"%s\" -> \"%s\"", key, translated);
        
        /* 更新文本 */
        lv_label_set_text(obj, translated);
        
        /* 更新字体 - 使用当前语言的标准字体 */
        _set_label_font(obj, lv_i18n_get_font(LV_I18N_FONT_NORMAL));
        
        return true;
    }
    
    return false;
}

/**
 * 将文本对象的文本设置为翻译文本
 * 
 * @param obj 文本对象指针 (如label)
 * @param key 文本键
 */
void lv_i18n_set_text(lv_obj_t * obj, const char * key)
{
    if (!s_is_initialized || !_is_obj_label(obj) || key == NULL) return;
    
    const char * translated = lv_i18n_get_text(key);
    lv_label_set_text(obj, translated);
    
    /* 更新字体 */
    _set_label_font(obj, lv_i18n_get_font(LV_I18N_FONT_NORMAL));
}

/**
 * 将文本对象的文本设置为格式化的翻译文本
 * 
 * @param obj 文本对象指针 (如label)
 * @param key 文本键
 * @param ... 格式化参数
 */
void lv_i18n_set_text_fmt(lv_obj_t * obj, const char * key, ...)
{
    if (!s_is_initialized || !_is_obj_label(obj) || key == NULL) return;
    
    const char * text = lv_i18n_get_text(key);
    
    va_list args;
    va_start(args, key);
    vsnprintf(s_text_buf, sizeof(s_text_buf), text, args);
    va_end(args);
    
    lv_label_set_text(obj, s_text_buf);
    
    /* 更新字体 */
    _set_label_font(obj, lv_i18n_get_font(LV_I18N_FONT_NORMAL));
}

/**
 * 刷新UI文本回调函数
 * 递归遍历所有对象，刷新标记为多语言的文本
 */
static void _i18n_text_refresh_cb(lv_obj_t * obj, void * user_data)
{
    if (obj == NULL) return;
    
    static uint32_t processed = 0;
    static uint32_t updated = 0;
    
    processed++;
    
    /* 尝试处理任何可能的标签对象 */
    if (_try_process_i18n_label(obj)) {
        updated++;
    }
    
    /* 递归处理子对象 */
    uint32_t child_cnt = lv_obj_get_child_cnt(obj);
    for (uint32_t i = 0; i < child_cnt; i++) {
        lv_obj_t * child = lv_obj_get_child(obj, i);
        if (child) {
            _i18n_text_refresh_cb(child, user_data);
        }
    }
    
    /* 在最顶层调用完成后输出统计信息 */
    if (obj == lv_scr_act()) {
        LV_I18N_DEBUG("UI刷新完成，处理对象: %d, 更新标签: %d", processed, updated);
        processed = 0;
        updated = 0;
    }
}

/**
 * 刷新所有UI控件的文本
 */
void lv_i18n_refresh_ui(void)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("I18N module not initialized");
        return;
    }
    
    LV_I18N_DEBUG("开始刷新UI文本 (语言: %s)", 
                 s_languages[s_current_lang].language_name);
    
    /* 从活动屏幕开始递归刷新 */
    _i18n_text_refresh_cb(lv_scr_act(), NULL);
}

/**
 * 获取语言名称
 * 
 * @param lang_id 语言ID
 * @return 语言名称
 */
const char * lv_i18n_get_language_name(lv_i18n_lang_t lang_id)
{
    if (!s_is_initialized || lang_id >= s_language_cnt) {
        return "Unknown";
    }
    
    return s_languages[lang_id].language_name;
}

/**
 * 创建一个多语言标签
 * 
 * @param parent 父对象
 * @param key 文本键
 * @param font_size 字体大小
 * @return 创建的标签对象
 */
lv_obj_t * lv_i18n_label_create(lv_obj_t * parent, const char * key, lv_i18n_font_size_t font_size)
{
    if (!s_is_initialized) {
        LV_LOG_ERROR("I18N module not initialized");
        return NULL;
    }
    
    lv_obj_t * label = lv_label_create(parent);
    if (label == NULL) return NULL;
    
    /* 设置字体 */
    lv_obj_set_style_text_font(label, lv_i18n_get_font(font_size), 0);
    
    /* 设置翻译文本 */
    if (key != NULL) {
        lv_i18n_set_text(label, key);
    }
    
    return label;
} 