# 01-事件系统-移植计划

**时间**: 2025-07-30 14:30 GMT+8  
**作者**: <PERSON> (全栈开发者)  
**版本**: 1.0.0

## 项目概述

本文档记录将app_event模块功能移植到app/plugins目录，使用hicar_lite事件系统API的详细计划和实施过程。

## 移植目标

1. 将`app_event/adapters/uart_adapter.c`功能移植到`app/plugins/plug_uart.c`
2. 将`app_event/adapters/hicar_adapter.c`功能移植到`app/plugins/plug_hicar.c`  
3. 将`app_event/src/uart_hicar_bridge.c`功能移植到`app/plugins/plug_bridge.c`
4. 移除图标控制器处理逻辑，标记TODO
5. 确保所有功能正常工作

## 技术架构分析

### 原有架构
```
app_event/
├── adapters/
│   ├── uart_adapter.c      # UART适配器
│   ├── hicar_adapter.c     # HiCar适配器
│   └── app_lvgl_adapter.c  # LVGL适配器
├── src/
│   ├── app_event.c         # 事件系统核心
│   ├── uart_hicar_bridge.c # UART-HiCar桥接
│   └── ui_icon_controller.c # UI图标控制器
└── include/                # 头文件
```

### 目标架构
```
app/plugins/
├── plug_uart.c            # UART插件 (移植自uart_adapter.c)
├── plug_hicar.c           # HiCar插件 (移植自hicar_adapter.c)
└── plug_bridge.c          # 桥接插件 (移植自uart_hicar_bridge.c)

hicar_lite/                # 新事件系统
├── src/eb_core.c          # 事件总线核心
├── include/eb_types.h     # 数据类型定义
└── include/eb_core.h      # API声明
```

## hicar_lite事件系统API

### 核心事件API (8个)
```c
bool eb_init(void);                                    // 初始化事件总线
void eb_deinit(void);                                  // 清理事件总线
bool eb_subscribe(uint16_t id, event_cb_t cb, void *ctx);  // 订阅事件
bool eb_unsubscribe(uint16_t id, event_cb_t cb);      // 取消订阅
bool eb_publish(uint16_t id, void *data);             // 发布事件
bool eb_publish_isr(uint16_t id, void *data);         // 中断安全发布
void eb_dispatch(void);                               // 分发事件
uint32_t eb_stats_lost(void);                        // 获取统计信息
```

### 插件管理API (3个)
```c
bool eb_plug_register(const char *name, void (*init)(void), void (*deinit)(void));
bool eb_plug_unregister(const char *name);
void eb_plug_start_all(void);
```

## 移植映射关系

### API映射
| 原API | 新API | 说明 |
|-------|-------|------|
| app_event_post() | eb_publish() | 发布事件 |
| app_event_register() | eb_subscribe() | 订阅事件 |
| app_event_unregister() | eb_unsubscribe() | 取消订阅 |
| app_adapter_t | eb_plug_register() | 插件注册 |

### 事件ID映射
| 原事件类型 | 新事件ID | 说明 |
|-----------|----------|------|
| APP_EVENT_COMM_DATA_RECEIVED | 0x1001 | UART数据接收 |
| APP_EVENT_BIZ_HICAR_COMMAND | 0x2001 | HiCar命令 |
| APP_EVENT_BIZ_LIGHT_CHANGED | 0x2002 | 灯光状态变化 |
| APP_EVENT_BIZ_SCREEN_CONTROL | 0x2003 | 屏幕控制 |
| APP_EVENT_SYS_START | 0x1000 | 系统启动 |
| APP_EVENT_BIZ_VEHICLE_STATUS | 0x3001 | 车辆状态 |
| APP_EVENT_BIZ_SPEED_INFO | 0x3002 | 速度信息 |

## 实施步骤

### 阶段1: 准备工作
- [x] 分析现有代码结构
- [x] 理解hicar_lite事件系统API
- [x] 制定移植计划
- [x] 创建docs目录和文档

### 阶段2: 核心移植
- [ ] 移植uart_adapter.c到plug_uart.c
- [ ] 移植hicar_adapter.c到plug_hicar.c
- [ ] 移植uart_hicar_bridge.c到plug_bridge.c

### 阶段3: 功能调整
- [ ] 移除图标控制器处理逻辑
- [ ] 添加TODO标记
- [ ] 更新事件ID和回调函数

### 阶段4: 测试验证
- [ ] 编译测试
- [ ] 功能验证
- [ ] 集成测试
- [ ] 部署测试

## 关键技术点

### 1. 事件回调函数签名变化
```c
// 原签名
typedef void (*app_event_handler_t)(app_event_t *event, void *user_data);

// 新签名  
typedef void (*event_cb_t)(uint16_t id, void *data, void *ctx);
```

### 2. 插件注册方式变化
```c
// 原方式
app_adapter_t uart_adapter = {
    .name = "uart",
    .init = uart_adapter_init,
    .process = uart_adapter_process,
    .deinit = uart_adapter_deinit
};

// 新方式
eb_plug_register("uart", uart_plugin_init, uart_plugin_deinit);
```

### 3. 图标控制器处理
- 移除所有icon_controller相关代码
- 在相关位置添加TODO注释
- 保留接口但不实现具体功能

## 风险评估

### 高风险项
1. 事件回调函数签名不兼容
2. 数据结构变化导致的内存问题
3. 图标控制器移除可能影响UI显示

### 缓解措施
1. 逐步移植，每个文件单独测试
2. 保留原有数据结构，添加适配层
3. 添加TODO标记，后续补充实现

## 预期成果

1. 成功将3个文件移植到plugins目录
2. 使用hicar_lite事件系统API
3. 移除图标控制器依赖
4. 保持原有功能正常工作
5. 代码结构更加清晰和模块化

## 后续计划

1. 完成移植后进行全面测试
2. 优化事件处理性能
3. 补充图标控制器功能实现
4. 编写用户使用文档
