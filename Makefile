#
# Makefile
#
# ⭐全速编译命令： make clean && make -j$(nproc)

#CC ?= gcc
CC := arm-linux-gnueabi-gcc	# 注意配置工具链！
LVGL_DIR_NAME ?= lvgl
LVGL_DIR ?= .
CFLAGS ?= -O3 -g0 -I$(LVGL_DIR)/ -Wall -Wshadow -Wundef -Wmissing-prototypes -Wno-discarded-qualifiers -Wall -Wextra -Wno-unused-function -Wno-error=strict-prototypes -Wpointer-arith -fno-strict-aliasing -Wno-error=cpp -Wuninitialized -Wmaybe-uninitialized -Wno-unused-parameter -Wno-missing-field-initializers -Wtype-limits -Wsizeof-pointer-memaccess -Wno-format-nonliteral -Wno-cast-qual -Wunreachable-code -Wno-switch-default -Wreturn-type -Wmultichar -Wformat-security -Wno-ignored-qualifiers -Wno-error=pedantic -Wno-sign-compare -Wno-error=missing-prototypes -Wdouble-promotion -Wclobbered -Wdeprecated -Wempty-body -Wtype-limits -Wstack-usage=2048 -Wno-unused-value -Wno-unused-parameter -Wno-missing-field-initializers -Wuninitialized -Wmaybe-uninitialized -Wall -Wextra -Wno-unused-parameter -Wno-missing-field-initializers -Wtype-limits -Wsizeof-pointer-memaccess -Wno-format-nonliteral -Wpointer-arith -Wno-cast-qual -Wmissing-prototypes -Wunreachable-code -Wno-switch-default -Wreturn-type -Wmultichar -Wno-discarded-qualifiers -Wformat-security -Wno-ignored-qualifiers -Wno-sign-compare
LDFLAGS ?= -lm -lpthread -lrt

# SSH部署配置
SSH_HOST ?= **************
SSH_USER ?= root
SSH_PORT ?= 22
SSH_TARGET_DIR ?= /tmp/
SSH_KEY ?=
SSH_OPTS ?= -o StrictHostKeyChecking=no

# 目标文件路径
OBJ_DIR := obj
BIN_DIR := bin

# 目标应用
BIN = ebike_x1

# 应用源文件
MAINSRC = ./main.c

# 引入APP SDK
SDK_LIB = $(LVGL_DIR)/app/lib
SDK_INC = $(LVGL_DIR)/app/lib/include
SDK_NAME = ebike-utils

# SDK编译参数
CFLAGS += -I$(SDK_INC)
LDFLAGS += -L$(SDK_LIB) -l$(SDK_NAME) -Wl,-rpath,'$$ORIGIN' -Wl,-rpath,'$$ORIGIN/$(SDK_LIB)' -Wl,-rpath-link,'$$ORIGIN'

# 事件系统
CSRCS += $(wildcard $(LVGL_DIR)/hicar_lite/src/*.c)
CFLAGS += -I$(LVGL_DIR)/hicar_lite/include

# 启用MCOK_DATA模式
CFLAGS += -DUI_COMP_MGR_DEBUG_PRINT -DUART_HAL_DEBUG_PRINT

# 增加头文件自动依赖追踪
CFLAGS += -MMD -MP

# 引入编译文件
include $(LVGL_DIR)/lvgl/lvgl.mk
include $(LVGL_DIR)/lv_drivers/lv_drivers.mk
include $(LVGL_DIR)/ui/ui.mk
include $(LVGL_DIR)/app/ebike_x1.mk

OBJEXT ?= .o

AOBJS = $(ASRCS:.S=$(OBJEXT))

# 生成目标文件列表
COBJS = $(patsubst %.c,$(OBJ_DIR)/%.o,$(CSRCS))
MAINOBJ = $(patsubst %.c,$(OBJ_DIR)/%.o,$(MAINSRC))

SRCS = $(ASRCS) $(CSRCS) $(MAINSRC)
OBJS = $(AOBJS) $(COBJS)

# 自动包含所有依赖文件
-include $(COBJS:.o=.d) $(MAINOBJ:.o=.d)

all: default

# 编译规则
$(OBJ_DIR)/%.o: %.c
	@mkdir -p $(dir $@)
	$(CC)  $(CFLAGS) -c $< -o $@
	@echo "CC $<"
    
default: $(AOBJS) $(COBJS) $(MAINOBJ)
	$(CC) -o $(BIN) $(MAINOBJ) $(AOBJS) $(COBJS) $(LDFLAGS)
	mkdir -p $(LVGL_DIR)/$(BIN_DIR)
	mv $(BIN) $(LVGL_DIR)/$(BIN_DIR)/

clean: 
	rm -rf $(BIN) $(AOBJS) $(COBJS) $(MAINOBJ) ./$(BIN_DIR)/* ./$(OBJ_DIR)/*
	rm -f $(COBJS:.o=.d) $(MAINOBJ:.o=.d)

run: default
	./$(BIN_DIR)/$(BIN)

directories:
	mkdir -p $(OBJ_DIR) $(BIN_DIR)

# SSH部署到目标设备
deploy: default
	@echo "正在部署到目标设备 $(SSH_USER)@$(SSH_HOST)..."
	scp $(SSH_OPTS) ./$(BIN_DIR)/$(BIN) $(SSH_USER)@$(SSH_HOST):$(SSH_TARGET_DIR)
	scp $(SSH_OPTS) -r ./app/lib $(SSH_USER)@$(SSH_HOST):$(SSH_TARGET_DIR)
	scp $(SSH_OPTS) -r ./app/lib/*.so $(SSH_USER)@$(SSH_HOST):$(SSH_TARGET_DIR)
	@echo "部署完成"

# 在目标设备上运行
remote-run: deploy
	@echo "在目标设备上运行演示程序..."
	ssh $(SSH_OPTS) $(SSH_USER)@$(SSH_HOST) "cd $(SSH_TARGET_DIR) && export LD_LIBRARY_PATH=$(SSH_TARGET_DIR)lib:\$$LD_LIBRARY_PATH && ./$(BIN)"

# 部署模拟器库
deploy-simulator: deploy
	@echo "部署UART模拟器库到目标设备..."
	@if [ ! -f "libserial_utils_simulator.so" ]; then \
		echo "错误: 模拟器库不存在，请先编译"; \
		echo "运行: cd app_simulator && make && make install"; \
		exit 1; \
	fi
	scp $(SSH_OPTS) ./libserial_utils_simulator.so $(SSH_USER)@$(SSH_HOST):$(SSH_TARGET_DIR)
	@echo "模拟器库部署完成"

# 在目标设备上使用模拟器运行
remote-run-simulator: deploy-simulator
	@echo "在目标设备上运行演示程序（模拟器模式）..."
	@echo "使用UART模拟器替代真实硬件"
	ssh $(SSH_OPTS) $(SSH_USER)@$(SSH_HOST) "cd $(SSH_TARGET_DIR) && export LD_LIBRARY_PATH=$(SSH_TARGET_DIR)lib:\$$LD_LIBRARY_PATH && LD_PRELOAD=./libserial_utils_simulator.so ./$(BIN)"

.PHONY: all clean run directories deploy remote-run deploy-simulator remote-run-simulator
