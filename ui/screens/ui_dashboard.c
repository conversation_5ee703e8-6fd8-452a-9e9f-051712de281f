//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

void ui_dashboard_screen_init(void)
{
    ui_dashboard = lv_obj_create(NULL);
    lv_obj_clear_flag(ui_dashboard, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_dashboard, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_dashboard, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_tiled(ui_dashboard, true, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_statusbar = ui_Statusbar2_create(ui_dashboard);
    lv_obj_set_x(ui_statusbar, 0);
    lv_obj_set_y(ui_statusbar, 0);

    lv_obj_set_height(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT), lv_pct(100));
    lv_obj_set_width(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT), LV_SIZE_CONTENT);   /// 1

    lv_obj_set_height(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL), lv_pct(50));
    lv_obj_set_width(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL), LV_SIZE_CONTENT);   /// 100

    lv_obj_set_flex_flow(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA), LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA), LV_FLEX_ALIGN_SPACE_BETWEEN,
                          LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_pad_left(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA), 0,
                              LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA), 15,
                               LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA), 0,
                             LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA), 0,
                                LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_label_set_text(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA_TEMP), "25.0 °C");
    lv_obj_set_style_pad_left(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA_TEMP), 0,
                              LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA_TEMP), 0,
                               LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA_TEMP), 0,
                             LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_REAL_STA_TEMP), 0,
                                LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_height(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER), lv_pct(50));
    lv_obj_set_width(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER), LV_SIZE_CONTENT);   /// 1

    lv_obj_add_flag(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_CENTER), LV_OBJ_FLAG_HIDDEN);     /// Flags

    ui_speedometer = ui_Speedometer_create(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_CENTER));
    lv_obj_set_width(ui_speedometer, 413);
    lv_obj_set_height(ui_speedometer, lv_pct(100));
    lv_obj_set_x(ui_speedometer, -21);
    lv_obj_set_y(ui_speedometer, 5);
    lv_obj_clear_flag(ui_speedometer, LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_obj_clear_flag(ui_comp_get_child(ui_speedometer, UI_COMP_SPEEDOMETER_CENTER), LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_obj_add_flag(ui_comp_get_child(ui_speedometer, UI_COMP_SPEEDOMETER_ICON_L), LV_OBJ_FLAG_HIDDEN);     /// Flags

    ui_sta_icon = ui_StaIcon_create(ui_comp_get_child(ui_speedometer, UI_COMP_SPEEDOMETER_ICON_L));
    lv_obj_set_x(ui_sta_icon, 0);
    lv_obj_set_y(ui_sta_icon, 0);
    lv_obj_clear_flag(ui_sta_icon, LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_img_set_src(ui_comp_get_child(ui_sta_icon, UI_COMP_STAICON_ICON), &ui_img_dark_ic_sta_signals_f_png);

    lv_obj_set_flex_flow(ui_comp_get_child(ui_speedometer, UI_COMP_SPEEDOMETER_ICON_R), LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_comp_get_child(ui_speedometer, UI_COMP_SPEEDOMETER_ICON_R), LV_FLEX_ALIGN_START,
                          LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_add_flag(ui_comp_get_child(ui_speedometer, UI_COMP_SPEEDOMETER_ICON_R), LV_OBJ_FLAG_HIDDEN);     /// Flags

    ui_sta_icon = ui_StaIcon_create(ui_comp_get_child(ui_speedometer, UI_COMP_SPEEDOMETER_ICON_R));
    lv_obj_set_x(ui_sta_icon, 0);
    lv_obj_set_y(ui_sta_icon, 0);
    lv_obj_clear_flag(ui_sta_icon, LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_img_set_src(ui_comp_get_child(ui_sta_icon, UI_COMP_STAICON_ICON), &ui_img_dark_ic_sta_signals_fd_png);

    ui_sta_icon = ui_StaIcon_create(ui_comp_get_child(ui_speedometer, UI_COMP_SPEEDOMETER_ICON_R));
    lv_obj_set_x(ui_sta_icon, 0);
    lv_obj_set_y(ui_sta_icon, 0);
    lv_obj_clear_flag(ui_sta_icon, LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_img_set_src(ui_comp_get_child(ui_sta_icon, UI_COMP_STAICON_ICON), &ui_img_dark_ic_sta_signals_fe_png);

    lv_obj_set_height(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_RIGHT_NORMAL), lv_pct(50));
    lv_obj_set_width(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_RIGHT_NORMAL), LV_SIZE_CONTENT);   /// 1

    ui_StaIcon1 = ui_StaIcon_create(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_RIGHT_NORMAL_DYNAMIC_R));
    lv_obj_set_x(ui_StaIcon1, 574);
    lv_obj_set_y(ui_StaIcon1, 38);
    lv_obj_clear_flag(ui_StaIcon1, LV_OBJ_FLAG_HIDDEN);      /// Flags

    ui_StaIcon2 = ui_StaIcon_create(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_RIGHT_NORMAL_DYNAMIC_R));
    lv_obj_set_x(ui_StaIcon2, 574);
    lv_obj_set_y(ui_StaIcon2, 38);

    lv_img_set_src(ui_comp_get_child(ui_StaIcon2, UI_COMP_STAICON_ICON), &ui_img_dark_ic_sta_4g_full_png);

    lv_obj_set_height(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_RIGHT_ODO), lv_pct(50));
    lv_obj_set_width(ui_comp_get_child(ui_statusbar, UI_COMP_STATUSBAR2_RIGHT_ODO), LV_SIZE_CONTENT);   /// 1

    ui_sidebar = ui_Sidebar_create(ui_dashboard);
    lv_obj_set_x(ui_sidebar, 25);
    lv_obj_set_y(ui_sidebar, -45);
    lv_obj_clear_flag(ui_sidebar, LV_OBJ_FLAG_HIDDEN);      /// Flags

    ui_home = ui_Home_create(ui_dashboard);
    lv_obj_set_x(ui_home, 48);
    lv_obj_set_y(ui_home, 40);
    lv_obj_clear_flag(ui_home, LV_OBJ_FLAG_HIDDEN);      /// Flags


    lv_obj_set_height(ui_comp_get_child(ui_home, UI_COMP_HOME_HOME_RIGHT_RUN_NOS), 80);
    lv_obj_set_width(ui_comp_get_child(ui_home, UI_COMP_HOME_HOME_RIGHT_RUN_NOS), lv_pct(100));

    lv_obj_set_width(ui_comp_get_child(ui_home, UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_SPEED_BG), 80);
    lv_obj_set_height(ui_comp_get_child(ui_home, UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_SPEED_BG), 80);
    lv_obj_add_flag(ui_comp_get_child(ui_home, UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_SPEED_BG),
                    LV_OBJ_FLAG_HIDDEN);     /// Flags

    ui_hicar = ui_Hicar_create(ui_dashboard);
    lv_obj_set_x(ui_hicar, 48);
    lv_obj_set_y(ui_hicar, 40);
    lv_obj_add_flag(ui_hicar, LV_OBJ_FLAG_HIDDEN);     /// Flags

    lv_label_set_text(ui_comp_get_child(ui_hicar, UI_COMP_HICAR_CONNECTED_STATUS_STATUS_LABEL_STATUS_LABEL_VAL),
                      "#HICAR_NOT_CONNECT");

    ui_setting = lv_obj_create(ui_dashboard);
    lv_obj_set_width(ui_setting, 876);
    lv_obj_set_height(ui_setting, 476);
    lv_obj_set_x(ui_setting, 48);
    lv_obj_set_y(ui_setting, 40);
    lv_obj_set_align(ui_setting, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_setting, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_setting, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_flag(ui_setting, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_setting, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_setting, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_setting, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_setting, &ui_img_dark_bg_dashboard_main_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_SettingSidebar = ui_SettingSidebar_create(ui_setting);
    lv_obj_set_x(ui_SettingSidebar, 1);
    lv_obj_set_y(ui_SettingSidebar, 0);

    ui_setting_content = lv_obj_create(ui_setting);
    lv_obj_set_height(ui_setting_content, lv_pct(100));
    lv_obj_set_flex_grow(ui_setting_content, 11);
    lv_obj_set_x(ui_setting_content, 141);
    lv_obj_set_y(ui_setting_content, -5);
    lv_obj_set_align(ui_setting_content, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(ui_setting_content, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_setting_content, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_setting_content, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_setting_content, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_setting_content, 26, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_setting_content, 25, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_system_setting_warp = lv_obj_create(ui_setting_content);
    lv_obj_set_width(ui_system_setting_warp, lv_pct(100));
    lv_obj_set_height(ui_system_setting_warp, lv_pct(100));
    lv_obj_set_align(ui_system_setting_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_system_setting_warp, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(ui_system_setting_warp, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_set_style_bg_color(ui_system_setting_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_system_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_system_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_system_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_system_setting_warp, 30, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_system_setting_warp, 30, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_system_item1 = lv_obj_create(ui_system_setting_warp);
    lv_obj_set_width(ui_system_item1, 329);
    lv_obj_set_height(ui_system_item1, 115);
    lv_obj_set_x(ui_system_item1, -20);
    lv_obj_set_y(ui_system_item1, 94);
    lv_obj_set_align(ui_system_item1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_system_item1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_system_item1, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_system_item1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_system_item1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_system_item1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_system_item1, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_system_item1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_system_item1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_system_item1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_SettingItem = ui_SettingItem_create(ui_system_item1);
    lv_obj_set_x(ui_SettingItem, 0);
    lv_obj_set_y(ui_SettingItem, 0);

    ui_system_item2 = lv_obj_create(ui_system_setting_warp);
    lv_obj_set_width(ui_system_item2, 329);
    lv_obj_set_height(ui_system_item2, 115);
    lv_obj_set_x(ui_system_item2, -20);
    lv_obj_set_y(ui_system_item2, 94);
    lv_obj_set_align(ui_system_item2, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_system_item2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_system_item2, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_system_item2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_system_item2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_system_item2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_system_item2, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_system_item2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_system_item2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_system_item2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_SettingItem2 = ui_SettingItem_create(ui_system_item2);
    lv_obj_set_x(ui_SettingItem2, 0);
    lv_obj_set_y(ui_SettingItem2, 0);

    lv_img_set_src(ui_comp_get_child(ui_SettingItem2, UI_COMP_SETTINGITEM_ITEM_ICON),
                   &ui_img_setting_ic_setting_bluetooth_png);

    lv_label_set_text(ui_comp_get_child(ui_SettingItem2, UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_LEFT_ITEM_LABEL_VAL),
                      "#BLUETOOTH");

    lv_label_set_text(ui_comp_get_child(ui_SettingItem2, UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_RIGHT_LIST_ITEM_SUB),
                      "#ON");

    ui_system_item3 = lv_obj_create(ui_system_setting_warp);
    lv_obj_set_width(ui_system_item3, 329);
    lv_obj_set_height(ui_system_item3, 108);
    lv_obj_set_x(ui_system_item3, -20);
    lv_obj_set_y(ui_system_item3, 94);
    lv_obj_set_align(ui_system_item3, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_system_item3, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_system_item3, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_system_item3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_system_item3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_system_item3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_system_item3, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_system_item3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_system_item3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_system_item3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_SettingVolume = ui_SettingVolume_create(ui_system_item3);
    lv_obj_set_x(ui_SettingVolume, 0);
    lv_obj_set_y(ui_SettingVolume, 0);

    ui_system_item4 = lv_obj_create(ui_system_setting_warp);
    lv_obj_set_width(ui_system_item4, 329);
    lv_obj_set_height(ui_system_item4, 115);
    lv_obj_set_x(ui_system_item4, -20);
    lv_obj_set_y(ui_system_item4, 94);
    lv_obj_set_align(ui_system_item4, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_system_item4, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_system_item4, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_system_item4, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_system_item4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_system_item4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_system_item4, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_system_item4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_system_item4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_system_item4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_SettingItem3 = ui_SettingItem_create(ui_system_item4);
    lv_obj_set_x(ui_SettingItem3, 0);
    lv_obj_set_y(ui_SettingItem3, 0);

    lv_img_set_src(ui_comp_get_child(ui_SettingItem3, UI_COMP_SETTINGITEM_ITEM_ICON), &ui_img_setting_ic_setting_date_png);

    lv_label_set_text(ui_comp_get_child(ui_SettingItem3, UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_LEFT_ITEM_LABEL_VAL),
                      "#DATE");

    lv_label_set_text(ui_comp_get_child(ui_SettingItem3, UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_RIGHT_LIST_ITEM_SUB),
                      "2025-5-22 12:35");

    ui_vehicle_setting_warp = lv_obj_create(ui_setting_content);
    lv_obj_set_width(ui_vehicle_setting_warp, lv_pct(100));
    lv_obj_set_height(ui_vehicle_setting_warp, lv_pct(100));
    lv_obj_set_align(ui_vehicle_setting_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_vehicle_setting_warp, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(ui_vehicle_setting_warp, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_add_flag(ui_vehicle_setting_warp, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_set_style_bg_color(ui_vehicle_setting_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_vehicle_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_vehicle_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_vehicle_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_vehicle_setting_warp, 30, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_vehicle_setting_warp, 30, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_vehicle_setting_warp1 = lv_obj_create(ui_vehicle_setting_warp);
    lv_obj_set_width(ui_vehicle_setting_warp1, 329);
    lv_obj_set_height(ui_vehicle_setting_warp1, 108);
    lv_obj_set_x(ui_vehicle_setting_warp1, -20);
    lv_obj_set_y(ui_vehicle_setting_warp1, 94);
    lv_obj_set_align(ui_vehicle_setting_warp1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_vehicle_setting_warp1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_vehicle_setting_warp1, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_vehicle_setting_warp1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_vehicle_setting_warp1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_vehicle_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_vehicle_setting_warp1, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_vehicle_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_vehicle_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_vehicle_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_SystemItem3 = ui_SystemItem_create(ui_vehicle_setting_warp1);
    lv_obj_set_x(ui_SystemItem3, 0);
    lv_obj_set_y(ui_SystemItem3, 0);

    lv_img_set_src(ui_comp_get_child(ui_SystemItem3, UI_COMP_SYSTEMITEM_ITEM_ICON1), &ui_img_setting_ic_setting_btkey_png);

    lv_label_set_text(ui_comp_get_child(ui_SystemItem3, UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_LEFT1_ITEM_LABEL_VAL1),
                      "#UNLOCK");

    lv_label_set_text(ui_comp_get_child(ui_SystemItem3, UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_RIGHT1_LIST_ITEM_SUB1),
                      "#ON");

    ui_vehicle_setting_warp2 = lv_obj_create(ui_vehicle_setting_warp);
    lv_obj_set_width(ui_vehicle_setting_warp2, 329);
    lv_obj_set_height(ui_vehicle_setting_warp2, 108);
    lv_obj_set_x(ui_vehicle_setting_warp2, -20);
    lv_obj_set_y(ui_vehicle_setting_warp2, 94);
    lv_obj_set_align(ui_vehicle_setting_warp2, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_vehicle_setting_warp2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_vehicle_setting_warp2, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_vehicle_setting_warp2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_vehicle_setting_warp2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_vehicle_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_vehicle_setting_warp2, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_vehicle_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_vehicle_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_vehicle_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_SystemItem2 = ui_SystemItem_create(ui_vehicle_setting_warp2);
    lv_obj_set_x(ui_SystemItem2, 0);
    lv_obj_set_y(ui_SystemItem2, 0);

    lv_img_set_src(ui_comp_get_child(ui_SystemItem2, UI_COMP_SYSTEMITEM_ITEM_ICON1), &ui_img_setting_ic_setting_wifi_png);

    lv_label_set_text(ui_comp_get_child(ui_SystemItem2, UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_LEFT1_ITEM_LABEL_VAL1),
                      "#LOCATION");

    lv_label_set_text(ui_comp_get_child(ui_SystemItem2, UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_RIGHT1_LIST_ITEM_SUB1),
                      "#ON");

    ui_vehicle_setting_warp3 = lv_obj_create(ui_vehicle_setting_warp);
    lv_obj_set_width(ui_vehicle_setting_warp3, 658);
    lv_obj_set_height(ui_vehicle_setting_warp3, 80);
    lv_obj_set_x(ui_vehicle_setting_warp3, -20);
    lv_obj_set_y(ui_vehicle_setting_warp3, 94);
    lv_obj_set_align(ui_vehicle_setting_warp3, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_vehicle_setting_warp3, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_vehicle_setting_warp3, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_vehicle_setting_warp3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_vehicle_setting_warp3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_vehicle_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_vehicle_setting_warp3, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_vehicle_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_vehicle_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_vehicle_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_SettingInfo = ui_SettingInfo_create(ui_vehicle_setting_warp3);
    lv_obj_set_x(ui_SettingInfo, 0);
    lv_obj_set_y(ui_SettingInfo, 0);

    lv_obj_set_width(ui_comp_get_child(ui_SettingInfo, UI_COMP_SETTINGINFO_INFO_LEFT), 245);
    lv_obj_set_height(ui_comp_get_child(ui_SettingInfo, UI_COMP_SETTINGINFO_INFO_LEFT), 50);

    lv_obj_set_width(ui_comp_get_child(ui_SettingInfo, UI_COMP_SETTINGINFO_INFO_SPACE), 45);
    lv_obj_set_height(ui_comp_get_child(ui_SettingInfo, UI_COMP_SETTINGINFO_INFO_SPACE), lv_pct(100));

    lv_obj_set_style_pad_left(ui_comp_get_child(ui_SettingInfo, UI_COMP_SETTINGINFO_INFO_RIGHT), 10,
                              LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_comp_get_child(ui_SettingInfo, UI_COMP_SETTINGINFO_INFO_RIGHT), 0,
                               LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_comp_get_child(ui_SettingInfo, UI_COMP_SETTINGINFO_INFO_RIGHT), 0,
                             LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_comp_get_child(ui_SettingInfo, UI_COMP_SETTINGINFO_INFO_RIGHT), 0,
                                LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_other_setting_warp = lv_obj_create(ui_setting_content);
    lv_obj_set_width(ui_other_setting_warp, lv_pct(100));
    lv_obj_set_height(ui_other_setting_warp, lv_pct(100));
    lv_obj_set_align(ui_other_setting_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_other_setting_warp, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(ui_other_setting_warp, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_add_flag(ui_other_setting_warp, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_set_style_bg_color(ui_other_setting_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_other_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_other_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_other_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_other_setting_warp, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_other_setting_warp, 20, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_InfoLarge1 = ui_InfoLarge_create(ui_other_setting_warp);
    lv_obj_set_x(ui_InfoLarge1, 0);
    lv_obj_set_y(ui_InfoLarge1, 0);
    lv_obj_set_style_pad_left(ui_InfoLarge1, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_InfoLarge1, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_InfoLarge1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_InfoLarge1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_img_set_src(ui_comp_get_child(ui_InfoLarge1, UI_COMP_INFOLARGE_INFO_LEFT_INFO_ICON),
                   &ui_img_setting_ic_setting_alarm_png);

    lv_label_set_text(ui_comp_get_child(ui_InfoLarge1, UI_COMP_INFOLARGE_INFO_LEFT_LEFT_LABEL_VAL), "#SW");

    lv_label_set_text(ui_comp_get_child(ui_InfoLarge1, UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL), "#SW_INFO");
    lv_obj_set_style_text_color(ui_comp_get_child(ui_InfoLarge1, UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL),
                                lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_comp_get_child(ui_InfoLarge1, UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL), 200,
                              LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_flag(ui_comp_get_child(ui_InfoLarge1, UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_ICON),
                    LV_OBJ_FLAG_HIDDEN);     /// Flags

    ui_InfoLarge2 = ui_InfoLarge_create(ui_other_setting_warp);
    lv_obj_set_x(ui_InfoLarge2, 0);
    lv_obj_set_y(ui_InfoLarge2, 0);
    lv_obj_set_style_pad_left(ui_InfoLarge2, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_InfoLarge2, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_InfoLarge2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_InfoLarge2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_img_set_src(ui_comp_get_child(ui_InfoLarge2, UI_COMP_INFOLARGE_INFO_LEFT_INFO_ICON),
                   &ui_img_setting_ic_setting_keyboard_png);

    lv_label_set_text(ui_comp_get_child(ui_InfoLarge2, UI_COMP_INFOLARGE_INFO_LEFT_LEFT_LABEL_VAL), "#HW");

    lv_label_set_text(ui_comp_get_child(ui_InfoLarge2, UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL), "#HW_INFO");
    lv_obj_set_style_text_color(ui_comp_get_child(ui_InfoLarge2, UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL),
                                lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_comp_get_child(ui_InfoLarge2, UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL), 200,
                              LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_flag(ui_comp_get_child(ui_InfoLarge2, UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_ICON),
                    LV_OBJ_FLAG_HIDDEN);     /// Flags

    ui_InfoLarge3 = ui_InfoLarge_create(ui_other_setting_warp);
    lv_obj_set_x(ui_InfoLarge3, 0);
    lv_obj_set_y(ui_InfoLarge3, 0);

    ui_dialog = lv_obj_create(ui_dashboard);
    lv_obj_set_width(ui_dialog, lv_pct(100));
    lv_obj_set_height(ui_dialog, lv_pct(100));
    lv_obj_set_align(ui_dialog, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_dialog, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_dialog, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_dialog, 125, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_dialog_msg = lv_obj_create(ui_dialog);
    lv_obj_set_width(ui_dialog_msg, 480);
    lv_obj_set_height(ui_dialog_msg, 260);
    lv_obj_set_align(ui_dialog_msg, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_dialog_msg, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(ui_dialog_msg, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_flag(ui_dialog_msg, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_dialog_msg, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_dialog_msg, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_dialog_msg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_dialog_msg, &ui_img_dialog_pic_bg_m_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_ToastMsg = ui_Toastmsg_create(ui_dialog_msg);
    lv_obj_set_x(ui_ToastMsg, 0);
    lv_obj_set_y(ui_ToastMsg, 0);

    lv_img_set_src(ui_comp_get_child(ui_ToastMsg, UI_COMP_TOASTMSG_TOAST_MSG_ICON_TOAST_MSG_ICON_ASSET),
                   &ui_img_dialog_ic_wifi_msg_png);

    ui_dialog_confirm = lv_obj_create(ui_dialog);
    lv_obj_set_width(ui_dialog_confirm, lv_pct(100));
    lv_obj_set_height(ui_dialog_confirm, lv_pct(100));
    lv_obj_set_x(ui_dialog_confirm, 0);
    lv_obj_set_y(ui_dialog_confirm, -1);
    lv_obj_set_align(ui_dialog_confirm, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_dialog_confirm, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(ui_dialog_confirm, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_flag(ui_dialog_confirm, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_dialog_confirm, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_dialog_confirm, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_dialog_confirm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_DialogConfirm = ui_DialogConfirm_create(ui_dialog_confirm);
    lv_obj_set_x(ui_DialogConfirm, 0);
    lv_obj_set_y(ui_DialogConfirm, 0);

    ui_Keyboard = lv_obj_create(ui_dialog_confirm);
    lv_obj_set_width(ui_Keyboard, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_Keyboard, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_Keyboard, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_Keyboard, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(ui_Keyboard, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_flag(ui_Keyboard, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_Keyboard, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_Keyboard, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_Keyboard, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_Keyboard, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_Keyboard, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_Keyboard, 30, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_Keyboard, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_keyboard_input = lv_keyboard_create(ui_Keyboard);
    lv_obj_set_width(ui_keyboard_input, 585);
    lv_obj_set_height(ui_keyboard_input, 188);
    lv_obj_set_align(ui_keyboard_input, LV_ALIGN_CENTER);

    ui_dialog_view = ui_DialogView2_create(ui_dialog);
    lv_obj_set_x(ui_dialog_view, 0);
    lv_obj_set_y(ui_dialog_view, 0);
    lv_obj_clear_flag(ui_dialog_view, LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_obj_clear_flag(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_BG_PIC), LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_obj_clear_flag(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_HEAD),
                      LV_OBJ_FLAG_CLICKABLE);      /// Flags

    lv_obj_clear_flag(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT),
                      LV_OBJ_FLAG_CLICKABLE);      /// Flags

    lv_obj_add_flag(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST),
                    LV_OBJ_FLAG_HIDDEN);     /// Flags

    ui_paired = lv_obj_create(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_PAIRED_ITEMS));
    lv_obj_set_height(ui_paired, 80);
    lv_obj_set_width(ui_paired, lv_pct(100));
    lv_obj_set_align(ui_paired, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_paired, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_paired, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_paired, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_DeviceItem3 = ui_DeviceItem_create(ui_paired);
    lv_obj_set_x(ui_DeviceItem3, 0);
    lv_obj_set_y(ui_DeviceItem3, 0);
    lv_obj_add_state(ui_DeviceItem3, LV_STATE_CHECKED);       /// States

    ui_paired1 = lv_obj_create(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_PAIRED_ITEMS));
    lv_obj_set_height(ui_paired1, 80);
    lv_obj_set_width(ui_paired1, lv_pct(100));
    lv_obj_set_align(ui_paired1, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_paired1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_paired1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_paired1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_DeviceItem4 = ui_DeviceItem_create(ui_paired1);
    lv_obj_set_x(ui_DeviceItem4, 0);
    lv_obj_set_y(ui_DeviceItem4, 0);
    lv_obj_add_state(ui_DeviceItem4, LV_STATE_CHECKED);       /// States


    ui_unpaired = lv_obj_create(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_UNPAIRED_ITEMS));
    lv_obj_set_height(ui_unpaired, 80);
    lv_obj_set_width(ui_unpaired, lv_pct(100));
    lv_obj_set_align(ui_unpaired, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_unpaired, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_unpaired, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_unpaired, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_DeviceItem = ui_DeviceItem_create(ui_unpaired);
    lv_obj_set_x(ui_DeviceItem, 0);
    lv_obj_set_y(ui_DeviceItem, 0);

    ui_unpaired1 = lv_obj_create(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_UNPAIRED_ITEMS));
    lv_obj_set_height(ui_unpaired1, 80);
    lv_obj_set_width(ui_unpaired1, lv_pct(100));
    lv_obj_set_align(ui_unpaired1, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_unpaired1, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_unpaired1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_unpaired1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_unpaired1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_DeviceItem1 = ui_DeviceItem_create(ui_unpaired1);
    lv_obj_set_x(ui_DeviceItem1, 0);
    lv_obj_set_y(ui_DeviceItem1, 0);

    ui_BLANK = lv_obj_create(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST));
    lv_obj_set_height(ui_BLANK, 30);
    lv_obj_set_width(ui_BLANK, lv_pct(100));
    lv_obj_set_align(ui_BLANK, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_BLANK, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_BLANK, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BLANK, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_BLANK, lv_color_hex(0xFFFFFF), LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_BLANK, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    ui_ListEmpty = ui_ListEmpty_create(ui_comp_get_child(ui_dialog_view, UI_COMP_DIALOGVIEW2_MAIN_CONTENT));
    lv_obj_set_x(ui_ListEmpty, 0);
    lv_obj_set_y(ui_ListEmpty, 0);
    lv_obj_clear_flag(ui_ListEmpty, LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_obj_set_width(ui_comp_get_child(ui_ListEmpty, UI_COMP_LISTEMPTY_ICON), 168);
    lv_obj_set_height(ui_comp_get_child(ui_ListEmpty, UI_COMP_LISTEMPTY_ICON), 168);

    lv_img_set_src(ui_comp_get_child(ui_ListEmpty, UI_COMP_LISTEMPTY_ICON_IMAGE), &ui_img_setting_ic_wifi_big_png);

    lv_keyboard_set_textarea(ui_keyboard_input, ui_comp_get_child(ui_DialogConfirm,
                                                                  UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_TEXTAREA));
    lv_obj_add_event_cb(ui_dialog_view, ui_event_dialog_view_DialogView2, LV_EVENT_ALL, NULL);

}
