/*******************************************************************************
 * Size: 24 px
 * Bpp: 4
 * Opts: --bpp 4 --size 24 --font E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts/AlibabaPuHuiTi-3-55-Regular.ttf -o E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts\ui_font_AlibabaPuHui24.c --format lvgl -r 0x20-0x7f --symbols 俯仰角侧倾加速度：°1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_ALIBABAPUHUI24
#define UI_FONT_ALIBABAPUHUI24 1
#endif

#if UI_FONT_ALIBABAPUHUI24

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xd, 0xf7, 0xd, 0xf6, 0xc, 0xf6, 0xc, 0xf5,
    0xb, 0xf5, 0xb, 0xf4, 0xa, 0xf4, 0xa, 0xf3,
    0x9, 0xf3, 0x9, 0xf2, 0x8, 0xf2, 0x5, 0xa1,
    0x0, 0x0, 0x1d, 0xf7, 0x3f, 0xfc, 0x1e, 0xf8,

    /* U+0022 "\"" */
    0x1f, 0xf3, 0x7f, 0xd0, 0xff, 0x26, 0xfc, 0xf,
    0xf0, 0x5f, 0xa0, 0xef, 0x4, 0xf9, 0xc, 0xe0,
    0x2f, 0x80, 0xbd, 0x1, 0xf7, 0x9, 0xb0, 0xf,
    0x50,

    /* U+0023 "#" */
    0x0, 0x0, 0xf, 0xe0, 0x0, 0x3f, 0xb0, 0x0,
    0x0, 0x4f, 0xb0, 0x0, 0x7f, 0x70, 0x0, 0x0,
    0x7f, 0x70, 0x0, 0xaf, 0x40, 0x0, 0x0, 0xbf,
    0x30, 0x0, 0xef, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x5a, 0xab, 0xfe, 0xaa, 0xac,
    0xfd, 0xaa, 0x0, 0x5, 0xf9, 0x0, 0x8, 0xf6,
    0x0, 0x0, 0x9, 0xf5, 0x0, 0xc, 0xf2, 0x0,
    0x0, 0xc, 0xf2, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x2f, 0xc0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5a, 0xdf, 0xca,
    0xaa, 0xef, 0xba, 0xaa, 0x0, 0xaf, 0x40, 0x0,
    0xdf, 0x10, 0x0, 0x0, 0xdf, 0x10, 0x0, 0xfe,
    0x0, 0x0, 0x1, 0xfd, 0x0, 0x4, 0xfa, 0x0,
    0x0, 0x4, 0xfa, 0x0, 0x7, 0xf7, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x8, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x70, 0x0,
    0x0, 0x6, 0xdf, 0xff, 0xfd, 0xa1, 0x7, 0xfe,
    0xbf, 0xdc, 0xdf, 0x20, 0xff, 0x11, 0xf7, 0x0,
    0x0, 0x2f, 0xb0, 0x1f, 0x70, 0x0, 0x3, 0xfb,
    0x1, 0xf7, 0x0, 0x0, 0x1f, 0xe1, 0x1f, 0x70,
    0x0, 0x0, 0xaf, 0xe9, 0xf7, 0x0, 0x0, 0x0,
    0x8e, 0xff, 0xfb, 0x50, 0x0, 0x0, 0x5, 0xfe,
    0xff, 0xc0, 0x0, 0x0, 0x1f, 0x72, 0xcf, 0x80,
    0x0, 0x1, 0xf7, 0x2, 0xfd, 0x0, 0x0, 0x1f,
    0x70, 0xf, 0xf0, 0x0, 0x1, 0xf7, 0x0, 0xfe,
    0x21, 0x0, 0x1f, 0x70, 0x9f, 0xa7, 0xfe, 0xdc,
    0xfe, 0xff, 0xe2, 0x4b, 0xde, 0xff, 0xfd, 0x91,
    0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x70, 0x0, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x8, 0x30, 0x0, 0x0,

    /* U+0025 "%" */
    0x2, 0xbe, 0xeb, 0x20, 0x0, 0x0, 0x9e, 0x10,
    0x0, 0xd, 0xe6, 0x6d, 0xe0, 0x0, 0x3, 0xf6,
    0x0, 0x0, 0x3f, 0x60, 0x5, 0xf4, 0x0, 0xc,
    0xc0, 0x0, 0x0, 0x5f, 0x40, 0x2, 0xf6, 0x0,
    0x5f, 0x30, 0x0, 0x0, 0x5f, 0x30, 0x2, 0xf6,
    0x0, 0xea, 0x0, 0x0, 0x0, 0x4f, 0x50, 0x4,
    0xf5, 0x8, 0xf1, 0x0, 0x0, 0x0, 0xe, 0xc1,
    0x1b, 0xf1, 0x2f, 0x70, 0x0, 0x0, 0x0, 0x4,
    0xef, 0xff, 0x50, 0xad, 0x5, 0xdf, 0xe8, 0x0,
    0x0, 0x2, 0x20, 0x4, 0xf5, 0x2f, 0xa4, 0x7f,
    0x70, 0x0, 0x0, 0x0, 0xd, 0xb0, 0x8f, 0x10,
    0xb, 0xd0, 0x0, 0x0, 0x0, 0x6f, 0x20, 0xbe,
    0x0, 0x9, 0xf0, 0x0, 0x0, 0x1, 0xe9, 0x0,
    0xcd, 0x0, 0x8, 0xf1, 0x0, 0x0, 0x9, 0xe1,
    0x0, 0xbe, 0x0, 0x9, 0xf0, 0x0, 0x0, 0x2f,
    0x60, 0x0, 0x8f, 0x0, 0xa, 0xd0, 0x0, 0x0,
    0xcd, 0x0, 0x0, 0x3f, 0x80, 0x3f, 0x70, 0x0,
    0x5, 0xf3, 0x0, 0x0, 0x5, 0xdf, 0xe8, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x8, 0xef, 0xd7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xfc, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x6, 0xfe, 0x10, 0x3f, 0xf1, 0x0, 0x0,
    0x0, 0x9, 0xf9, 0x0, 0xd, 0xf3, 0x0, 0x0,
    0x0, 0x8, 0xfa, 0x0, 0x2f, 0xf1, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x13, 0xdf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xef, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xcf, 0xfd, 0x10, 0x0, 0x12, 0x10,
    0x0, 0x7f, 0xfa, 0xff, 0x50, 0x0, 0x9f, 0x70,
    0x4, 0xff, 0x40, 0x5f, 0xf4, 0x0, 0xdf, 0x30,
    0xc, 0xf6, 0x0, 0x7, 0xff, 0x24, 0xfd, 0x0,
    0xf, 0xf2, 0x0, 0x0, 0xaf, 0xed, 0xf6, 0x0,
    0xf, 0xf5, 0x0, 0x0, 0xc, 0xff, 0xb0, 0x0,
    0xa, 0xfe, 0x30, 0x0, 0x3c, 0xff, 0xc2, 0x0,
    0x1, 0xdf, 0xfe, 0xdf, 0xff, 0xae, 0xff, 0xd4,
    0x0, 0x8, 0xdf, 0xfd, 0x93, 0x1, 0x9d, 0xf5,

    /* U+0027 "'" */
    0x1f, 0xf3, 0xf, 0xf2, 0xf, 0xf0, 0xe, 0xf0,
    0xc, 0xe0, 0xb, 0xd0, 0x9, 0xb0,

    /* U+0028 "(" */
    0x0, 0x0, 0xdf, 0x20, 0x0, 0x7f, 0x70, 0x0,
    0x1f, 0xe0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x6f, 0xa0, 0x0, 0xb, 0xf5, 0x0,
    0x0, 0xef, 0x10, 0x0, 0x2f, 0xe0, 0x0, 0x4,
    0xfc, 0x0, 0x0, 0x5f, 0xb0, 0x0, 0x6, 0xfa,
    0x0, 0x0, 0x5f, 0xb0, 0x0, 0x4, 0xfc, 0x0,
    0x0, 0x3f, 0xe0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0xb, 0xf5, 0x0, 0x0, 0x7f, 0x90, 0x0, 0x1,
    0xff, 0x0, 0x0, 0xa, 0xf6, 0x0, 0x0, 0x2f,
    0xd0, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x0, 0xef,
    0x10,

    /* U+0029 ")" */
    0x6f, 0x90, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x4,
    0xfc, 0x0, 0x0, 0xd, 0xf3, 0x0, 0x0, 0x7f,
    0xa0, 0x0, 0x1, 0xff, 0x10, 0x0, 0xc, 0xf5,
    0x0, 0x0, 0x8f, 0x90, 0x0, 0x4, 0xfd, 0x0,
    0x0, 0x3f, 0xe0, 0x0, 0x1, 0xff, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x3f,
    0xe0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x8f, 0x80,
    0x0, 0xc, 0xf4, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x6f, 0x90, 0x0, 0xd, 0xf2, 0x0, 0x5, 0xfa,
    0x0, 0x0, 0xdf, 0x20, 0x0, 0x7f, 0x70, 0x0,
    0x0,

    /* U+002A "*" */
    0x0, 0x0, 0xdf, 0x20, 0x0, 0x0, 0x0, 0xcf,
    0x0, 0x0, 0xb, 0x82, 0xaf, 0x6, 0xd1, 0x2f,
    0xff, 0xff, 0xff, 0xf7, 0x1, 0x5a, 0xff, 0xc6,
    0x20, 0x0, 0xc, 0xfd, 0xf2, 0x0, 0x0, 0xaf,
    0x83, 0xfe, 0x10, 0x0, 0xae, 0x0, 0x9d, 0x20,
    0x0, 0x1, 0x0, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x26, 0x30, 0x0, 0x0,

    /* U+002C "," */
    0x0, 0xe, 0xf1, 0x0, 0xf, 0xe0, 0x0, 0x5f,
    0x90, 0x0, 0xbf, 0x20, 0x3, 0xf9, 0x0, 0xc,
    0xe1, 0x0,

    /* U+002D "-" */
    0x2b, 0xbb, 0xbb, 0xb9, 0x4f, 0xff, 0xff, 0xfd,

    /* U+002E "." */
    0x9f, 0xc0, 0xef, 0xf2, 0x9f, 0xd0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0xe, 0xf0, 0x0, 0x0, 0x0,
    0x3f, 0x90, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x0,
    0x0, 0x0, 0xee, 0x0, 0x0, 0x0, 0x5, 0xf8,
    0x0, 0x0, 0x0, 0xa, 0xf2, 0x0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x0, 0x6f, 0x70, 0x0,
    0x0, 0x0, 0xcf, 0x10, 0x0, 0x0, 0x1, 0xfb,
    0x0, 0x0, 0x0, 0x7, 0xf6, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x0, 0x0, 0x3f, 0xa0, 0x0,
    0x0, 0x0, 0x8f, 0x50, 0x0, 0x0, 0x0, 0xee,
    0x0, 0x0, 0x0, 0x4, 0xf9, 0x0, 0x0, 0x0,
    0x9, 0xf3, 0x0, 0x0, 0x0, 0xf, 0xe0, 0x0,
    0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x19, 0xdf, 0xfc, 0x70, 0x0, 0x2, 0xef,
    0xfd, 0xdf, 0xfc, 0x0, 0xb, 0xfc, 0x10, 0x2,
    0xdf, 0x80, 0x2f, 0xf2, 0x0, 0x0, 0x4f, 0xe0,
    0x6f, 0xc0, 0x0, 0x0, 0xf, 0xf3, 0x9f, 0xa0,
    0x0, 0x0, 0xc, 0xf5, 0xaf, 0x80, 0x0, 0x0,
    0xb, 0xf7, 0xbf, 0x70, 0x0, 0x0, 0xa, 0xf8,
    0xbf, 0x70, 0x0, 0x0, 0xa, 0xf8, 0xaf, 0x80,
    0x0, 0x0, 0xb, 0xf7, 0x9f, 0x90, 0x0, 0x0,
    0xc, 0xf5, 0x6f, 0xc0, 0x0, 0x0, 0xf, 0xf3,
    0x2f, 0xf1, 0x0, 0x0, 0x4f, 0xe0, 0xb, 0xfb,
    0x10, 0x2, 0xdf, 0x80, 0x2, 0xef, 0xfc, 0xdf,
    0xfc, 0x0, 0x0, 0x19, 0xdf, 0xfd, 0x70, 0x0,

    /* U+0031 "1" */
    0x3, 0x7a, 0xdf, 0xb9, 0xff, 0xff, 0xfb, 0x57,
    0x41, 0x7f, 0xb0, 0x0, 0x7, 0xfb, 0x0, 0x0,
    0x7f, 0xb0, 0x0, 0x7, 0xfb, 0x0, 0x0, 0x7f,
    0xb0, 0x0, 0x7, 0xfb, 0x0, 0x0, 0x7f, 0xb0,
    0x0, 0x7, 0xfb, 0x0, 0x0, 0x7f, 0xb0, 0x0,
    0x7, 0xfb, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x7,
    0xfb, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x7, 0xfb,

    /* U+0032 "2" */
    0x5, 0xce, 0xff, 0xeb, 0x50, 0x0, 0x7, 0xfd,
    0xcd, 0xff, 0xfa, 0x0, 0x1, 0x0, 0x0, 0x7,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x6f, 0xe3,
    0x0, 0x0, 0x0, 0x9, 0xfd, 0x20, 0x0, 0x0,
    0x1, 0xcf, 0xb0, 0x0, 0x0, 0x0, 0x4e, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfc, 0xcc, 0xcc,
    0xcc, 0xc4, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xf5,

    /* U+0033 "3" */
    0x5, 0xce, 0xff, 0xeb, 0x50, 0x0, 0x8f, 0xdc,
    0xce, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x30, 0x0, 0x0, 0x0, 0xb, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x40, 0x0, 0x0, 0x0, 0x7f,
    0xd0, 0x0, 0x3a, 0xab, 0xff, 0xa1, 0x0, 0x5,
    0xff, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x1, 0x4b,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0xa, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xb0, 0x0, 0x0, 0x0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0,
    0x0, 0x0, 0x1, 0x7f, 0xf4, 0x5f, 0xdc, 0xcd,
    0xff, 0xf8, 0x3, 0xce, 0xff, 0xfd, 0x93, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x3, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x7f, 0x84,
    0xfb, 0x0, 0x0, 0x0, 0x4f, 0xc0, 0x4f, 0xb0,
    0x0, 0x0, 0x1e, 0xf2, 0x4, 0xfb, 0x0, 0x0,
    0xb, 0xf5, 0x0, 0x4f, 0xb0, 0x0, 0x7, 0xf9,
    0x0, 0x4, 0xfb, 0x0, 0x4, 0xfd, 0x0, 0x0,
    0x4f, 0xb0, 0x0, 0xef, 0x30, 0x0, 0x4, 0xfb,
    0x0, 0x2f, 0xfc, 0xcc, 0xcc, 0xdf, 0xfc, 0x82,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfb, 0x0,

    /* U+0035 "5" */
    0xd, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xdd,
    0xdd, 0xdd, 0x0, 0x2f, 0xb0, 0x0, 0x0, 0x0,
    0x4, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x50,
    0x0, 0x0, 0x0, 0x9, 0xfa, 0x99, 0x85, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0x60, 0x4, 0x20,
    0x1, 0x39, 0xff, 0x50, 0x0, 0x0, 0x0, 0x7,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x6, 0xfd, 0x1,
    0x0, 0x0, 0x6, 0xff, 0x50, 0xee, 0xcc, 0xdf,
    0xff, 0x80, 0xb, 0xef, 0xff, 0xd9, 0x30, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x5, 0xad, 0xe4, 0x0, 0x0, 0x3,
    0xdf, 0xff, 0xd3, 0x0, 0x0, 0x4f, 0xf9, 0x20,
    0x0, 0x0, 0x1, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x9, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xf, 0xc3,
    0xbe, 0xfd, 0x91, 0x0, 0x4f, 0xef, 0xeb, 0xbe,
    0xfe, 0x20, 0x7f, 0xf7, 0x0, 0x0, 0xaf, 0xb0,
    0x8f, 0xc0, 0x0, 0x0, 0x1f, 0xf1, 0x9f, 0x80,
    0x0, 0x0, 0xc, 0xf4, 0x9f, 0x70, 0x0, 0x0,
    0xb, 0xf4, 0x7f, 0x90, 0x0, 0x0, 0xc, 0xf3,
    0x4f, 0xe0, 0x0, 0x0, 0x2f, 0xf0, 0xd, 0xfa,
    0x0, 0x2, 0xcf, 0x90, 0x3, 0xef, 0xfc, 0xdf,
    0xfc, 0x0, 0x0, 0x19, 0xdf, 0xfc, 0x70, 0x0,

    /* U+0037 "7" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x9d, 0xdd,
    0xdd, 0xdd, 0xdf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0x6, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0,
    0x7, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0, 0x7,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x4a, 0xef, 0xfd, 0x92, 0x0, 0x6, 0xff,
    0xeb, 0xce, 0xff, 0x30, 0xf, 0xf6, 0x0, 0x0,
    0x9f, 0xc0, 0x3f, 0xc0, 0x0, 0x0, 0xf, 0xf0,
    0x2f, 0xc0, 0x0, 0x0, 0xf, 0xf0, 0xd, 0xf4,
    0x0, 0x0, 0x8f, 0xa0, 0x2, 0xcf, 0xb9, 0x9d,
    0xfb, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xe6, 0x0,
    0xc, 0xf9, 0x20, 0x13, 0xbf, 0x90, 0x6f, 0xa0,
    0x0, 0x0, 0xd, 0xf3, 0xbf, 0x50, 0x0, 0x0,
    0x8, 0xf7, 0xcf, 0x40, 0x0, 0x0, 0x7, 0xf8,
    0xaf, 0x70, 0x0, 0x0, 0xb, 0xf7, 0x5f, 0xf4,
    0x0, 0x0, 0x6f, 0xf2, 0xa, 0xff, 0xdb, 0xce,
    0xff, 0x70, 0x0, 0x5b, 0xef, 0xfd, 0xa3, 0x0,

    /* U+0039 "9" */
    0x0, 0x29, 0xdf, 0xfc, 0x60, 0x0, 0x4, 0xff,
    0xfc, 0xdf, 0xfb, 0x0, 0xe, 0xf8, 0x0, 0x2,
    0xdf, 0x70, 0x6f, 0xc0, 0x0, 0x0, 0x2f, 0xe0,
    0x9f, 0x70, 0x0, 0x0, 0xd, 0xf2, 0x9f, 0x60,
    0x0, 0x0, 0xb, 0xf4, 0x7f, 0x90, 0x0, 0x0,
    0xe, 0xf4, 0x3f, 0xf4, 0x0, 0x0, 0x8f, 0xf3,
    0x8, 0xff, 0xca, 0xae, 0xff, 0xf2, 0x0, 0x5b,
    0xef, 0xe9, 0x2f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x50,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x0, 0x0, 0x0,
    0x5, 0xdf, 0xe2, 0x0, 0x0, 0x6d, 0xff, 0xfb,
    0x10, 0x0, 0x0, 0x8e, 0xd9, 0x30, 0x0, 0x0,

    /* U+003A ":" */
    0xbf, 0xc0, 0xff, 0xf0, 0xbf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0xff, 0xf0,
    0xbf, 0xc0,

    /* U+003B ";" */
    0x0, 0xbf, 0xc0, 0x0, 0xff, 0xf0, 0x0, 0xbf,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x3f,
    0xb0, 0x0, 0x6f, 0x80, 0x0, 0xbf, 0x20, 0x1,
    0xfc, 0x0, 0x9, 0xf3, 0x0, 0x2f, 0x90, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xb6, 0x0, 0x0, 0x0,
    0x2, 0x8e, 0xff, 0x60, 0x0, 0x0, 0x5b, 0xff,
    0xfa, 0x40, 0x0, 0x28, 0xef, 0xfd, 0x71, 0x0,
    0x1, 0xbf, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x2f,
    0xd7, 0x10, 0x0, 0x0, 0x0, 0x2, 0xff, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfd, 0x71,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xfa, 0x30,
    0x0, 0x0, 0x0, 0x2, 0x9f, 0xff, 0xd7, 0x10,
    0x0, 0x0, 0x0, 0x6, 0xcf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x29, 0x50,

    /* U+003D "=" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x62, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x62, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xd7,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x28, 0xef, 0xfd, 0x71,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xfa, 0x30,
    0x0, 0x0, 0x0, 0x2, 0x8e, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0x2, 0x8e, 0xf6, 0x0, 0x0, 0x0, 0x5b,
    0xff, 0xfa, 0x20, 0x0, 0x28, 0xef, 0xfd, 0x71,
    0x0, 0x5, 0xbf, 0xff, 0xa4, 0x0, 0x0, 0x2,
    0xff, 0xd7, 0x10, 0x0, 0x0, 0x0, 0x2a, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x9d, 0xff, 0xfd, 0xa2, 0xf, 0xff, 0xef, 0xff,
    0xf3, 0x30, 0x0, 0x3, 0xef, 0xa0, 0x0, 0x0,
    0x8, 0xfc, 0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0,
    0x0, 0xb, 0xf9, 0x0, 0x0, 0x8, 0xff, 0x20,
    0x0, 0xa, 0xfe, 0x40, 0x0, 0xb, 0xfb, 0x10,
    0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x5f, 0x90,
    0x0, 0x0, 0x1, 0x42, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x0,
    0xef, 0xf1, 0x0, 0x0, 0xa, 0xfd, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x28, 0xce, 0xff, 0xda, 0x50,
    0x0, 0x0, 0x0, 0x2, 0xbf, 0xfe, 0xba, 0xbd,
    0xff, 0xd3, 0x0, 0x0, 0x4, 0xff, 0xa3, 0x0,
    0x0, 0x1, 0x9f, 0xf3, 0x0, 0x3, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xe0, 0x1, 0xef,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x60,
    0x8f, 0x70, 0x0, 0x3b, 0xff, 0xa6, 0xf6, 0x3,
    0xfa, 0xf, 0xe0, 0x0, 0x5f, 0xf9, 0x9e, 0xff,
    0x30, 0xf, 0xd4, 0xf9, 0x0, 0x2f, 0xe2, 0x0,
    0x4f, 0xf0, 0x0, 0xfe, 0x7f, 0x60, 0x8, 0xf7,
    0x0, 0x1, 0xfd, 0x0, 0xf, 0xd9, 0xf4, 0x0,
    0xcf, 0x10, 0x0, 0x2f, 0xa0, 0x1, 0xfc, 0xaf,
    0x30, 0xe, 0xf0, 0x0, 0x6, 0xf7, 0x0, 0x4f,
    0x88, 0xf4, 0x0, 0xef, 0x0, 0x0, 0xcf, 0x50,
    0x9, 0xf3, 0x6f, 0x70, 0xb, 0xf7, 0x2, 0xaf,
    0xfa, 0x5, 0xfb, 0x2, 0xfc, 0x0, 0x2e, 0xff,
    0xff, 0x8b, 0xff, 0xfc, 0x10, 0xb, 0xf4, 0x0,
    0x16, 0x87, 0x20, 0x6, 0x85, 0x0, 0x0, 0x3f,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf8, 0x20, 0x0, 0x0, 0x0, 0x21,
    0x0, 0x0, 0x0, 0x3d, 0xff, 0xec, 0xba, 0xbd,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef,
    0xfe, 0xc9, 0x50, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x9f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfa, 0xaf, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf4, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xd0, 0xd, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x70, 0x7, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x6, 0xfa, 0x0, 0x0, 0xaf, 0x60, 0x0,
    0x0, 0xc, 0xf4, 0x0, 0x0, 0x4f, 0xc0, 0x0,
    0x0, 0x2f, 0xfe, 0xee, 0xee, 0xef, 0xf3, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0xef, 0x10, 0x0, 0x0, 0x2, 0xff, 0x0,
    0x5, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x50,
    0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xc0,
    0x2f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2,
    0x8f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf8,

    /* U+0042 "B" */
    0xcf, 0xff, 0xff, 0xec, 0x70, 0x0, 0xcf, 0xdc,
    0xcd, 0xff, 0xfc, 0x0, 0xcf, 0x60, 0x0, 0x4,
    0xff, 0x50, 0xcf, 0x60, 0x0, 0x0, 0xaf, 0x80,
    0xcf, 0x60, 0x0, 0x0, 0x8f, 0x80, 0xcf, 0x60,
    0x0, 0x0, 0xbf, 0x60, 0xcf, 0x60, 0x0, 0x39,
    0xfd, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xa1, 0x0,
    0xcf, 0xcb, 0xbb, 0xdf, 0xfc, 0x10, 0xcf, 0x60,
    0x0, 0x1, 0xbf, 0xb0, 0xcf, 0x60, 0x0, 0x0,
    0x2f, 0xf0, 0xcf, 0x60, 0x0, 0x0, 0xf, 0xf2,
    0xcf, 0x60, 0x0, 0x0, 0x2f, 0xf1, 0xcf, 0x60,
    0x0, 0x1, 0xbf, 0xd0, 0xcf, 0xed, 0xdd, 0xef,
    0xff, 0x40, 0xcf, 0xff, 0xff, 0xfd, 0x92, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x49, 0xde, 0xff, 0xda, 0x10, 0x1,
    0xbf, 0xff, 0xfe, 0xff, 0xf2, 0x0, 0xcf, 0xf7,
    0x10, 0x0, 0x3, 0x0, 0x5f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfe, 0x51,
    0x0, 0x1, 0x42, 0x0, 0x3e, 0xff, 0xff, 0xef,
    0xff, 0x50, 0x0, 0x6, 0xbe, 0xff, 0xec, 0x91,

    /* U+0044 "D" */
    0xcf, 0xff, 0xff, 0xec, 0x94, 0x0, 0x0, 0xcf,
    0xed, 0xde, 0xff, 0xff, 0xb0, 0x0, 0xcf, 0x60,
    0x0, 0x3, 0x9f, 0xfa, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x7, 0xff, 0x30, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0xdf, 0x90, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0x7f, 0xd0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x4f,
    0xf0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0x7f, 0xc0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0xdf, 0x80, 0xcf, 0x60, 0x0, 0x0,
    0x8, 0xff, 0x20, 0xcf, 0x60, 0x0, 0x3, 0xaf,
    0xf8, 0x0, 0xcf, 0xed, 0xde, 0xff, 0xff, 0x80,
    0x0, 0xcf, 0xff, 0xff, 0xec, 0x82, 0x0, 0x0,

    /* U+0045 "E" */
    0xcf, 0xff, 0xff, 0xff, 0xf7, 0xc, 0xfe, 0xee,
    0xee, 0xee, 0x60, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xdc, 0xcc, 0xcc, 0xc0, 0xc, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xfe, 0xee,
    0xee, 0xee, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xf1,

    /* U+0046 "F" */
    0xcf, 0xff, 0xff, 0xff, 0xf7, 0xcf, 0xee, 0xee,
    0xee, 0xe6, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf0,
    0xcf, 0xee, 0xee, 0xee, 0xe0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x39, 0xce, 0xff, 0xeb, 0x80, 0x0,
    0x1b, 0xff, 0xff, 0xee, 0xff, 0xf1, 0x0, 0xbf,
    0xf7, 0x20, 0x0, 0x1, 0x40, 0x5, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0xdf, 0xff, 0xf8,
    0x3f, 0xf0, 0x0, 0x0, 0xac, 0xce, 0xf8, 0x2f,
    0xf1, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x1f, 0xf3,
    0x0, 0x0, 0x0, 0x8, 0xf8, 0xd, 0xf8, 0x0,
    0x0, 0x0, 0x8, 0xf8, 0x8, 0xfe, 0x10, 0x0,
    0x0, 0x8, 0xf8, 0x1, 0xef, 0xe5, 0x0, 0x0,
    0x9, 0xf8, 0x0, 0x2d, 0xff, 0xfe, 0xde, 0xff,
    0xf8, 0x0, 0x0, 0x6b, 0xdf, 0xff, 0xec, 0x93,

    /* U+0048 "H" */
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x6f, 0xcc, 0xf6,
    0x0, 0x0, 0x0, 0x6, 0xfc, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0x6f, 0xcc, 0xf6, 0x0, 0x0, 0x0,
    0x6, 0xfc, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x6f,
    0xcc, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xfc, 0xcf,
    0xdc, 0xcc, 0xcc, 0xcc, 0xdf, 0xcc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0x6f, 0xcc, 0xf6, 0x0, 0x0, 0x0, 0x6,
    0xfc, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x6f, 0xcc,
    0xf6, 0x0, 0x0, 0x0, 0x6, 0xfc, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0x6f, 0xcc, 0xf6, 0x0, 0x0,
    0x0, 0x6, 0xfc, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0x6f, 0xcc, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xfc,

    /* U+0049 "I" */
    0xcf, 0x6c, 0xf6, 0xcf, 0x6c, 0xf6, 0xcf, 0x6c,
    0xf6, 0xcf, 0x6c, 0xf6, 0xcf, 0x6c, 0xf6, 0xcf,
    0x6c, 0xf6, 0xcf, 0x6c, 0xf6, 0xcf, 0x6c, 0xf6,

    /* U+004A "J" */
    0x0, 0x0, 0xcf, 0x60, 0x0, 0xc, 0xf6, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0xc, 0xf6, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0xc, 0xf6, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0xcf, 0x60, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0xcf, 0x60, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0xdf, 0x50, 0x0, 0xf, 0xf4, 0x0,
    0x9, 0xff, 0xc, 0xef, 0xff, 0x60, 0xcf, 0xfc,
    0x50, 0x0,

    /* U+004B "K" */
    0xcf, 0x60, 0x0, 0x0, 0x2e, 0xf9, 0xc, 0xf6,
    0x0, 0x0, 0x2e, 0xf9, 0x0, 0xcf, 0x60, 0x0,
    0x1e, 0xf9, 0x0, 0xc, 0xf6, 0x0, 0x1d, 0xf9,
    0x0, 0x0, 0xcf, 0x60, 0x1d, 0xfa, 0x0, 0x0,
    0xc, 0xf6, 0xc, 0xfa, 0x0, 0x0, 0x0, 0xcf,
    0xee, 0xfa, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xcf, 0x62, 0xff, 0x70,
    0x0, 0x0, 0xc, 0xf6, 0x5, 0xff, 0x40, 0x0,
    0x0, 0xcf, 0x60, 0x9, 0xfe, 0x10, 0x0, 0xc,
    0xf6, 0x0, 0xd, 0xfc, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x2f, 0xf9, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x5f, 0xf5, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x9f,
    0xf2, 0xc, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0xd0,

    /* U+004C "L" */
    0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,
    0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xcf, 0xfe,
    0xee, 0xee, 0xe8, 0xcf, 0xff, 0xff, 0xff, 0xf9,

    /* U+004D "M" */
    0xcf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfb,
    0xcf, 0xff, 0x40, 0x0, 0x0, 0x5, 0xff, 0xfb,
    0xcf, 0xaf, 0xa0, 0x0, 0x0, 0xb, 0xfb, 0xfb,
    0xcf, 0x5e, 0xf1, 0x0, 0x0, 0x2f, 0xd7, 0xfb,
    0xcf, 0x58, 0xf8, 0x0, 0x0, 0x8f, 0x67, 0xfb,
    0xcf, 0x51, 0xfe, 0x0, 0x0, 0xef, 0x7, 0xfb,
    0xcf, 0x50, 0xbf, 0x50, 0x5, 0xf9, 0x7, 0xfb,
    0xcf, 0x50, 0x4f, 0xb0, 0xc, 0xf2, 0x7, 0xfb,
    0xcf, 0x50, 0xd, 0xf2, 0x3f, 0xc0, 0x7, 0xfb,
    0xcf, 0x50, 0x6, 0xf9, 0x9f, 0x50, 0x7, 0xfb,
    0xcf, 0x50, 0x0, 0xff, 0xfe, 0x0, 0x7, 0xfb,
    0xcf, 0x50, 0x0, 0x9f, 0xf8, 0x0, 0x7, 0xfb,
    0xcf, 0x50, 0x0, 0x16, 0x61, 0x0, 0x7, 0xfb,
    0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfb,
    0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfb,
    0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfb,

    /* U+004E "N" */
    0xcf, 0xfd, 0x0, 0x0, 0x0, 0xb, 0xf7, 0xcf,
    0xff, 0x70, 0x0, 0x0, 0xb, 0xf7, 0xcf, 0x8f,
    0xf1, 0x0, 0x0, 0xb, 0xf7, 0xcf, 0x59, 0xf9,
    0x0, 0x0, 0xb, 0xf7, 0xcf, 0x51, 0xff, 0x20,
    0x0, 0xb, 0xf7, 0xcf, 0x50, 0x7f, 0xb0, 0x0,
    0xb, 0xf7, 0xcf, 0x50, 0xd, 0xf4, 0x0, 0xb,
    0xf7, 0xcf, 0x50, 0x4, 0xfd, 0x0, 0xb, 0xf7,
    0xcf, 0x50, 0x0, 0xbf, 0x70, 0xb, 0xf7, 0xcf,
    0x50, 0x0, 0x2f, 0xe1, 0xb, 0xf7, 0xcf, 0x50,
    0x0, 0x8, 0xf9, 0xb, 0xf7, 0xcf, 0x50, 0x0,
    0x1, 0xef, 0x2b, 0xf7, 0xcf, 0x50, 0x0, 0x0,
    0x6f, 0xbb, 0xf7, 0xcf, 0x50, 0x0, 0x0, 0xd,
    0xfe, 0xf7, 0xcf, 0x50, 0x0, 0x0, 0x3, 0xff,
    0xf7, 0xcf, 0x50, 0x0, 0x0, 0x0, 0xaf, 0xf7,

    /* U+004F "O" */
    0x0, 0x1, 0x7c, 0xef, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x4e, 0xff, 0xed, 0xdf, 0xff, 0xd2, 0x0,
    0x1, 0xff, 0xb3, 0x0, 0x0, 0x4d, 0xfd, 0x0,
    0x9, 0xfd, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xe0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xe0,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xb0,
    0x9, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xef, 0x60,
    0x2, 0xff, 0xb2, 0x0, 0x0, 0x4d, 0xfd, 0x0,
    0x0, 0x4f, 0xff, 0xed, 0xdf, 0xff, 0xe2, 0x0,
    0x0, 0x1, 0x8c, 0xef, 0xfe, 0xb7, 0x0, 0x0,

    /* U+0050 "P" */
    0xcf, 0xff, 0xff, 0xeb, 0x70, 0xc, 0xfe, 0xdd,
    0xef, 0xff, 0xd1, 0xcf, 0x60, 0x0, 0x5, 0xff,
    0x8c, 0xf6, 0x0, 0x0, 0x8, 0xfc, 0xcf, 0x60,
    0x0, 0x0, 0x5f, 0xec, 0xf6, 0x0, 0x0, 0x4,
    0xfe, 0xcf, 0x60, 0x0, 0x0, 0x8f, 0xcc, 0xf6,
    0x0, 0x0, 0x4f, 0xf7, 0xcf, 0xdc, 0xcc, 0xef,
    0xfc, 0xc, 0xff, 0xff, 0xfe, 0xb6, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x1, 0x7c, 0xef, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x4e, 0xff, 0xed, 0xdf, 0xff, 0xd2, 0x0,
    0x1, 0xff, 0xb3, 0x0, 0x0, 0x4d, 0xfd, 0x0,
    0x9, 0xfd, 0x0, 0x0, 0x0, 0x1, 0xff, 0x50,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xe0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xe0,
    0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xb0,
    0x9, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xef, 0x60,
    0x2, 0xff, 0xb2, 0x0, 0x0, 0x4d, 0xfd, 0x0,
    0x0, 0x4f, 0xff, 0xed, 0xdf, 0xff, 0xd2, 0x0,
    0x0, 0x1, 0x8c, 0xef, 0xff, 0xc6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0x70, 0x0,

    /* U+0052 "R" */
    0xcf, 0xff, 0xff, 0xdb, 0x60, 0x0, 0xc, 0xfe,
    0xdd, 0xef, 0xff, 0xb0, 0x0, 0xcf, 0x60, 0x0,
    0x6, 0xff, 0x60, 0xc, 0xf6, 0x0, 0x0, 0x9,
    0xfa, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x6f, 0xc0,
    0xc, 0xf6, 0x0, 0x0, 0x7, 0xfc, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0xaf, 0x90, 0xc, 0xf6, 0x0,
    0x2, 0x8f, 0xf4, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0xc, 0xfd, 0xcc, 0xef, 0xb1, 0x0,
    0x0, 0xcf, 0x60, 0x3, 0xff, 0x30, 0x0, 0xc,
    0xf6, 0x0, 0x7, 0xfd, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0xb, 0xfa, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x1e, 0xf7, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x4f,
    0xf3, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x8f, 0xe1,

    /* U+0053 "S" */
    0x0, 0x4a, 0xef, 0xfe, 0xda, 0x20, 0x7, 0xff,
    0xfe, 0xef, 0xff, 0x50, 0x1f, 0xf7, 0x0, 0x0,
    0x14, 0x20, 0x5f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x84, 0x0,
    0x0, 0x0, 0x1, 0x9f, 0xff, 0xfc, 0x70, 0x0,
    0x0, 0x0, 0x47, 0xcf, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x1, 0xaf, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x44, 0x10,
    0x0, 0x3, 0xcf, 0xd0, 0xaf, 0xff, 0xee, 0xff,
    0xff, 0x30, 0x4a, 0xce, 0xff, 0xfc, 0x81, 0x0,

    /* U+0054 "T" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x48, 0xee,
    0xee, 0xff, 0xee, 0xee, 0xe3, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0,

    /* U+0055 "U" */
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x6f, 0xbd, 0xf4,
    0x0, 0x0, 0x0, 0x6, 0xfb, 0xdf, 0x40, 0x0,
    0x0, 0x0, 0x6f, 0xbd, 0xf4, 0x0, 0x0, 0x0,
    0x6, 0xfb, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x6f,
    0xbd, 0xf4, 0x0, 0x0, 0x0, 0x6, 0xfb, 0xdf,
    0x40, 0x0, 0x0, 0x0, 0x6f, 0xbd, 0xf4, 0x0,
    0x0, 0x0, 0x6, 0xfb, 0xdf, 0x40, 0x0, 0x0,
    0x0, 0x6f, 0xbd, 0xf4, 0x0, 0x0, 0x0, 0x6,
    0xfb, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x7f, 0xbc,
    0xf6, 0x0, 0x0, 0x0, 0x8, 0xfa, 0xaf, 0xa0,
    0x0, 0x0, 0x0, 0xcf, 0x74, 0xff, 0x81, 0x0,
    0x1, 0x9f, 0xf1, 0x9, 0xff, 0xfe, 0xde, 0xff,
    0xf5, 0x0, 0x4, 0xad, 0xff, 0xfc, 0x82, 0x0,

    /* U+0056 "V" */
    0x6f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x41,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0xb,
    0xf8, 0x0, 0x0, 0x0, 0x9, 0xf8, 0x0, 0x6f,
    0xd0, 0x0, 0x0, 0x0, 0xef, 0x30, 0x0, 0xff,
    0x30, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0xa, 0xf8,
    0x0, 0x0, 0xa, 0xf7, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0xff, 0x30,
    0x0, 0x5f, 0xc0, 0x0, 0x0, 0x9, 0xf9, 0x0,
    0xa, 0xf6, 0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x0, 0xef, 0x30, 0x5f,
    0xb0, 0x0, 0x0, 0x0, 0x8, 0xf9, 0xb, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xe1, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xaf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xfe, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x4f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xc0, 0xff, 0x30, 0x0, 0x0, 0x2,
    0x20, 0x0, 0x0, 0x9, 0xf8, 0xc, 0xf7, 0x0,
    0x0, 0xa, 0xff, 0x40, 0x0, 0x0, 0xdf, 0x40,
    0x8f, 0xa0, 0x0, 0x0, 0xef, 0xf9, 0x0, 0x0,
    0xf, 0xf1, 0x4, 0xfe, 0x0, 0x0, 0x3f, 0xbf,
    0xe0, 0x0, 0x4, 0xfc, 0x0, 0xf, 0xf2, 0x0,
    0x8, 0xf4, 0xcf, 0x20, 0x0, 0x8f, 0x80, 0x0,
    0xcf, 0x50, 0x0, 0xcf, 0x7, 0xf7, 0x0, 0xb,
    0xf5, 0x0, 0x8, 0xf9, 0x0, 0x1f, 0xb0, 0x3f,
    0xb0, 0x0, 0xff, 0x10, 0x0, 0x4f, 0xd0, 0x6,
    0xf6, 0x0, 0xef, 0x0, 0x3f, 0xd0, 0x0, 0x1,
    0xff, 0x10, 0xaf, 0x20, 0xa, 0xf5, 0x6, 0xf9,
    0x0, 0x0, 0xd, 0xf4, 0xf, 0xd0, 0x0, 0x5f,
    0x90, 0xaf, 0x50, 0x0, 0x0, 0x9f, 0x84, 0xf8,
    0x0, 0x1, 0xfe, 0xe, 0xf1, 0x0, 0x0, 0x5,
    0xfc, 0x8f, 0x40, 0x0, 0xc, 0xf4, 0xfd, 0x0,
    0x0, 0x0, 0x1f, 0xfd, 0xf0, 0x0, 0x0, 0x7f,
    0xdf, 0x90, 0x0, 0x0, 0x0, 0xdf, 0xfa, 0x0,
    0x0, 0x3, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x60, 0x0, 0x0, 0xe, 0xff, 0x10, 0x0,

    /* U+0058 "X" */
    0xa, 0xfd, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0,
    0x1e, 0xf6, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0,
    0x6f, 0xf1, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0,
    0xbf, 0xa0, 0x0, 0x6f, 0xd0, 0x0, 0x0, 0x2,
    0xff, 0x30, 0x1e, 0xf3, 0x0, 0x0, 0x0, 0x7,
    0xfd, 0xa, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xfb, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x6f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x8f,
    0xc0, 0x0, 0x0, 0x0, 0x6f, 0xd0, 0x0, 0xef,
    0x70, 0x0, 0x0, 0x2f, 0xf4, 0x0, 0x4, 0xff,
    0x20, 0x0, 0xc, 0xf9, 0x0, 0x0, 0xa, 0xfc,
    0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x1f, 0xf6,
    0x2, 0xff, 0x40, 0x0, 0x0, 0x0, 0x6f, 0xf2,

    /* U+0059 "Y" */
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0,
    0xdf, 0x90, 0x0, 0x0, 0x3, 0xff, 0x20, 0x4,
    0xff, 0x30, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x9,
    0xfc, 0x0, 0x0, 0x6f, 0xd0, 0x0, 0x0, 0xe,
    0xf6, 0x0, 0x1f, 0xf3, 0x0, 0x0, 0x0, 0x4f,
    0xf1, 0xa, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xa4, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xef, 0x30, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x71, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x2, 0xef, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x5, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+005B "[" */
    0x5f, 0xff, 0xf3, 0x5f, 0xfc, 0xc2, 0x5f, 0xc0,
    0x0, 0x5f, 0xc0, 0x0, 0x5f, 0xc0, 0x0, 0x5f,
    0xc0, 0x0, 0x5f, 0xc0, 0x0, 0x5f, 0xc0, 0x0,
    0x5f, 0xc0, 0x0, 0x5f, 0xc0, 0x0, 0x5f, 0xc0,
    0x0, 0x5f, 0xc0, 0x0, 0x5f, 0xc0, 0x0, 0x5f,
    0xc0, 0x0, 0x5f, 0xc0, 0x0, 0x5f, 0xc0, 0x0,
    0x5f, 0xc0, 0x0, 0x5f, 0xc0, 0x0, 0x5f, 0xc0,
    0x0, 0x5f, 0xc0, 0x0, 0x5f, 0xc0, 0x0, 0x5f,
    0xfb, 0xb2, 0x5f, 0xff, 0xf3,

    /* U+005C "\\" */
    0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x80, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0x80, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x84,

    /* U+005D "]" */
    0x3f, 0xff, 0xf6, 0x2c, 0xce, 0xf6, 0x0, 0xb,
    0xf6, 0x0, 0xb, 0xf6, 0x0, 0xb, 0xf6, 0x0,
    0xb, 0xf6, 0x0, 0xb, 0xf6, 0x0, 0xb, 0xf6,
    0x0, 0xb, 0xf6, 0x0, 0xb, 0xf6, 0x0, 0xb,
    0xf6, 0x0, 0xb, 0xf6, 0x0, 0xb, 0xf6, 0x0,
    0xb, 0xf6, 0x0, 0xb, 0xf6, 0x0, 0xb, 0xf6,
    0x0, 0xb, 0xf6, 0x0, 0xb, 0xf6, 0x0, 0xb,
    0xf6, 0x0, 0xb, 0xf6, 0x0, 0xb, 0xf6, 0x2b,
    0xbe, 0xf6, 0x3f, 0xff, 0xf6,

    /* U+005E "^" */
    0x0, 0x0, 0xef, 0xf4, 0x0, 0x0, 0x0, 0x6f,
    0xef, 0xa0, 0x0, 0x0, 0xc, 0xf3, 0xef, 0x10,
    0x0, 0x3, 0xfc, 0x8, 0xf8, 0x0, 0x0, 0xaf,
    0x60, 0x2f, 0xe0, 0x0, 0x1f, 0xe0, 0x0, 0xbf,
    0x50, 0x7, 0xf8, 0x0, 0x5, 0xfb, 0x0, 0xef,
    0x20, 0x0, 0xe, 0xf2, 0x4f, 0xb0, 0x0, 0x0,
    0x7f, 0x80,

    /* U+005F "_" */
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff,

    /* U+0060 "`" */
    0x38, 0x81, 0x0, 0xc, 0xf9, 0x0, 0x1, 0xdf,
    0x20, 0x0, 0x2e, 0xb0,

    /* U+0061 "a" */
    0x0, 0x8, 0xff, 0xfe, 0xc6, 0x0, 0x0, 0x8,
    0xcb, 0xac, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf7, 0x0, 0x26,
    0x89, 0xaa, 0xad, 0xf7, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x4f, 0xf6, 0x10, 0x0, 0x9, 0xf7,
    0x9f, 0x90, 0x0, 0x0, 0x9, 0xf7, 0xaf, 0x70,
    0x0, 0x0, 0xd, 0xf7, 0x8f, 0xb0, 0x0, 0x1,
    0xbf, 0xf7, 0x1e, 0xfc, 0x88, 0xaf, 0xea, 0xf7,
    0x2, 0xae, 0xff, 0xc7, 0x5, 0xf7,

    /* U+0062 "b" */
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x43,
    0xbe, 0xfe, 0xb4, 0x0, 0xdf, 0x7f, 0xfc, 0xbe,
    0xff, 0x70, 0xdf, 0xe8, 0x0, 0x0, 0x8f, 0xf2,
    0xdf, 0xb0, 0x0, 0x0, 0xd, 0xf7, 0xdf, 0x50,
    0x0, 0x0, 0x8, 0xf9, 0xdf, 0x40, 0x0, 0x0,
    0x6, 0xfb, 0xdf, 0x20, 0x0, 0x0, 0x6, 0xfc,
    0xdf, 0x40, 0x0, 0x0, 0x6, 0xfb, 0xdf, 0x60,
    0x0, 0x0, 0x8, 0xf9, 0xdf, 0xb0, 0x0, 0x0,
    0xd, 0xf6, 0xdf, 0xf9, 0x0, 0x0, 0x8f, 0xf1,
    0xdf, 0x5f, 0xfc, 0xce, 0xff, 0x50, 0xdf, 0x3,
    0xbe, 0xfe, 0xa3, 0x0,

    /* U+0063 "c" */
    0x0, 0x18, 0xdf, 0xfe, 0xd8, 0x2, 0xef, 0xfd,
    0xcd, 0xfc, 0xc, 0xfb, 0x10, 0x0, 0x2, 0x3f,
    0xf1, 0x0, 0x0, 0x0, 0x6f, 0xc0, 0x0, 0x0,
    0x0, 0x8f, 0xa0, 0x0, 0x0, 0x0, 0x9f, 0xa0,
    0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0, 0x0, 0x0,
    0x6f, 0xc0, 0x0, 0x0, 0x0, 0x3f, 0xf1, 0x0,
    0x0, 0x0, 0xc, 0xfb, 0x10, 0x0, 0x2, 0x2,
    0xef, 0xfd, 0xcd, 0xfd, 0x0, 0x18, 0xdf, 0xff,
    0xd8,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x10, 0x1, 0x9d, 0xff, 0xc5, 0xf, 0xf1, 0x2,
    0xef, 0xfc, 0xbe, 0xf7, 0xff, 0x10, 0xcf, 0xc1,
    0x0, 0x5, 0xef, 0xf1, 0x2f, 0xf2, 0x0, 0x0,
    0x8, 0xff, 0x16, 0xfd, 0x0, 0x0, 0x0, 0x2f,
    0xf1, 0x7f, 0xb0, 0x0, 0x0, 0x0, 0xff, 0x18,
    0xfa, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x7f, 0xb0,
    0x0, 0x0, 0x0, 0xff, 0x16, 0xfc, 0x0, 0x0,
    0x0, 0x1f, 0xf1, 0x3f, 0xf0, 0x0, 0x0, 0x5,
    0xff, 0x10, 0xdf, 0x90, 0x0, 0x2, 0xef, 0xf1,
    0x3, 0xff, 0xc8, 0x8a, 0xf9, 0xef, 0x10, 0x2,
    0xae, 0xff, 0xc6, 0xb, 0xf1,

    /* U+0065 "e" */
    0x0, 0x18, 0xdf, 0xfe, 0xa2, 0x0, 0x2, 0xef,
    0xfc, 0xbd, 0xff, 0x40, 0xc, 0xfa, 0x0, 0x0,
    0x5f, 0xe0, 0x3f, 0xf1, 0x0, 0x0, 0xc, 0xf3,
    0x6f, 0xb0, 0x0, 0x0, 0x9, 0xf6, 0x8f, 0xb4,
    0x57, 0x89, 0xad, 0xf7, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x8f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfc, 0x20, 0x0,
    0x2, 0x50, 0x2, 0xef, 0xfe, 0xdd, 0xff, 0xc0,
    0x0, 0x18, 0xdf, 0xff, 0xda, 0x40,

    /* U+0066 "f" */
    0x0, 0x0, 0x8d, 0xff, 0x50, 0x0, 0xaf, 0xeb,
    0xa4, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x4, 0xfc,
    0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xb0, 0x6b, 0xdf, 0xeb, 0xb7, 0x0,
    0x6, 0xfa, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0x0,
    0x0, 0x6, 0xfa, 0x0, 0x0, 0x0, 0x6f, 0xa0,
    0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0, 0x6f,
    0xa0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0,
    0x6f, 0xa0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0,
    0x0, 0x6f, 0xa0, 0x0, 0x0, 0x6, 0xfa, 0x0,
    0x0,

    /* U+0067 "g" */
    0x0, 0x19, 0xdf, 0xfc, 0x50, 0xff, 0x10, 0x2e,
    0xff, 0xcb, 0xef, 0x6f, 0xf1, 0xc, 0xfb, 0x10,
    0x0, 0x5e, 0xff, 0x13, 0xff, 0x10, 0x0, 0x0,
    0x7f, 0xf1, 0x6f, 0xc0, 0x0, 0x0, 0x2, 0xff,
    0x17, 0xfb, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x8f,
    0xa0, 0x0, 0x0, 0x0, 0xef, 0x18, 0xfb, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x6f, 0xc0, 0x0, 0x0,
    0x1, 0xff, 0x13, 0xff, 0x10, 0x0, 0x0, 0x6f,
    0xf1, 0xd, 0xfb, 0x0, 0x0, 0x4e, 0xff, 0x10,
    0x4f, 0xff, 0xcb, 0xdf, 0x7f, 0xf1, 0x0, 0x2a,
    0xef, 0xfc, 0x60, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x40,
    0x0, 0x6e, 0xcb, 0xcf, 0xff, 0x70, 0x0, 0x4,
    0xde, 0xff, 0xd9, 0x30, 0x0,

    /* U+0068 "h" */
    0xdf, 0x40, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0xd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0x0, 0x0, 0xd, 0xf4, 0x4b, 0xef, 0xea,
    0x20, 0xdf, 0x9f, 0xfe, 0xdf, 0xfe, 0x1d, 0xff,
    0x91, 0x0, 0x2e, 0xf8, 0xdf, 0xa0, 0x0, 0x0,
    0x7f, 0xad, 0xf4, 0x0, 0x0, 0x5, 0xfb, 0xdf,
    0x40, 0x0, 0x0, 0x5f, 0xcd, 0xf4, 0x0, 0x0,
    0x5, 0xfc, 0xdf, 0x40, 0x0, 0x0, 0x5f, 0xcd,
    0xf4, 0x0, 0x0, 0x5, 0xfc, 0xdf, 0x40, 0x0,
    0x0, 0x5f, 0xcd, 0xf4, 0x0, 0x0, 0x5, 0xfc,
    0xdf, 0x40, 0x0, 0x0, 0x5f, 0xcd, 0xf4, 0x0,
    0x0, 0x5, 0xfc,

    /* U+0069 "i" */
    0xd, 0xf4, 0xf, 0xf6, 0x4, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf4, 0xd, 0xf4, 0xd, 0xf4,
    0xd, 0xf4, 0xd, 0xf4, 0xd, 0xf4, 0xd, 0xf4,
    0xd, 0xf4, 0xd, 0xf4, 0xd, 0xf4, 0xd, 0xf4,
    0xd, 0xf4, 0xd, 0xf4,

    /* U+006A "j" */
    0x0, 0x0, 0xdf, 0x40, 0x0, 0xf, 0xf6, 0x0,
    0x0, 0x46, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0, 0xdf,
    0x40, 0x0, 0xd, 0xf4, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0xd, 0xf4, 0x0, 0x0, 0xdf, 0x40, 0x0,
    0xd, 0xf4, 0x0, 0x0, 0xdf, 0x40, 0x0, 0xd,
    0xf4, 0x0, 0x0, 0xdf, 0x40, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0xd, 0xf3, 0x0,
    0x0, 0xdf, 0x30, 0x0, 0xe, 0xf2, 0x0, 0x4,
    0xff, 0x8, 0xbb, 0xff, 0x80, 0xaf, 0xfd, 0x70,
    0x0,

    /* U+006B "k" */
    0xdf, 0x40, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0xd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x1, 0xdf,
    0x90, 0xdf, 0x40, 0x0, 0xbf, 0xb0, 0xd, 0xf4,
    0x0, 0x9f, 0xc0, 0x0, 0xdf, 0x40, 0x6f, 0xd1,
    0x0, 0xd, 0xf4, 0x4f, 0xe2, 0x0, 0x0, 0xdf,
    0xae, 0xf3, 0x0, 0x0, 0xd, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0xdf, 0x4a, 0xfb, 0x0, 0x0, 0xd,
    0xf4, 0xd, 0xf9, 0x0, 0x0, 0xdf, 0x40, 0x2f,
    0xf6, 0x0, 0xd, 0xf4, 0x0, 0x4f, 0xf4, 0x0,
    0xdf, 0x40, 0x0, 0x8f, 0xe2, 0xd, 0xf4, 0x0,
    0x0, 0xbf, 0xd1,

    /* U+006C "l" */
    0xdf, 0x40, 0xd, 0xf4, 0x0, 0xdf, 0x40, 0xd,
    0xf4, 0x0, 0xdf, 0x40, 0xd, 0xf4, 0x0, 0xdf,
    0x40, 0xd, 0xf4, 0x0, 0xdf, 0x40, 0xd, 0xf4,
    0x0, 0xdf, 0x40, 0xd, 0xf4, 0x0, 0xdf, 0x40,
    0xd, 0xf4, 0x0, 0xdf, 0x40, 0xc, 0xf5, 0x0,
    0x9f, 0xeb, 0x51, 0xae, 0xf6,

    /* U+006D "m" */
    0xdf, 0x46, 0xcf, 0xfc, 0x50, 0x8, 0xdf, 0xfb,
    0x40, 0xd, 0xfb, 0xff, 0xdf, 0xff, 0x6c, 0xfe,
    0xdf, 0xff, 0x40, 0xdf, 0xf5, 0x0, 0xa, 0xff,
    0xe4, 0x0, 0xb, 0xfc, 0xd, 0xf8, 0x0, 0x0,
    0x1f, 0xf6, 0x0, 0x0, 0x3f, 0xf0, 0xdf, 0x40,
    0x0, 0x0, 0xff, 0x20, 0x0, 0x1, 0xff, 0xd,
    0xf4, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0xf,
    0xf0, 0xdf, 0x40, 0x0, 0x0, 0xff, 0x20, 0x0,
    0x0, 0xff, 0xd, 0xf4, 0x0, 0x0, 0xf, 0xf2,
    0x0, 0x0, 0xf, 0xf0, 0xdf, 0x40, 0x0, 0x0,
    0xff, 0x20, 0x0, 0x0, 0xff, 0xd, 0xf4, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0x0, 0xf, 0xf0, 0xdf,
    0x40, 0x0, 0x0, 0xff, 0x20, 0x0, 0x0, 0xff,
    0xd, 0xf4, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0,
    0xf, 0xf0, 0xdf, 0x40, 0x0, 0x0, 0xff, 0x20,
    0x0, 0x0, 0xff, 0x0,

    /* U+006E "n" */
    0xdf, 0x44, 0xbe, 0xfe, 0xa2, 0xd, 0xf9, 0xff,
    0xed, 0xff, 0xe1, 0xdf, 0xf9, 0x10, 0x2, 0xef,
    0x8d, 0xfa, 0x0, 0x0, 0x8, 0xfa, 0xdf, 0x40,
    0x0, 0x0, 0x5f, 0xbd, 0xf4, 0x0, 0x0, 0x5,
    0xfc, 0xdf, 0x40, 0x0, 0x0, 0x5f, 0xcd, 0xf4,
    0x0, 0x0, 0x5, 0xfc, 0xdf, 0x40, 0x0, 0x0,
    0x5f, 0xcd, 0xf4, 0x0, 0x0, 0x5, 0xfc, 0xdf,
    0x40, 0x0, 0x0, 0x5f, 0xcd, 0xf4, 0x0, 0x0,
    0x5, 0xfc, 0xdf, 0x40, 0x0, 0x0, 0x5f, 0xc0,

    /* U+006F "o" */
    0x0, 0x18, 0xdf, 0xfe, 0xc7, 0x0, 0x0, 0x2e,
    0xff, 0xcb, 0xcf, 0xfd, 0x0, 0xc, 0xfa, 0x0,
    0x0, 0x1d, 0xf9, 0x3, 0xff, 0x10, 0x0, 0x0,
    0x3f, 0xf0, 0x6f, 0xc0, 0x0, 0x0, 0x0, 0xff,
    0x38, 0xfa, 0x0, 0x0, 0x0, 0xd, 0xf5, 0x9f,
    0xa0, 0x0, 0x0, 0x0, 0xcf, 0x68, 0xfa, 0x0,
    0x0, 0x0, 0xd, 0xf5, 0x6f, 0xc0, 0x0, 0x0,
    0x0, 0xff, 0x33, 0xff, 0x10, 0x0, 0x0, 0x3f,
    0xf0, 0xc, 0xfb, 0x10, 0x0, 0x1c, 0xf9, 0x0,
    0x2e, 0xff, 0xcb, 0xcf, 0xfd, 0x10, 0x0, 0x18,
    0xdf, 0xfe, 0xc7, 0x0, 0x0,

    /* U+0070 "p" */
    0xdf, 0x43, 0xbe, 0xfe, 0xb4, 0x0, 0xdf, 0x7f,
    0xfc, 0xbe, 0xff, 0x70, 0xdf, 0xe8, 0x0, 0x0,
    0x8f, 0xf2, 0xdf, 0xb0, 0x0, 0x0, 0xd, 0xf7,
    0xdf, 0x50, 0x0, 0x0, 0x8, 0xf9, 0xdf, 0x40,
    0x0, 0x0, 0x6, 0xfb, 0xdf, 0x20, 0x0, 0x0,
    0x6, 0xfc, 0xdf, 0x40, 0x0, 0x0, 0x6, 0xfb,
    0xdf, 0x60, 0x0, 0x0, 0x8, 0xf9, 0xdf, 0xb0,
    0x0, 0x0, 0xd, 0xf6, 0xdf, 0xf9, 0x0, 0x0,
    0x8f, 0xf1, 0xdf, 0x7f, 0xfc, 0xce, 0xff, 0x50,
    0xdf, 0x43, 0xbe, 0xfe, 0xa3, 0x0, 0xdf, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x19, 0xdf, 0xec, 0x50, 0xff, 0x10, 0x2e,
    0xff, 0xcb, 0xef, 0x7f, 0xf1, 0xc, 0xfb, 0x10,
    0x0, 0x5e, 0xff, 0x12, 0xff, 0x10, 0x0, 0x0,
    0x7f, 0xf1, 0x6f, 0xd0, 0x0, 0x0, 0x2, 0xff,
    0x17, 0xfb, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x8f,
    0xa0, 0x0, 0x0, 0x0, 0xef, 0x17, 0xfb, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x6f, 0xc0, 0x0, 0x0,
    0x1, 0xff, 0x13, 0xff, 0x10, 0x0, 0x0, 0x7f,
    0xf1, 0xd, 0xfb, 0x10, 0x0, 0x5e, 0xff, 0x10,
    0x3f, 0xff, 0xcb, 0xef, 0x6f, 0xf1, 0x0, 0x2a,
    0xef, 0xec, 0x50, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf1,

    /* U+0072 "r" */
    0xdf, 0x43, 0xbe, 0x9d, 0xf8, 0xfe, 0xa5, 0xdf,
    0xf8, 0x0, 0xd, 0xfa, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0xd, 0xf4, 0x0, 0x0, 0xdf, 0x40, 0x0,
    0xd, 0xf4, 0x0, 0x0, 0xdf, 0x40, 0x0, 0xd,
    0xf4, 0x0, 0x0, 0xdf, 0x40, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x7c, 0xff, 0xed, 0x50, 0xb, 0xfe, 0xba,
    0xce, 0x70, 0x3f, 0xd0, 0x0, 0x0, 0x0, 0x5f,
    0x90, 0x0, 0x0, 0x0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0xc, 0xfe, 0xa6, 0x10, 0x0, 0x0, 0x8d,
    0xff, 0xfa, 0x10, 0x0, 0x0, 0x16, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0xe, 0xf1, 0x0, 0x0, 0x0, 0x3f, 0xe0, 0x9d,
    0xba, 0xac, 0xff, 0x70, 0x6d, 0xff, 0xfe, 0xb4,
    0x0,

    /* U+0074 "t" */
    0x0, 0xbf, 0x50, 0x0, 0x0, 0xbf, 0x50, 0x0,
    0x0, 0xbf, 0x50, 0x0, 0xaf, 0xff, 0xff, 0xf9,
    0x7b, 0xef, 0xcb, 0xb6, 0x0, 0xbf, 0x50, 0x0,
    0x0, 0xbf, 0x50, 0x0, 0x0, 0xbf, 0x50, 0x0,
    0x0, 0xbf, 0x50, 0x0, 0x0, 0xbf, 0x50, 0x0,
    0x0, 0xbf, 0x50, 0x0, 0x0, 0xbf, 0x50, 0x0,
    0x0, 0xbf, 0x50, 0x0, 0x0, 0x9f, 0x90, 0x0,
    0x0, 0x4f, 0xfc, 0xb7, 0x0, 0x7, 0xdf, 0xf9,

    /* U+0075 "u" */
    0xef, 0x20, 0x0, 0x0, 0x8f, 0x8e, 0xf2, 0x0,
    0x0, 0x8, 0xf8, 0xef, 0x20, 0x0, 0x0, 0x8f,
    0x8e, 0xf2, 0x0, 0x0, 0x8, 0xf8, 0xef, 0x20,
    0x0, 0x0, 0x8f, 0x8e, 0xf2, 0x0, 0x0, 0x8,
    0xf8, 0xef, 0x20, 0x0, 0x0, 0x8f, 0x8e, 0xf2,
    0x0, 0x0, 0x8, 0xf8, 0xef, 0x20, 0x0, 0x0,
    0x8f, 0x8d, 0xf4, 0x0, 0x0, 0xc, 0xf8, 0xaf,
    0xa0, 0x0, 0x8, 0xff, 0x83, 0xff, 0xc9, 0x9d,
    0xfa, 0xf8, 0x4, 0xbe, 0xfe, 0xb3, 0x4f, 0x80,

    /* U+0076 "v" */
    0x9f, 0x80, 0x0, 0x0, 0x0, 0xff, 0x33, 0xfd,
    0x0, 0x0, 0x0, 0x5f, 0xd0, 0xd, 0xf3, 0x0,
    0x0, 0xa, 0xf7, 0x0, 0x7f, 0x90, 0x0, 0x0,
    0xff, 0x10, 0x1, 0xfe, 0x0, 0x0, 0x5f, 0xb0,
    0x0, 0xb, 0xf4, 0x0, 0xb, 0xf5, 0x0, 0x0,
    0x5f, 0xa0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0xef,
    0x0, 0x6f, 0x90, 0x0, 0x0, 0x9, 0xf6, 0xc,
    0xf3, 0x0, 0x0, 0x0, 0x3f, 0xc1, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x9f, 0x70, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xfb, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0x5f, 0xb0, 0x0, 0x0, 0x7f, 0xf4, 0x0, 0x0,
    0xc, 0xf3, 0x1f, 0xf0, 0x0, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0xf, 0xf0, 0xc, 0xf3, 0x0, 0x1,
    0xfd, 0xfe, 0x0, 0x0, 0x4f, 0xa0, 0x8, 0xf7,
    0x0, 0x5, 0xf7, 0xaf, 0x30, 0x0, 0x8f, 0x60,
    0x4, 0xfb, 0x0, 0xa, 0xf2, 0x5f, 0x80, 0x0,
    0xcf, 0x10, 0x0, 0xff, 0x0, 0xe, 0xe0, 0x1f,
    0xc0, 0x1, 0xfd, 0x0, 0x0, 0xbf, 0x30, 0x3f,
    0x90, 0xc, 0xf1, 0x4, 0xf9, 0x0, 0x0, 0x7f,
    0x70, 0x8f, 0x40, 0x7, 0xf6, 0x8, 0xf4, 0x0,
    0x0, 0x2f, 0xb0, 0xdf, 0x0, 0x2, 0xfb, 0xc,
    0xf0, 0x0, 0x0, 0xe, 0xf2, 0xfa, 0x0, 0x0,
    0xdf, 0x1f, 0xb0, 0x0, 0x0, 0xa, 0xfa, 0xf5,
    0x0, 0x0, 0x8f, 0xaf, 0x70, 0x0, 0x0, 0x5,
    0xff, 0xf1, 0x0, 0x0, 0x4f, 0xff, 0x20, 0x0,
    0x0, 0x1, 0xff, 0xb0, 0x0, 0x0, 0xe, 0xfe,
    0x0, 0x0,

    /* U+0078 "x" */
    0x2f, 0xf3, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x6f,
    0xd0, 0x0, 0x6, 0xfc, 0x0, 0x0, 0xcf, 0x70,
    0x1, 0xff, 0x20, 0x0, 0x2, 0xff, 0x20, 0xaf,
    0x70, 0x0, 0x0, 0x7, 0xfc, 0x4f, 0xd0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xfd, 0xf6, 0x0, 0x0, 0x0, 0xa, 0xf9, 0x2f,
    0xf1, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x8f, 0xb0,
    0x0, 0x1, 0xef, 0x40, 0x0, 0xdf, 0x60, 0x0,
    0xaf, 0x90, 0x0, 0x4, 0xff, 0x10, 0x5f, 0xe1,
    0x0, 0x0, 0x9, 0xfb, 0x0,

    /* U+0079 "y" */
    0x9f, 0xa0, 0x0, 0x0, 0x0, 0xdf, 0x32, 0xff,
    0x0, 0x0, 0x0, 0x3f, 0xd0, 0xc, 0xf5, 0x0,
    0x0, 0x8, 0xf7, 0x0, 0x6f, 0xb0, 0x0, 0x0,
    0xef, 0x20, 0x0, 0xff, 0x10, 0x0, 0x3f, 0xc0,
    0x0, 0x9, 0xf7, 0x0, 0x9, 0xf6, 0x0, 0x0,
    0x3f, 0xd0, 0x0, 0xef, 0x0, 0x0, 0x0, 0xcf,
    0x30, 0x4f, 0xa0, 0x0, 0x0, 0x6, 0xf8, 0x9,
    0xf4, 0x0, 0x0, 0x0, 0xf, 0xe0, 0xee, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x8f, 0x80, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xf7, 0x0, 0x0, 0x0,
    0x4, 0xdf, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xc6, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x6f, 0xff, 0xff, 0xff, 0xfe, 0x4, 0xcc, 0xcc,
    0xcc, 0xef, 0xc0, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x1e, 0xf6, 0x0, 0x0, 0x0,
    0xb, 0xf9, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x20, 0x0, 0x0, 0x1,
    0xef, 0x50, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0,
    0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x0, 0x4f,
    0xe1, 0x0, 0x0, 0x0, 0xe, 0xfe, 0xdd, 0xdd,
    0xdd, 0xd3, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,

    /* U+007B "{" */
    0x0, 0x9, 0xd5, 0x0, 0xaf, 0xf5, 0x1, 0xff,
    0x10, 0x3, 0xfd, 0x0, 0x4, 0xfc, 0x0, 0x4,
    0xfc, 0x0, 0x4, 0xfc, 0x0, 0x4, 0xfc, 0x0,
    0x4, 0xfc, 0x0, 0x4, 0xfc, 0x0, 0x9, 0xfa,
    0x0, 0xdf, 0xe2, 0x0, 0xcf, 0xf4, 0x0, 0x7,
    0xfb, 0x0, 0x4, 0xfc, 0x0, 0x4, 0xfc, 0x0,
    0x4, 0xfc, 0x0, 0x4, 0xfc, 0x0, 0x4, 0xfc,
    0x0, 0x4, 0xfc, 0x0, 0x2, 0xff, 0x10, 0x0,
    0xcf, 0xf5, 0x0, 0x19, 0xe5,

    /* U+007C "|" */
    0xcf, 0x3c, 0xf3, 0xcf, 0x3c, 0xf3, 0xcf, 0x3c,
    0xf3, 0xcf, 0x3c, 0xf3, 0xcf, 0x3c, 0xf3, 0xcf,
    0x3c, 0xf3, 0xcf, 0x3c, 0xf3, 0xcf, 0x3c, 0xf3,
    0xcf, 0x3c, 0xf3, 0xcf, 0x3c, 0xf3, 0xcf, 0x3c,
    0xf3, 0xcf, 0x30,

    /* U+007D "}" */
    0x2e, 0xa1, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0xdf,
    0x40, 0x0, 0xaf, 0x60, 0x0, 0xaf, 0x70, 0x0,
    0xaf, 0x70, 0x0, 0xaf, 0x70, 0x0, 0xaf, 0x70,
    0x0, 0xaf, 0x70, 0x0, 0x9f, 0x70, 0x0, 0x7f,
    0xb1, 0x0, 0x1d, 0xff, 0x0, 0x2e, 0xff, 0x0,
    0x8f, 0xa0, 0x0, 0x9f, 0x70, 0x0, 0xaf, 0x70,
    0x0, 0xaf, 0x70, 0x0, 0xaf, 0x70, 0x0, 0xaf,
    0x70, 0x0, 0xaf, 0x70, 0x0, 0xdf, 0x50, 0x2f,
    0xfe, 0x0, 0x2e, 0xb2, 0x0,

    /* U+007E "~" */
    0x0, 0x9e, 0xfb, 0x30, 0x0, 0x5d, 0x60, 0x9f,
    0xff, 0xff, 0x80, 0xb, 0xf5, 0x1f, 0xe1, 0x6,
    0xff, 0xfe, 0xfd, 0x3, 0xc8, 0x0, 0x1, 0x9e,
    0xfb, 0x20,

    /* U+00B0 "°" */
    0x0, 0x4a, 0xc9, 0x10, 0x0, 0x7f, 0xff, 0xfe,
    0x30, 0x2f, 0xe3, 0x6, 0xfd, 0x7, 0xf6, 0x0,
    0xb, 0xf2, 0x7f, 0x50, 0x0, 0xaf, 0x33, 0xfc,
    0x10, 0x3f, 0xe0, 0x9, 0xff, 0xdf, 0xf4, 0x0,
    0x7, 0xef, 0xc3, 0x0,

    /* U+4EF0 "仰" */
    0x0, 0x0, 0x1, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x5c, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf0, 0x29, 0xef, 0xfa, 0xbb, 0xbb, 0xbb, 0xa0,
    0x0, 0x4, 0xfa, 0xa, 0xff, 0xa3, 0xf, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0xbf, 0x30, 0xaf, 0x20,
    0x0, 0xfb, 0x0, 0xc, 0xe0, 0x0, 0x2f, 0xd0,
    0xa, 0xf1, 0x0, 0xf, 0xb0, 0x0, 0xce, 0x0,
    0x9, 0xfb, 0x0, 0xaf, 0x10, 0x0, 0xfb, 0x0,
    0xc, 0xe0, 0x2, 0xff, 0xb0, 0xa, 0xf1, 0x0,
    0xf, 0xb0, 0x0, 0xce, 0x0, 0xaf, 0xfb, 0x0,
    0xaf, 0x10, 0x0, 0xfb, 0x0, 0xc, 0xe0, 0x3f,
    0xdf, 0xb0, 0xa, 0xf1, 0x0, 0xf, 0xb0, 0x0,
    0xce, 0xd, 0xf4, 0xfb, 0x0, 0xaf, 0x10, 0x0,
    0xfb, 0x0, 0xc, 0xe8, 0xf9, 0xf, 0xb0, 0xa,
    0xf1, 0x0, 0xf, 0xb0, 0x0, 0xce, 0x1a, 0x0,
    0xfb, 0x0, 0xaf, 0x10, 0x0, 0xfb, 0x0, 0xc,
    0xe0, 0x0, 0xf, 0xb0, 0xa, 0xf1, 0x0, 0xf,
    0xb0, 0x0, 0xce, 0x0, 0x0, 0xfb, 0x0, 0xaf,
    0x10, 0x13, 0xfb, 0x0, 0xc, 0xe0, 0x0, 0xf,
    0xb0, 0xa, 0xf2, 0x8f, 0x7f, 0xb0, 0x0, 0xde,
    0x0, 0x0, 0xfb, 0x0, 0xbf, 0xff, 0xe4, 0xfb,
    0x8, 0xcf, 0xd0, 0x0, 0xf, 0xb0, 0x1f, 0xfe,
    0x70, 0xf, 0xb0, 0xdf, 0xf5, 0x0, 0x0, 0xfb,
    0x0, 0xb7, 0x0, 0x0, 0xfb, 0x3, 0x30, 0x0,
    0x0, 0xf, 0xb0, 0x0, 0x0, 0x0, 0xf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xf, 0xb0,
    0x0, 0x0, 0x0, 0xf, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0x0,
    0x0, 0x0,

    /* U+4FA7 "侧" */
    0x0, 0x0, 0x4, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xe1, 0x0, 0x0,
    0xaf, 0xe, 0xff, 0xff, 0xff, 0x70, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0xfb, 0xf, 0xec, 0xcc, 0xcf,
    0x80, 0xd9, 0x9, 0xf1, 0x0, 0x6, 0xf5, 0xf,
    0x90, 0x0, 0xf, 0x80, 0xeb, 0x9, 0xf1, 0x0,
    0xc, 0xf1, 0xf, 0x90, 0xe8, 0xf, 0x80, 0xeb,
    0x9, 0xf1, 0x0, 0x3f, 0xf1, 0xf, 0x90, 0xf9,
    0xf, 0x80, 0xeb, 0x9, 0xf1, 0x0, 0xbf, 0xf1,
    0xf, 0x90, 0xf9, 0xf, 0x80, 0xeb, 0x9, 0xf1,
    0x2, 0xff, 0xf1, 0xf, 0x90, 0xf9, 0xf, 0x80,
    0xeb, 0x9, 0xf1, 0xc, 0xfa, 0xf1, 0xf, 0x90,
    0xf9, 0xf, 0x80, 0xeb, 0x9, 0xf1, 0x5f, 0x88,
    0xf1, 0xf, 0x90, 0xf9, 0xf, 0x80, 0xeb, 0x9,
    0xf1, 0x9, 0x18, 0xf1, 0xf, 0x90, 0xf9, 0xf,
    0x80, 0xeb, 0x9, 0xf1, 0x0, 0x8, 0xf1, 0xf,
    0x90, 0xf9, 0xf, 0x80, 0xeb, 0x9, 0xf1, 0x0,
    0x8, 0xf1, 0xf, 0x90, 0xf8, 0xf, 0x80, 0xeb,
    0x9, 0xf1, 0x0, 0x8, 0xf1, 0xf, 0x92, 0xf7,
    0xf, 0x80, 0xeb, 0x9, 0xf1, 0x0, 0x8, 0xf1,
    0xf, 0x94, 0xf5, 0xf, 0x80, 0xeb, 0x9, 0xf1,
    0x0, 0x8, 0xf1, 0x2, 0x1a, 0xf5, 0x21, 0x0,
    0xeb, 0x9, 0xf1, 0x0, 0x8, 0xf1, 0x0, 0x3f,
    0xbb, 0xe3, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x8,
    0xf1, 0x1, 0xdf, 0x21, 0xdf, 0x30, 0x0, 0x9,
    0xf1, 0x0, 0x8, 0xf1, 0x2d, 0xf5, 0x0, 0x1d,
    0xe2, 0x0, 0x1d, 0xf0, 0x0, 0x8, 0xf5, 0xff,
    0x40, 0x0, 0x2, 0xe2, 0x6f, 0xff, 0xb0, 0x0,
    0x8, 0xf1, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x29,
    0x85, 0x0,

    /* U+4FEF "俯" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b, 0x20,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf0, 0x0, 0x0, 0x0, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xfa, 0xa, 0xaa,
    0xaa, 0xaf, 0xfa, 0xaa, 0xaa, 0xa0, 0x0, 0x0,
    0x8f, 0x51, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0xe, 0xe0, 0x1f, 0x90, 0x2,
    0x82, 0x0, 0x2, 0x40, 0x0, 0x0, 0x6, 0xfa,
    0x1, 0xf9, 0x0, 0x8f, 0x20, 0x0, 0x8f, 0x10,
    0x0, 0x0, 0xdf, 0xa0, 0x1f, 0x90, 0xd, 0xc0,
    0x0, 0x8, 0xf1, 0x0, 0x0, 0x5f, 0xfa, 0x1,
    0xf9, 0x5, 0xf6, 0x0, 0x0, 0x9f, 0x10, 0x0,
    0xe, 0xff, 0xa0, 0x1f, 0x90, 0xdf, 0x3f, 0xff,
    0xff, 0xff, 0xf8, 0x9, 0xf7, 0xfa, 0x1, 0xf9,
    0x6f, 0xf2, 0x88, 0x88, 0xcf, 0x88, 0x42, 0xfd,
    0xf, 0xa0, 0x1f, 0xbf, 0xff, 0x11, 0x30, 0x8,
    0xf1, 0x0, 0x4, 0x30, 0xfa, 0x1, 0xff, 0xfb,
    0xf1, 0xcd, 0x0, 0x8f, 0x10, 0x0, 0x0, 0xf,
    0xa0, 0x1f, 0x84, 0x8f, 0x14, 0xf6, 0x8, 0xf1,
    0x0, 0x0, 0x0, 0xfa, 0x1, 0xf7, 0x8, 0xf1,
    0xc, 0xd0, 0x8f, 0x10, 0x0, 0x0, 0xf, 0xa0,
    0x2f, 0x60, 0x8f, 0x10, 0x5f, 0x48, 0xf1, 0x0,
    0x0, 0x0, 0xfa, 0x4, 0xf5, 0x8, 0xf1, 0x0,
    0xb2, 0x8f, 0x10, 0x0, 0x0, 0xf, 0xa0, 0x7f,
    0x20, 0x8f, 0x10, 0x0, 0x8, 0xf1, 0x0, 0x0,
    0x0, 0xfa, 0xb, 0xf0, 0x8, 0xf1, 0x0, 0x0,
    0x8f, 0x10, 0x0, 0x0, 0xf, 0xa1, 0xfb, 0x0,
    0x8f, 0x10, 0x0, 0xa, 0xf0, 0x0, 0x0, 0x0,
    0xfa, 0x9f, 0x50, 0x8, 0xf1, 0x4, 0xbd, 0xfe,
    0x0, 0x0, 0x0, 0xf, 0xa3, 0x90, 0x0, 0x8f,
    0x10, 0x3f, 0xdb, 0x30, 0x0, 0x0, 0x0, 0x21,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+503E "倾" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf6, 0x6c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x18, 0xf1, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xe, 0xd0, 0x8f, 0x10,
    0x18, 0x88, 0x8f, 0xc8, 0x88, 0x80, 0x0, 0x4,
    0xf7, 0x8, 0xf1, 0x0, 0x0, 0x2, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x20, 0x8f, 0x10, 0x0,
    0x0, 0x5f, 0x40, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x8, 0xf1, 0x0, 0x58, 0x8c, 0xf9, 0x88, 0x80,
    0x0, 0x7, 0xff, 0x10, 0x8f, 0xff, 0x6a, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xef, 0xf1, 0x8,
    0xfa, 0xa4, 0xae, 0x0, 0x0, 0x9, 0xf0, 0x0,
    0x5f, 0xef, 0x10, 0x8f, 0x10, 0xa, 0xe0, 0x19,
    0x30, 0x9f, 0x0, 0xd, 0xe8, 0xf1, 0x8, 0xf1,
    0x0, 0xae, 0x2, 0xf6, 0x9, 0xf0, 0x7, 0xf6,
    0x8f, 0x10, 0x8f, 0x10, 0xa, 0xe0, 0x2f, 0x60,
    0x9f, 0x0, 0x3c, 0x8, 0xf1, 0x8, 0xf1, 0x0,
    0xae, 0x2, 0xf6, 0x9, 0xf0, 0x0, 0x0, 0x8f,
    0x10, 0x8f, 0x10, 0xa, 0xe0, 0x2f, 0x50, 0x9f,
    0x0, 0x0, 0x8, 0xf1, 0x8, 0xf1, 0x0, 0xae,
    0x3, 0xf4, 0x9, 0xf0, 0x0, 0x0, 0x8f, 0x10,
    0x8f, 0x16, 0x8a, 0xe0, 0x5f, 0x20, 0x9f, 0x0,
    0x0, 0x8, 0xf1, 0x8, 0xfa, 0xfc, 0xae, 0xa,
    0xf2, 0x9, 0xf0, 0x0, 0x0, 0x8f, 0x10, 0xaf,
    0xfb, 0x10, 0x13, 0xf9, 0xdb, 0x11, 0x0, 0x0,
    0x8, 0xf1, 0x1f, 0xf7, 0x0, 0x2, 0xed, 0x15,
    0xfd, 0x10, 0x0, 0x0, 0x8f, 0x10, 0x84, 0x0,
    0x5, 0xee, 0x20, 0x4, 0xfe, 0x20, 0x0, 0x8,
    0xf1, 0x0, 0x0, 0x3c, 0xfc, 0x10, 0x0, 0x3,
    0xee, 0x30, 0x0, 0x8f, 0x10, 0x0, 0x1, 0xe7,
    0x0, 0x0, 0x0, 0x3, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+52A0 "加" */
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf3, 0x0, 0x0, 0x0, 0x45, 0x55,
    0x55, 0x55, 0x0, 0x0, 0x9f, 0x40, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0x20, 0xdf, 0x77, 0x77, 0xef, 0x5,
    0xbb, 0xef, 0xcb, 0xbe, 0xf1, 0xd, 0xe0, 0x0,
    0xd, 0xf0, 0x0, 0xa, 0xf2, 0x0, 0xbf, 0x10,
    0xde, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xbf, 0x10,
    0xb, 0xf1, 0xd, 0xe0, 0x0, 0xd, 0xf0, 0x0,
    0xc, 0xf0, 0x0, 0xcf, 0x0, 0xde, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0xde, 0x0, 0xc, 0xf0, 0xd,
    0xe0, 0x0, 0xd, 0xf0, 0x0, 0xf, 0xd0, 0x0,
    0xcf, 0x0, 0xde, 0x0, 0x0, 0xdf, 0x0, 0x1,
    0xfb, 0x0, 0xd, 0xf0, 0xd, 0xe0, 0x0, 0xd,
    0xf0, 0x0, 0x4f, 0x80, 0x0, 0xdf, 0x0, 0xde,
    0x0, 0x0, 0xdf, 0x0, 0x8, 0xf5, 0x0, 0xe,
    0xe0, 0xd, 0xe0, 0x0, 0xd, 0xf0, 0x0, 0xbf,
    0x10, 0x0, 0xee, 0x0, 0xde, 0x0, 0x0, 0xdf,
    0x0, 0x1f, 0xd0, 0x0, 0xe, 0xe0, 0xd, 0xe0,
    0x0, 0xd, 0xf0, 0x6, 0xf7, 0x0, 0x0, 0xfd,
    0x0, 0xde, 0x0, 0x0, 0xdf, 0x0, 0xef, 0x10,
    0x0, 0xf, 0xd0, 0xd, 0xfc, 0xcc, 0xcf, 0xf0,
    0x7f, 0x90, 0x1, 0x37, 0xfc, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0x2f, 0xf2, 0x0, 0xef, 0xff, 0x80,
    0xd, 0xe0, 0x0, 0xd, 0xf6, 0xf7, 0x0, 0x8,
    0xca, 0x60, 0x0, 0xde, 0x0, 0x0, 0xdf, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x62, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0xa,
    0xfa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xa7, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x95, 0x0,
    0x0, 0x8, 0x70, 0x0, 0x0, 0x0, 0xa, 0xf1,
    0x0, 0x1f, 0xa0, 0x0, 0x0, 0xec, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0xa, 0xf4, 0x99,
    0x9f, 0xd9, 0x99, 0x99, 0xfe, 0x99, 0x96, 0x0,
    0x0, 0xaf, 0x10, 0x1, 0xfa, 0x0, 0x0, 0xe,
    0xc0, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x1f,
    0xb5, 0x55, 0x55, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x2, 0x22,
    0x22, 0x22, 0x22, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x7, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x20,
    0x0, 0x0, 0xd, 0xd0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x5f, 0xd1, 0x0, 0x0, 0x8, 0xfd, 0x10, 0x0,
    0x0, 0x3f, 0x80, 0x0, 0x6f, 0xd2, 0x0, 0x9,
    0xfd, 0x10, 0x0, 0x0, 0x7, 0xf5, 0x0, 0x0,
    0x5f, 0xf6, 0x3d, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0, 0x15, 0x9e,
    0xfe, 0xdf, 0xfb, 0x74, 0x10, 0x0, 0xc, 0xf3,
    0x4b, 0xef, 0xff, 0xb5, 0x0, 0x38, 0xdf, 0xff,
    0xfc, 0x22, 0xeb, 0x2, 0xfd, 0x94, 0x0, 0x0,
    0x0, 0x0, 0x15, 0x8c, 0xd0, 0x1, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+89D2 "角" */
    0x0, 0x0, 0x0, 0x2a, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x99, 0x99, 0x99, 0x99, 0x20, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x3, 0xff, 0x40, 0x0, 0x0, 0x4, 0xfd,
    0x0, 0x0, 0x0, 0x2e, 0xf6, 0x0, 0x0, 0x0,
    0x1e, 0xf4, 0x0, 0x0, 0x4, 0xef, 0x80, 0x0,
    0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x3e, 0x6f, 0xea, 0xaa, 0xaa, 0xef, 0xaa, 0xaa,
    0xae, 0xf1, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0xcf,
    0x0, 0x0, 0xa, 0xf1, 0x0, 0x1f, 0xa0, 0x0,
    0x0, 0xcf, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x1f,
    0xec, 0xcc, 0xcc, 0xff, 0xcc, 0xcc, 0xce, 0xf1,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x1f, 0xa0, 0x0, 0x0, 0xcf,
    0x0, 0x0, 0xa, 0xf1, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0xcf, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x2f,
    0xd9, 0x99, 0x99, 0xef, 0x99, 0x99, 0x9d, 0xf1,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x8f, 0x30, 0x0, 0x0, 0xcf,
    0x0, 0x0, 0xa, 0xf1, 0x0, 0xee, 0x0, 0x0,
    0x0, 0xcf, 0x0, 0x0, 0xa, 0xf1, 0x6, 0xf8,
    0x0, 0x0, 0x0, 0xcf, 0x0, 0x0, 0xb, 0xf1,
    0x1e, 0xf1, 0x0, 0x0, 0x0, 0xcf, 0x0, 0x39,
    0xcf, 0xf0, 0xbf, 0x60, 0x0, 0x0, 0x0, 0xcf,
    0x0, 0x2f, 0xff, 0x70, 0x18, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x20, 0x0,

    /* U+901F "速" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x40, 0x0, 0x0, 0x0, 0xc, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xfd, 0x0, 0xcd, 0xdd,
    0xdd, 0xff, 0xdd, 0xdd, 0xdd, 0x30, 0x0, 0x8,
    0xf6, 0xb, 0xcc, 0xcc, 0xcf, 0xfc, 0xcc, 0xcc,
    0xc3, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0,
    0xcf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x88,
    0x0, 0x34, 0x44, 0x4d, 0xf4, 0x44, 0x44, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x22, 0x2d, 0xf2, 0x22, 0xaf, 0x10, 0x0,
    0x4b, 0xbb, 0xa0, 0xc, 0xf0, 0x0, 0xcf, 0x0,
    0x9, 0xf1, 0x0, 0x5, 0xff, 0xfe, 0x0, 0xcf,
    0x11, 0x1c, 0xf1, 0x11, 0xaf, 0x10, 0x0, 0x0,
    0xc, 0xe0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0xce, 0x0, 0x45, 0x55,
    0xcf, 0xf6, 0x55, 0x55, 0x0, 0x0, 0x0, 0xc,
    0xe0, 0x0, 0x0, 0x7f, 0xff, 0x4, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xce, 0x0, 0x0, 0x8f, 0xbc,
    0xf0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0xc, 0xe0,
    0x1, 0xbf, 0xb0, 0xcf, 0x0, 0x9f, 0xc1, 0x0,
    0x0, 0x0, 0xce, 0x6, 0xef, 0x90, 0xc, 0xf0,
    0x0, 0x7f, 0xe3, 0x0, 0x0, 0x1e, 0xf1, 0xde,
    0x50, 0x0, 0xcf, 0x0, 0x0, 0x5f, 0x30, 0x0,
    0x1d, 0xff, 0xc4, 0x10, 0x0, 0xc, 0xf0, 0x0,
    0x0, 0x20, 0x0, 0x1d, 0xf8, 0x7f, 0xf8, 0x20,
    0x0, 0x68, 0x0, 0x0, 0x0, 0x0, 0xc, 0xfa,
    0x0, 0x4e, 0xff, 0xfd, 0xcb, 0xbb, 0xbb, 0xbb,
    0xcd, 0x30, 0x8b, 0x0, 0x0, 0x5, 0x9c, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+FF1A "：" */
    0x0, 0x10, 0x2f, 0xf8, 0x4f, 0xfb, 0x1d, 0xe6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xd6,
    0x4f, 0xfb, 0x2f, 0xf9, 0x1, 0x10
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 99, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 139, .box_w = 4, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 32, .adv_w = 156, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 57, .adv_w = 230, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 169, .adv_w = 221, .box_w = 11, .box_h = 23, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 296, .adv_w = 315, .box_w = 18, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 440, .adv_w = 246, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 568, .adv_w = 97, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 582, .adv_w = 138, .box_w = 7, .box_h = 23, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 663, .adv_w = 138, .box_w = 7, .box_h = 23, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 744, .adv_w = 165, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 789, .adv_w = 212, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 867, .adv_w = 116, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 885, .adv_w = 169, .box_w = 8, .box_h = 2, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 893, .adv_w = 116, .box_w = 4, .box_h = 3, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 899, .adv_w = 192, .box_w = 10, .box_h = 18, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 989, .adv_w = 221, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1085, .adv_w = 221, .box_w = 7, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1141, .adv_w = 221, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1237, .adv_w = 221, .box_w = 11, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1325, .adv_w = 221, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1429, .adv_w = 221, .box_w = 11, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1517, .adv_w = 221, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1613, .adv_w = 221, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1709, .adv_w = 221, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1805, .adv_w = 221, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1901, .adv_w = 145, .box_w = 4, .box_h = 13, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 1927, .adv_w = 145, .box_w = 6, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1975, .adv_w = 212, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 2060, .adv_w = 212, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 2112, .adv_w = 212, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 2197, .adv_w = 166, .box_w = 9, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2269, .adv_w = 330, .box_w = 19, .box_h = 19, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2450, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2578, .adv_w = 238, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2674, .adv_w = 230, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2778, .adv_w = 268, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2890, .adv_w = 214, .box_w = 11, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2978, .adv_w = 202, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3058, .adv_w = 263, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3170, .adv_w = 272, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3274, .adv_w = 105, .box_w = 3, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3298, .adv_w = 104, .box_w = 7, .box_h = 21, .ofs_x = -2, .ofs_y = -5},
    {.bitmap_index = 3372, .adv_w = 236, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3476, .adv_w = 195, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3556, .adv_w = 319, .box_w = 16, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3684, .adv_w = 283, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3796, .adv_w = 285, .box_w = 16, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3924, .adv_w = 229, .box_w = 11, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4012, .adv_w = 285, .box_w = 16, .box_h = 19, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4164, .adv_w = 236, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4268, .adv_w = 217, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4364, .adv_w = 203, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4468, .adv_w = 269, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4572, .adv_w = 237, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4692, .adv_w = 345, .box_w = 21, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4860, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4980, .adv_w = 228, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5100, .adv_w = 240, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5204, .adv_w = 113, .box_w = 6, .box_h = 23, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 5273, .adv_w = 196, .box_w = 12, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5399, .adv_w = 113, .box_w = 6, .box_h = 23, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5468, .adv_w = 212, .box_w = 11, .box_h = 9, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 5518, .adv_w = 192, .box_w = 12, .box_h = 2, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5530, .adv_w = 133, .box_w = 6, .box_h = 4, .ofs_x = 1, .ofs_y = 15},
    {.bitmap_index = 5542, .adv_w = 235, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5620, .adv_w = 244, .box_w = 12, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5728, .adv_w = 191, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5793, .adv_w = 244, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5910, .adv_w = 222, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5988, .adv_w = 134, .box_w = 9, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6069, .adv_w = 244, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6186, .adv_w = 237, .box_w = 11, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6285, .adv_w = 103, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6321, .adv_w = 103, .box_w = 7, .box_h = 23, .ofs_x = -2, .ofs_y = -5},
    {.bitmap_index = 6402, .adv_w = 199, .box_w = 11, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6501, .adv_w = 110, .box_w = 5, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6546, .adv_w = 354, .box_w = 19, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6670, .adv_w = 237, .box_w = 11, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6742, .adv_w = 237, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6827, .adv_w = 244, .box_w = 12, .box_h = 18, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 6935, .adv_w = 244, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7052, .adv_w = 146, .box_w = 7, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7098, .adv_w = 184, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7163, .adv_w = 129, .box_w = 8, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7227, .adv_w = 235, .box_w = 11, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7299, .adv_w = 202, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7384, .adv_w = 318, .box_w = 20, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7514, .adv_w = 198, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7599, .adv_w = 202, .box_w = 13, .box_h = 18, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7716, .adv_w = 194, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7788, .adv_w = 115, .box_w = 6, .box_h = 23, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7857, .adv_w = 70, .box_w = 3, .box_h = 23, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7892, .adv_w = 115, .box_w = 6, .box_h = 23, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7961, .adv_w = 212, .box_w = 13, .box_h = 4, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 7987, .adv_w = 139, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 8023, .adv_w = 378, .box_w = 21, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8265, .adv_w = 378, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8507, .adv_w = 378, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8772, .adv_w = 378, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9037, .adv_w = 378, .box_w = 21, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9279, .adv_w = 378, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9544, .adv_w = 378, .box_w = 20, .box_h = 23, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 9774, .adv_w = 378, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10039, .adv_w = 378, .box_w = 4, .box_h = 15, .ofs_x = 4, .ofs_y = -1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x4e40, 0x4ef7, 0x4f3f, 0x4f8e, 0x51f0, 0x5df6, 0x8922,
    0x8f6f, 0xfe6a
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 65131, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 10, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    3, 3,
    3, 8,
    3, 13,
    3, 15,
    8, 3,
    8, 8,
    8, 13,
    8, 15,
    9, 75,
    13, 3,
    13, 8,
    13, 18,
    13, 24,
    13, 26,
    15, 3,
    15, 8,
    15, 18,
    15, 24,
    15, 26,
    16, 16,
    18, 13,
    18, 15,
    18, 27,
    18, 28,
    24, 13,
    24, 15,
    24, 27,
    24, 28,
    27, 18,
    27, 24,
    28, 18,
    28, 24,
    34, 3,
    34, 8,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 71,
    34, 77,
    34, 85,
    34, 87,
    34, 90,
    35, 36,
    35, 40,
    35, 48,
    35, 50,
    35, 53,
    35, 55,
    35, 57,
    35, 58,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 56,
    37, 57,
    37, 58,
    37, 59,
    37, 66,
    38, 75,
    39, 13,
    39, 15,
    39, 34,
    39, 66,
    39, 73,
    39, 76,
    40, 55,
    40, 58,
    43, 43,
    44, 34,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 53,
    44, 54,
    44, 55,
    44, 56,
    44, 58,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 77,
    44, 80,
    44, 82,
    44, 84,
    44, 85,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 87,
    45, 88,
    45, 90,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 56,
    48, 57,
    48, 58,
    48, 59,
    48, 66,
    49, 13,
    49, 15,
    49, 34,
    49, 53,
    49, 55,
    49, 56,
    49, 57,
    49, 58,
    49, 59,
    49, 66,
    50, 13,
    50, 15,
    50, 34,
    50, 43,
    50, 53,
    50, 55,
    50, 56,
    50, 57,
    50, 58,
    50, 59,
    50, 66,
    51, 36,
    51, 40,
    51, 48,
    51, 50,
    51, 53,
    51, 54,
    51, 55,
    51, 56,
    51, 57,
    51, 58,
    51, 68,
    51, 69,
    51, 70,
    51, 72,
    51, 80,
    51, 82,
    51, 85,
    51, 86,
    51, 87,
    51, 89,
    51, 90,
    52, 13,
    52, 15,
    52, 52,
    52, 53,
    52, 55,
    52, 56,
    52, 58,
    52, 85,
    52, 87,
    52, 88,
    52, 89,
    52, 90,
    53, 3,
    53, 8,
    53, 13,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 48,
    53, 50,
    53, 52,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 74,
    53, 75,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 85,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    54, 57,
    54, 89,
    55, 13,
    55, 15,
    55, 27,
    55, 28,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 52,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 73,
    55, 76,
    55, 80,
    55, 82,
    55, 84,
    55, 87,
    55, 90,
    56, 13,
    56, 15,
    56, 27,
    56, 28,
    56, 34,
    56, 36,
    56, 40,
    56, 48,
    56, 50,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 84,
    57, 34,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 53,
    57, 54,
    57, 55,
    57, 56,
    57, 58,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 77,
    57, 80,
    57, 82,
    57, 84,
    57, 85,
    57, 86,
    57, 87,
    57, 88,
    57, 90,
    58, 3,
    58, 8,
    58, 13,
    58, 15,
    58, 27,
    58, 28,
    58, 34,
    58, 36,
    58, 40,
    58, 48,
    58, 50,
    58, 52,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 73,
    58, 74,
    58, 76,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    60, 75,
    66, 53,
    66, 55,
    66, 58,
    66, 87,
    66, 88,
    66, 90,
    67, 13,
    67, 15,
    67, 53,
    67, 55,
    67, 56,
    67, 57,
    67, 58,
    67, 87,
    67, 89,
    67, 90,
    70, 13,
    70, 15,
    70, 53,
    70, 58,
    70, 75,
    71, 3,
    71, 8,
    71, 10,
    71, 11,
    71, 13,
    71, 15,
    71, 32,
    71, 53,
    71, 55,
    71, 56,
    71, 57,
    71, 58,
    71, 62,
    71, 66,
    71, 68,
    71, 69,
    71, 70,
    71, 71,
    71, 72,
    71, 73,
    71, 74,
    71, 75,
    71, 76,
    71, 77,
    71, 80,
    71, 82,
    71, 84,
    71, 85,
    71, 94,
    73, 53,
    73, 58,
    73, 87,
    73, 90,
    74, 3,
    74, 8,
    74, 10,
    74, 32,
    74, 53,
    74, 62,
    74, 94,
    75, 53,
    76, 53,
    76, 54,
    76, 56,
    76, 66,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 77,
    76, 80,
    76, 82,
    76, 86,
    77, 71,
    77, 87,
    77, 88,
    77, 90,
    78, 53,
    78, 58,
    78, 87,
    78, 90,
    79, 53,
    79, 58,
    79, 87,
    79, 90,
    80, 13,
    80, 15,
    80, 53,
    80, 55,
    80, 56,
    80, 57,
    80, 58,
    80, 87,
    80, 89,
    80, 90,
    81, 13,
    81, 15,
    81, 53,
    81, 55,
    81, 56,
    81, 57,
    81, 58,
    81, 87,
    81, 89,
    81, 90,
    82, 53,
    82, 58,
    82, 75,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 80,
    83, 82,
    83, 85,
    84, 53,
    84, 55,
    84, 56,
    84, 57,
    84, 58,
    84, 87,
    84, 89,
    84, 90,
    85, 71,
    86, 53,
    86, 58,
    87, 13,
    87, 15,
    87, 53,
    87, 57,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    88, 53,
    88, 57,
    88, 66,
    89, 53,
    89, 54,
    89, 56,
    89, 66,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 77,
    89, 80,
    89, 82,
    89, 86,
    90, 13,
    90, 15,
    90, 53,
    90, 57,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 80,
    90, 82,
    91, 53,
    92, 73,
    92, 75,
    92, 76
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -22, -22, -69, -69, -22, -22, -69, -69,
    23, -61, -61, -54, -16, -15, -61, -61,
    -54, -16, -15, -46, -38, -38, -38, -38,
    -61, -61, -38, -38, -30, -23, -30, -23,
    -38, -38, -8, -8, -8, -8, -31, -4,
    -27, -15, -31, -8, -4, -8, -8, -8,
    -4, -4, -4, -4, -15, -8, -8, -8,
    -23, -23, -8, -14, -12, -7, -8, -15,
    -8, -15, 7, -61, -61, -15, -23, 0,
    0, -4, -8, 3, -7, -8, -8, -8,
    -8, -7, -15, -15, -15, -15, -15, -15,
    -15, -15, -15, -15, -15, -7, -4, -15,
    -23, -15, -23, -38, -38, 8, -23, -23,
    -23, -23, -30, -7, -30, -15, -38, -16,
    -8, -16, -23, -23, -8, -14, -12, -7,
    -8, -15, -8, -15, -76, -76, -23, -11,
    -4, -4, -19, -8, -15, -23, -23, -23,
    -8, 3, -14, -12, -7, -8, -15, -8,
    -15, -6, -6, -6, -6, -15, -4, -8,
    -4, -11, -16, -8, -8, -8, -8, -8,
    -8, -8, -4, -4, 8, -4, -8, -8,
    0, 0, -8, -4, -8, 0, -8, -4,
    0, -8, 1, 1, -59, -59, -31, -8,
    -8, -8, -8, 0, -31, -23, -23, -23,
    -23, -8, -7, -23, -23, -23, -22, -23,
    -23, -23, 8, -30, -15, -7, -15, -15,
    -23, -4, -7, 0, -54, -54, -16, -16,
    -27, -12, -12, -12, -12, -4, -16, -8,
    -8, -8, -8, 0, 0, -8, -8, -8,
    0, 0, -30, -30, -15, -15, -15, -7,
    -7, -7, -7, -15, -8, -8, -8, -8,
    -8, -8, -8, -7, -8, -8, -8, -8,
    -7, -15, -15, -15, -15, -15, -15, -15,
    -15, -15, -15, -15, -7, -4, -15, -23,
    -15, -23, 0, 0, -54, -54, -23, -23,
    -31, -15, -15, -15, -15, 0, -39, -23,
    -23, -23, 0, -23, 0, -7, 0, -16,
    -16, -23, -15, -23, -16, -23, 0, -15,
    -15, -16, -15, -16, -8, -8, -8, -8,
    31, -23, -8, -23, -4, -4, -4, -15,
    -15, -23, -8, -8, -15, -23, -4, -8,
    -4, -8, -8, -23, -15, 8, 1, 1,
    0, 23, 0, 0, 15, 15, 8, 8,
    8, 0, 8, -11, -7, -7, -7, 0,
    -15, -7, -7, -7, -7, -7, -7, -7,
    -7, 0, 1, -22, -15, -4, -4, 1,
    1, 1, 1, -8, 16, 8, -8, -15,
    0, -8, -8, -8, -8, -8, -8, -8,
    -8, -8, -4, -8, -7, -4, -7, -22,
    -15, -4, -4, -22, -15, -4, -4, -15,
    -15, -23, -8, -8, -15, -23, -4, -8,
    -4, -15, -15, -23, -8, -8, -15, -23,
    -4, -8, -4, -7, -7, 8, 1, 1,
    -35, -35, -19, -7, -7, -7, 0, -7,
    -7, 8, -23, -8, -8, -7, -23, -7,
    -4, -7, 0, -15, -7, -35, -35, -15,
    -15, -13, -4, -4, -4, -4, -4, -19,
    -19, -7, -15, -12, -15, 0, -8, -8,
    -8, -8, -8, -8, -8, -8, -8, -4,
    -35, -35, -15, -15, -13, -4, -4, -4,
    -4, -4, -23, 1, 30, 1
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 486,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_AlibabaPuHui24 = {
#else
lv_font_t ui_font_AlibabaPuHui24 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 25,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_ALIBABAPUHUI24*/

