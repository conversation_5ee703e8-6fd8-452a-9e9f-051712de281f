/*******************************************************************************
 * Size: 18 px
 * Bpp: 4
 * Opts: --bpp 4 --size 18 --font E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts/AlibabaPuHuiTi-3-55-Regular.ttf -o E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts\ui_font_AlibabaPuHui18.c --format lvgl -r 0x20-0x7f --symbols 车辆通用其他 --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_ALIBABAPUHUI18
#define UI_FONT_ALIBABAPUHUI18 1
#endif

#if UI_FONT_ALIBABAPUHUI18

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xaf, 0x19, 0xf0, 0x9f, 0x8, 0xf0, 0x8f, 0x7,
    0xe0, 0x7e, 0x6, 0xd0, 0x6c, 0x0, 0x0, 0x23,
    0xe, 0xf4, 0xcf, 0x30,

    /* U+0022 "\"" */
    0x9f, 0x2d, 0xd7, 0xf1, 0xcc, 0x6f, 0xb, 0xb5,
    0xe0, 0xaa, 0x4d, 0x8, 0x91, 0x40, 0x33,

    /* U+0023 "#" */
    0x0, 0x4, 0xf3, 0x0, 0xe8, 0x0, 0x0, 0x7f,
    0x0, 0x1f, 0x50, 0x0, 0x9, 0xd0, 0x4, 0xf3,
    0x0, 0x0, 0xca, 0x0, 0x7f, 0x10, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x72, 0x36, 0xf6, 0x33, 0xea,
    0x31, 0x0, 0x7f, 0x0, 0x1f, 0x50, 0x0, 0xb,
    0xc0, 0x6, 0xf2, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0x72, 0x5f, 0x73, 0x3d, 0xb3, 0x31, 0x5,
    0xf1, 0x0, 0xf7, 0x0, 0x0, 0x8e, 0x0, 0x2f,
    0x40, 0x0, 0xb, 0xb0, 0x5, 0xf1, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x8, 0x90, 0x0, 0x6, 0xdf, 0xfe, 0xc4,
    0x4f, 0x8b, 0xb5, 0x84, 0x9d, 0x8, 0x90, 0x0,
    0xac, 0x8, 0x90, 0x0, 0x8f, 0x28, 0x90, 0x0,
    0x1e, 0xfe, 0xb0, 0x0, 0x1, 0x8e, 0xff, 0x80,
    0x0, 0x8, 0xa8, 0xf8, 0x0, 0x8, 0x90, 0xad,
    0x0, 0x8, 0x90, 0x7f, 0x0, 0x8, 0x90, 0x9d,
    0x98, 0x6b, 0xb9, 0xf7, 0x9d, 0xff, 0xfc, 0x60,
    0x0, 0x8, 0x90, 0x0, 0x0, 0x8, 0x90, 0x0,
    0x0, 0x4, 0x40, 0x0,

    /* U+0025 "%" */
    0xa, 0xfe, 0x50, 0x0, 0x1e, 0x30, 0x7, 0xc1,
    0x4f, 0x10, 0x8, 0xa0, 0x0, 0xb7, 0x0, 0xe4,
    0x1, 0xf2, 0x0, 0xc, 0x70, 0xd, 0x50, 0x99,
    0x0, 0x0, 0xb7, 0x0, 0xe4, 0x2f, 0x10, 0x0,
    0x7, 0xc0, 0x4f, 0x1b, 0x80, 0x0, 0x0, 0xa,
    0xed, 0x53, 0xe0, 0x7e, 0xe7, 0x0, 0x0, 0x0,
    0xc6, 0x4f, 0x11, 0xf3, 0x0, 0x0, 0x4d, 0x8,
    0xb0, 0xb, 0x70, 0x0, 0xd, 0x50, 0x9a, 0x0,
    0xa8, 0x0, 0x6, 0xc0, 0x8, 0xa0, 0xb, 0x70,
    0x0, 0xe4, 0x0, 0x4e, 0x0, 0xe4, 0x0, 0x7b,
    0x0, 0x0, 0x8c, 0xd8, 0x0,

    /* U+0026 "&" */
    0x0, 0x3, 0xcf, 0xd5, 0x0, 0x0, 0x0, 0x1e,
    0xd6, 0xcf, 0x20, 0x0, 0x0, 0x5f, 0x30, 0x3f,
    0x60, 0x0, 0x0, 0x6f, 0x30, 0x4f, 0x50, 0x0,
    0x0, 0x3f, 0x94, 0xec, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x9f, 0xfe, 0x10,
    0x7, 0x90, 0x9, 0xf6, 0x3f, 0xb0, 0xd, 0xa0,
    0x1f, 0x90, 0x6, 0xf8, 0x3f, 0x60, 0x4f, 0x60,
    0x0, 0xaf, 0xde, 0x0, 0x2f, 0xa0, 0x0, 0xe,
    0xf7, 0x0, 0xa, 0xfa, 0x67, 0xcf, 0xff, 0x83,
    0x0, 0x7d, 0xfe, 0xa2, 0x2b, 0xf8,

    /* U+0027 "'" */
    0x9f, 0x27, 0xf1, 0x6f, 0x5, 0xe0, 0x4d, 0x1,
    0x40,

    /* U+0028 "(" */
    0x0, 0x3f, 0x40, 0xc, 0xb0, 0x5, 0xf3, 0x0,
    0xbd, 0x0, 0x1f, 0x60, 0x5, 0xf2, 0x0, 0x9f,
    0x0, 0xb, 0xd0, 0x0, 0xcc, 0x0, 0xc, 0xc0,
    0x0, 0xbd, 0x0, 0x9, 0xf0, 0x0, 0x6f, 0x20,
    0x2, 0xf6, 0x0, 0xc, 0xc0, 0x0, 0x5f, 0x20,
    0x0, 0xda, 0x0, 0x4, 0xf3,

    /* U+0029 ")" */
    0xc, 0xb0, 0x0, 0x3, 0xf5, 0x0, 0x0, 0xbd,
    0x0, 0x0, 0x5f, 0x30, 0x0, 0xf, 0x90, 0x0,
    0xb, 0xd0, 0x0, 0x8, 0xf1, 0x0, 0x6, 0xf3,
    0x0, 0x5, 0xf4, 0x0, 0x5, 0xf3, 0x0, 0x6,
    0xf2, 0x0, 0x8, 0xf1, 0x0, 0xb, 0xd0, 0x0,
    0xf, 0x90, 0x0, 0x5f, 0x20, 0x0, 0xcc, 0x0,
    0x4, 0xf4, 0x0, 0xd, 0xa0, 0x0,

    /* U+002A "*" */
    0x0, 0xd, 0x90, 0x0, 0x4, 0xc, 0x80, 0x40,
    0x4f, 0xde, 0xce, 0xf0, 0x3, 0x9f, 0xf6, 0x20,
    0x0, 0xcc, 0xe9, 0x0, 0x6, 0xf2, 0x6f, 0x20,
    0x0, 0x20, 0x2, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0xdb, 0x0, 0x0, 0x0, 0x0, 0xdb,
    0x0, 0x0, 0x0, 0x0, 0xdb, 0x0, 0x0, 0x38,
    0x88, 0xed, 0x88, 0x82, 0x6f, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0xdb, 0x0, 0x0, 0x0, 0x0,
    0xdb, 0x0, 0x0, 0x0, 0x0, 0xdb, 0x0, 0x0,
    0x0, 0x0, 0x98, 0x0, 0x0,

    /* U+002C "," */
    0x0, 0xac, 0x0, 0xd9, 0x2, 0xf4, 0x8, 0xd0,
    0x1f, 0x40,

    /* U+002D "-" */
    0x1, 0x11, 0x10, 0xbf, 0xff, 0xf9, 0x24, 0x44,
    0x42,

    /* U+002E "." */
    0x4, 0x16, 0xfd, 0x4f, 0xa0,

    /* U+002F "/" */
    0x0, 0x0, 0x6, 0x40, 0x0, 0x2, 0xf4, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0xd, 0x80, 0x0, 0x3,
    0xf2, 0x0, 0x0, 0x9d, 0x0, 0x0, 0xe, 0x70,
    0x0, 0x4, 0xf1, 0x0, 0x0, 0xab, 0x0, 0x0,
    0xf, 0x60, 0x0, 0x5, 0xf0, 0x0, 0x0, 0xba,
    0x0, 0x0, 0x1f, 0x40, 0x0, 0x6, 0xe0, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x2b, 0xff, 0xd5, 0x0, 0x1, 0xee, 0x87,
    0xcf, 0x40, 0x7, 0xf3, 0x0, 0xd, 0xd0, 0xc,
    0xe0, 0x0, 0x8, 0xf1, 0xe, 0xb0, 0x0, 0x5,
    0xf4, 0xf, 0xa0, 0x0, 0x4, 0xf5, 0xf, 0x90,
    0x0, 0x3, 0xf6, 0xf, 0xa0, 0x0, 0x4, 0xf5,
    0xe, 0xb0, 0x0, 0x5, 0xf4, 0xc, 0xd0, 0x0,
    0x7, 0xf1, 0x7, 0xf3, 0x0, 0xd, 0xd0, 0x1,
    0xee, 0x87, 0xcf, 0x50, 0x0, 0x2b, 0xff, 0xd5,
    0x0,

    /* U+0031 "1" */
    0x5, 0x9c, 0xf8, 0x3f, 0xda, 0xf8, 0x0, 0x1,
    0xf8, 0x0, 0x1, 0xf8, 0x0, 0x1, 0xf8, 0x0,
    0x1, 0xf8, 0x0, 0x1, 0xf8, 0x0, 0x1, 0xf8,
    0x0, 0x1, 0xf8, 0x0, 0x1, 0xf8, 0x0, 0x1,
    0xf8, 0x0, 0x1, 0xf8, 0x0, 0x1, 0xf8,

    /* U+0032 "2" */
    0x1c, 0xef, 0xeb, 0x30, 0x1, 0x96, 0x68, 0xef,
    0x30, 0x0, 0x0, 0x2, 0xf9, 0x0, 0x0, 0x0,
    0xd, 0xb0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0,
    0x0, 0x4f, 0x60, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xb, 0xf3, 0x0, 0x0, 0xb, 0xf4, 0x0,
    0x0, 0x1c, 0xe3, 0x0, 0x0, 0x2d, 0xd2, 0x0,
    0x0, 0xe, 0xf6, 0x66, 0x66, 0x61, 0xff, 0xff,
    0xff, 0xff, 0x40,

    /* U+0033 "3" */
    0x1c, 0xef, 0xea, 0x20, 0x19, 0x65, 0x8e, 0xf1,
    0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x0, 0xf8,
    0x0, 0x0, 0x3, 0xf6, 0x0, 0x1, 0x5e, 0xd0,
    0x4, 0xff, 0xfc, 0x10, 0x0, 0x34, 0x6d, 0xe2,
    0x0, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x0, 0xbd,
    0x0, 0x0, 0x1, 0xeb, 0x77, 0x66, 0x8e, 0xf4,
    0x9e, 0xff, 0xea, 0x30,

    /* U+0034 "4" */
    0x0, 0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x8e, 0x8f, 0x0, 0x0,
    0x2, 0xf6, 0x7f, 0x0, 0x0, 0xc, 0xb0, 0x7f,
    0x0, 0x0, 0x7f, 0x20, 0x7f, 0x0, 0x2, 0xf8,
    0x0, 0x7f, 0x0, 0xb, 0xd0, 0x0, 0x7f, 0x0,
    0x4f, 0x51, 0x11, 0x8f, 0x20, 0x6f, 0xff, 0xff,
    0xff, 0xf8, 0x14, 0x44, 0x44, 0x9f, 0x52, 0x0,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x0,

    /* U+0035 "5" */
    0xa, 0xff, 0xff, 0xf4, 0x0, 0xcc, 0x66, 0x66,
    0x10, 0xe, 0x70, 0x0, 0x0, 0x0, 0xf5, 0x0,
    0x0, 0x0, 0x2f, 0x30, 0x0, 0x0, 0x4, 0xff,
    0xfe, 0xb5, 0x0, 0x25, 0x33, 0x5b, 0xf7, 0x0,
    0x0, 0x0, 0xc, 0xe0, 0x0, 0x0, 0x0, 0x8f,
    0x10, 0x0, 0x0, 0x8, 0xf0, 0x0, 0x0, 0x1,
    0xdc, 0x4, 0x86, 0x68, 0xef, 0x40, 0x5e, 0xff,
    0xda, 0x20, 0x0,

    /* U+0036 "6" */
    0x0, 0x4, 0xae, 0xa0, 0x0, 0xa, 0xfd, 0x84,
    0x0, 0x9, 0xf6, 0x0, 0x0, 0x2, 0xf8, 0x0,
    0x0, 0x0, 0x8f, 0x0, 0x0, 0x0, 0xb, 0xc8,
    0xef, 0xd7, 0x0, 0xef, 0xb5, 0x48, 0xfa, 0xf,
    0xd0, 0x0, 0x8, 0xf1, 0xf9, 0x0, 0x0, 0x4f,
    0x3d, 0xb0, 0x0, 0x5, 0xf2, 0x9f, 0x10, 0x0,
    0xaf, 0x2, 0xfd, 0x76, 0xbf, 0x70, 0x2, 0xbe,
    0xfd, 0x60, 0x0,

    /* U+0037 "7" */
    0xf, 0xff, 0xff, 0xff, 0xf3, 0x6, 0x66, 0x66,
    0x69, 0xf2, 0x0, 0x0, 0x0, 0xa, 0xd0, 0x0,
    0x0, 0x0, 0x2f, 0x50, 0x0, 0x0, 0x0, 0x9e,
    0x0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0,
    0x8, 0xf1, 0x0, 0x0, 0x0, 0xf, 0x90, 0x0,
    0x0, 0x0, 0x7f, 0x20, 0x0, 0x0, 0x0, 0xea,
    0x0, 0x0, 0x0, 0x6, 0xf3, 0x0, 0x0, 0x0,
    0xd, 0xc0, 0x0, 0x0, 0x0, 0x5f, 0x50, 0x0,
    0x0,

    /* U+0038 "8" */
    0x0, 0x3b, 0xff, 0xd6, 0x0, 0x2, 0xfc, 0x65,
    0xaf, 0x60, 0x8, 0xf1, 0x0, 0xb, 0xd0, 0xa,
    0xc0, 0x0, 0x7, 0xf0, 0x8, 0xe0, 0x0, 0x9,
    0xe0, 0x2, 0xf8, 0x10, 0x5f, 0x70, 0x0, 0x4f,
    0xff, 0xfa, 0x0, 0x4, 0xf8, 0x43, 0x6e, 0xa0,
    0xe, 0xa0, 0x0, 0x4, 0xf3, 0xf, 0x70, 0x0,
    0x1, 0xf6, 0xf, 0xa0, 0x0, 0x5, 0xf5, 0x8,
    0xfa, 0x65, 0x8f, 0xd0, 0x0, 0x6c, 0xef, 0xd8,
    0x10,

    /* U+0039 "9" */
    0x3, 0xbf, 0xfc, 0x40, 0x2, 0xfd, 0x76, 0xcf,
    0x40, 0xae, 0x0, 0x0, 0xcc, 0xd, 0xa0, 0x0,
    0x6, 0xf1, 0xf9, 0x0, 0x0, 0x5f, 0x3d, 0xc0,
    0x0, 0x7, 0xf3, 0x7f, 0x71, 0x15, 0xff, 0x20,
    0x8f, 0xff, 0xdb, 0xf0, 0x0, 0x2, 0x20, 0xbc,
    0x0, 0x0, 0x0, 0x3f, 0x60, 0x0, 0x0, 0x2d,
    0xd0, 0x0, 0x26, 0xaf, 0xd1, 0x0, 0x6, 0xec,
    0x70, 0x0, 0x0,

    /* U+003A ":" */
    0x9f, 0x6b, 0xf7, 0x14, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x40, 0xbf, 0x79, 0xf6,

    /* U+003B ";" */
    0x9, 0xf6, 0xb, 0xf7, 0x1, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x72,
    0x3, 0xf3, 0x7, 0xe0, 0xd, 0x80, 0x5e, 0x10,
    0x32, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xb4, 0x0, 0x0, 0x18, 0xef, 0xc2, 0x0,
    0x4b, 0xff, 0x93, 0x0, 0x3d, 0xfd, 0x60, 0x0,
    0x0, 0x6f, 0x60, 0x0, 0x0, 0x0, 0x4f, 0xfb,
    0x50, 0x0, 0x0, 0x0, 0x6c, 0xfe, 0x81, 0x0,
    0x0, 0x0, 0x3a, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0x17, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003D "=" */
    0x6f, 0xff, 0xff, 0xff, 0xf5, 0x38, 0x88, 0x88,
    0x88, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0x88, 0x88, 0x88,
    0x82, 0x6f, 0xff, 0xff, 0xff, 0xf5,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5b, 0x40, 0x0,
    0x0, 0x0, 0x3d, 0xfe, 0x71, 0x0, 0x0, 0x0,
    0x3a, 0xff, 0xa4, 0x0, 0x0, 0x0, 0x17, 0xdf,
    0xd2, 0x0, 0x0, 0x0, 0x7, 0xf5, 0x0, 0x0,
    0x5, 0xcf, 0xe3, 0x0, 0x28, 0xef, 0xc5, 0x0,
    0x2b, 0xff, 0x93, 0x0, 0x0, 0x6d, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x2c, 0xef, 0xeb, 0x30, 0x3a, 0x87, 0x9f, 0xe1,
    0x0, 0x0, 0x7, 0xf4, 0x0, 0x0, 0x5, 0xf5,
    0x0, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x5f, 0xb0,
    0x0, 0x6, 0xfa, 0x0, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0x4f, 0x30, 0x0, 0x0, 0x14, 0x0, 0x0,
    0x0, 0x14, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x0,
    0x0, 0x8f, 0x60, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x4a, 0xef, 0xfc, 0x81, 0x0, 0x0,
    0x1b, 0xfa, 0x64, 0x58, 0xee, 0x40, 0x0, 0xdd,
    0x20, 0x0, 0x0, 0xb, 0xf1, 0xa, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0xe8, 0x3f, 0x40, 0x7, 0xde,
    0x9c, 0x80, 0xac, 0x9d, 0x0, 0x8f, 0x63, 0xbf,
    0x50, 0x7e, 0xd9, 0x1, 0xf6, 0x0, 0x5f, 0x20,
    0x7d, 0xe7, 0x6, 0xf1, 0x0, 0x6f, 0x0, 0x9b,
    0xf6, 0x7, 0xf0, 0x0, 0xac, 0x0, 0xd8, 0xe8,
    0x5, 0xf4, 0x6, 0xfe, 0x17, 0xf2, 0xab, 0x0,
    0xaf, 0xfe, 0x6d, 0xff, 0x50, 0x5f, 0x20, 0x1,
    0x20, 0x0, 0x20, 0x0, 0xc, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xde, 0x73, 0x10, 0x24,
    0x82, 0x0, 0x0, 0x7, 0xdf, 0xff, 0xff, 0xc2,
    0x0, 0x0, 0x0, 0x1, 0x23, 0x10, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x9e, 0xea, 0x0, 0x0, 0x0, 0x0, 0xf9, 0x8f,
    0x0, 0x0, 0x0, 0x5, 0xf3, 0x3f, 0x50, 0x0,
    0x0, 0xb, 0xe0, 0xe, 0xb0, 0x0, 0x0, 0x1f,
    0x90, 0x9, 0xf1, 0x0, 0x0, 0x6f, 0x30, 0x3,
    0xf7, 0x0, 0x0, 0xce, 0x22, 0x22, 0xed, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0x20, 0x8, 0xf6,
    0x55, 0x55, 0x6f, 0x80, 0xe, 0xc0, 0x0, 0x0,
    0xd, 0xe0, 0x4f, 0x60, 0x0, 0x0, 0x7, 0xf4,
    0x9f, 0x10, 0x0, 0x0, 0x1, 0xfa,

    /* U+0042 "B" */
    0x5f, 0xff, 0xfe, 0xb2, 0x5, 0xf8, 0x66, 0x9f,
    0xe1, 0x5f, 0x40, 0x0, 0x5f, 0x55, 0xf4, 0x0,
    0x2, 0xf6, 0x5f, 0x40, 0x0, 0x4f, 0x55, 0xf5,
    0x1, 0x4d, 0xd0, 0x5f, 0xff, 0xff, 0xe3, 0x5,
    0xf7, 0x34, 0x6c, 0xf4, 0x5f, 0x40, 0x0, 0xe,
    0xb5, 0xf4, 0x0, 0x0, 0xcd, 0x5f, 0x40, 0x0,
    0xe, 0xb5, 0xf9, 0x66, 0x7d, 0xf6, 0x5f, 0xff,
    0xff, 0xc6, 0x0,

    /* U+0043 "C" */
    0x0, 0x19, 0xdf, 0xfe, 0xa0, 0x3, 0xef, 0xa7,
    0x78, 0xa0, 0xd, 0xf3, 0x0, 0x0, 0x0, 0x4f,
    0x70, 0x0, 0x0, 0x0, 0x8f, 0x20, 0x0, 0x0,
    0x0, 0xaf, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x20, 0x0, 0x0, 0x0, 0x5f, 0x60, 0x0,
    0x0, 0x0, 0xe, 0xe2, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xa8, 0x89, 0xd0, 0x0, 0x3a, 0xef, 0xfd,
    0x90,

    /* U+0044 "D" */
    0x5f, 0xff, 0xfe, 0xc7, 0x0, 0x5, 0xf9, 0x67,
    0x9d, 0xfc, 0x0, 0x5f, 0x40, 0x0, 0x9, 0xf8,
    0x5, 0xf4, 0x0, 0x0, 0xe, 0xe0, 0x5f, 0x40,
    0x0, 0x0, 0x9f, 0x15, 0xf4, 0x0, 0x0, 0x7,
    0xf3, 0x5f, 0x40, 0x0, 0x0, 0x6f, 0x45, 0xf4,
    0x0, 0x0, 0x7, 0xf3, 0x5f, 0x40, 0x0, 0x0,
    0x9f, 0x15, 0xf4, 0x0, 0x0, 0xe, 0xd0, 0x5f,
    0x40, 0x0, 0xa, 0xf6, 0x5, 0xf9, 0x77, 0x9d,
    0xfa, 0x0, 0x5f, 0xff, 0xfe, 0xb5, 0x0, 0x0,

    /* U+0045 "E" */
    0x5f, 0xff, 0xff, 0xf9, 0x5, 0xf9, 0x77, 0x77,
    0x40, 0x5f, 0x40, 0x0, 0x0, 0x5, 0xf4, 0x0,
    0x0, 0x0, 0x5f, 0x40, 0x0, 0x0, 0x5, 0xf5,
    0x11, 0x11, 0x0, 0x5f, 0xff, 0xff, 0xf4, 0x5,
    0xf7, 0x44, 0x44, 0x10, 0x5f, 0x40, 0x0, 0x0,
    0x5, 0xf4, 0x0, 0x0, 0x0, 0x5f, 0x40, 0x0,
    0x0, 0x5, 0xf9, 0x77, 0x77, 0x70, 0x5f, 0xff,
    0xff, 0xff, 0x0,

    /* U+0046 "F" */
    0x5f, 0xff, 0xff, 0xf9, 0x5f, 0x97, 0x77, 0x74,
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x52, 0x22, 0x20,
    0x5f, 0xff, 0xff, 0xf4, 0x5f, 0x85, 0x55, 0x51,
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5f, 0x40, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x18, 0xdf, 0xfe, 0xc5, 0x3, 0xef, 0xb8,
    0x78, 0xa7, 0xd, 0xf3, 0x0, 0x0, 0x0, 0x4f,
    0x70, 0x0, 0x0, 0x0, 0x8f, 0x20, 0x0, 0x0,
    0x0, 0xaf, 0x0, 0x0, 0x11, 0x11, 0xbf, 0x0,
    0x2, 0xff, 0xfe, 0xaf, 0x0, 0x0, 0x44, 0xbe,
    0x8f, 0x20, 0x0, 0x0, 0xae, 0x5f, 0x60, 0x0,
    0x0, 0xae, 0xe, 0xe1, 0x0, 0x0, 0xae, 0x5,
    0xff, 0xa8, 0x68, 0xde, 0x0, 0x3a, 0xdf, 0xfe,
    0xc8,

    /* U+0048 "H" */
    0x5f, 0x40, 0x0, 0x0, 0x8f, 0x15, 0xf4, 0x0,
    0x0, 0x8, 0xf1, 0x5f, 0x40, 0x0, 0x0, 0x8f,
    0x15, 0xf4, 0x0, 0x0, 0x8, 0xf1, 0x5f, 0x40,
    0x0, 0x0, 0x8f, 0x15, 0xf5, 0x11, 0x11, 0x19,
    0xf1, 0x5f, 0xff, 0xff, 0xff, 0xff, 0x15, 0xf7,
    0x44, 0x44, 0x4a, 0xf1, 0x5f, 0x40, 0x0, 0x0,
    0x8f, 0x15, 0xf4, 0x0, 0x0, 0x8, 0xf1, 0x5f,
    0x40, 0x0, 0x0, 0x8f, 0x15, 0xf4, 0x0, 0x0,
    0x8, 0xf1, 0x5f, 0x40, 0x0, 0x0, 0x8f, 0x10,

    /* U+0049 "I" */
    0x5f, 0x45, 0xf4, 0x5f, 0x45, 0xf4, 0x5f, 0x45,
    0xf4, 0x5f, 0x45, 0xf4, 0x5f, 0x45, 0xf4, 0x5f,
    0x45, 0xf4, 0x5f, 0x40,

    /* U+004A "J" */
    0x0, 0x5, 0xf4, 0x0, 0x5, 0xf4, 0x0, 0x5,
    0xf4, 0x0, 0x5, 0xf4, 0x0, 0x5, 0xf4, 0x0,
    0x5, 0xf4, 0x0, 0x5, 0xf4, 0x0, 0x5, 0xf4,
    0x0, 0x5, 0xf4, 0x0, 0x5, 0xf4, 0x0, 0x5,
    0xf4, 0x0, 0x5, 0xf4, 0x0, 0x5, 0xf4, 0x0,
    0x5, 0xf4, 0x0, 0x9, 0xf1, 0x37, 0x9f, 0xb0,
    0x5f, 0xfa, 0x10,

    /* U+004B "K" */
    0x5f, 0x40, 0x0, 0xc, 0xf3, 0x5f, 0x40, 0x0,
    0x9f, 0x50, 0x5f, 0x40, 0x5, 0xf9, 0x0, 0x5f,
    0x40, 0x2f, 0xc0, 0x0, 0x5f, 0x40, 0xce, 0x10,
    0x0, 0x5f, 0x59, 0xf3, 0x0, 0x0, 0x5f, 0xff,
    0xa0, 0x0, 0x0, 0x5f, 0x8b, 0xf4, 0x0, 0x0,
    0x5f, 0x40, 0xde, 0x20, 0x0, 0x5f, 0x40, 0x2f,
    0xd0, 0x0, 0x5f, 0x40, 0x5, 0xfa, 0x0, 0x5f,
    0x40, 0x0, 0x8f, 0x70, 0x5f, 0x40, 0x0, 0xc,
    0xf4,

    /* U+004C "L" */
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x40, 0x0, 0x0,
    0x5f, 0x40, 0x0, 0x0, 0x5f, 0x97, 0x77, 0x75,
    0x5f, 0xff, 0xff, 0xfb,

    /* U+004D "M" */
    0x5f, 0xf7, 0x0, 0x0, 0x7, 0xff, 0x45, 0xff,
    0xd0, 0x0, 0x0, 0xdf, 0xf4, 0x5f, 0x9f, 0x30,
    0x0, 0x3f, 0x9f, 0x45, 0xf4, 0xe9, 0x0, 0x9,
    0xd5, 0xf4, 0x5f, 0x39, 0xe0, 0x0, 0xf7, 0x5f,
    0x45, 0xf3, 0x2f, 0x50, 0x5f, 0x15, 0xf4, 0x5f,
    0x30, 0xcb, 0xb, 0xb0, 0x5f, 0x45, 0xf3, 0x6,
    0xf3, 0xf5, 0x5, 0xf4, 0x5f, 0x30, 0xf, 0xee,
    0x0, 0x5f, 0x45, 0xf3, 0x0, 0x9f, 0x90, 0x5,
    0xf4, 0x5f, 0x30, 0x0, 0x0, 0x0, 0x5f, 0x45,
    0xf3, 0x0, 0x0, 0x0, 0x5, 0xf4, 0x5f, 0x30,
    0x0, 0x0, 0x0, 0x5f, 0x40,

    /* U+004E "N" */
    0x5f, 0xf7, 0x0, 0x0, 0xf, 0x95, 0xfe, 0xe0,
    0x0, 0x0, 0xf9, 0x5f, 0x6f, 0x80, 0x0, 0xf,
    0x95, 0xf3, 0x9f, 0x10, 0x0, 0xf9, 0x5f, 0x31,
    0xf8, 0x0, 0xf, 0x95, 0xf3, 0x8, 0xf1, 0x0,
    0xf9, 0x5f, 0x30, 0x1f, 0x90, 0xf, 0x95, 0xf3,
    0x0, 0x7f, 0x20, 0xf9, 0x5f, 0x30, 0x0, 0xea,
    0xf, 0x95, 0xf3, 0x0, 0x6, 0xf3, 0xf9, 0x5f,
    0x30, 0x0, 0xd, 0xbf, 0x95, 0xf3, 0x0, 0x0,
    0x5f, 0xf9, 0x5f, 0x30, 0x0, 0x0, 0xcf, 0x90,

    /* U+004F "O" */
    0x0, 0x4b, 0xef, 0xfc, 0x70, 0x0, 0x6, 0xfd,
    0x86, 0x7b, 0xfc, 0x0, 0x1f, 0xc0, 0x0, 0x0,
    0x7f, 0x60, 0x6f, 0x50, 0x0, 0x0, 0xe, 0xb0,
    0x9f, 0x10, 0x0, 0x0, 0xb, 0xe0, 0xaf, 0x0,
    0x0, 0x0, 0xa, 0xf0, 0xbf, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0xaf, 0x0, 0x0, 0x0, 0xa, 0xf0,
    0x9f, 0x10, 0x0, 0x0, 0xb, 0xe0, 0x6f, 0x50,
    0x0, 0x0, 0xe, 0xb0, 0x1f, 0xc0, 0x0, 0x0,
    0x7f, 0x60, 0x6, 0xfd, 0x86, 0x7b, 0xfc, 0x0,
    0x0, 0x4b, 0xef, 0xfc, 0x70, 0x0,

    /* U+0050 "P" */
    0x5f, 0xff, 0xfe, 0xa3, 0x5, 0xf9, 0x67, 0x9f,
    0xf2, 0x5f, 0x40, 0x0, 0x5f, 0x85, 0xf4, 0x0,
    0x0, 0xfa, 0x5f, 0x40, 0x0, 0xf, 0xa5, 0xf4,
    0x0, 0x2, 0xf8, 0x5f, 0x51, 0x14, 0xcf, 0x35,
    0xff, 0xff, 0xfe, 0x50, 0x5f, 0x74, 0x32, 0x0,
    0x5, 0xf4, 0x0, 0x0, 0x0, 0x5f, 0x40, 0x0,
    0x0, 0x5, 0xf4, 0x0, 0x0, 0x0, 0x5f, 0x40,
    0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x4b, 0xef, 0xfc, 0x70, 0x0, 0x6, 0xfd,
    0x86, 0x7b, 0xfc, 0x0, 0x1f, 0xc0, 0x0, 0x0,
    0x7f, 0x60, 0x6f, 0x50, 0x0, 0x0, 0xe, 0xb0,
    0x9f, 0x10, 0x0, 0x0, 0xb, 0xe0, 0xaf, 0x0,
    0x0, 0x0, 0xa, 0xf0, 0xbf, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0xaf, 0x0, 0x0, 0x0, 0xa, 0xf0,
    0x9f, 0x10, 0x0, 0x0, 0xb, 0xe0, 0x6f, 0x50,
    0x0, 0x0, 0xe, 0xb0, 0x1f, 0xc0, 0x0, 0x0,
    0x7f, 0x60, 0x6, 0xfd, 0x86, 0x7b, 0xfc, 0x0,
    0x0, 0x4b, 0xef, 0xfd, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xdd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x5, 0x81, 0x0,

    /* U+0052 "R" */
    0x5f, 0xff, 0xfe, 0xa2, 0x0, 0x5f, 0x96, 0x7a,
    0xfe, 0x10, 0x5f, 0x40, 0x0, 0x6f, 0x60, 0x5f,
    0x40, 0x0, 0x1f, 0x80, 0x5f, 0x40, 0x0, 0x1f,
    0x80, 0x5f, 0x40, 0x0, 0x4f, 0x60, 0x5f, 0x51,
    0x25, 0xdf, 0x10, 0x5f, 0xff, 0xff, 0xd4, 0x0,
    0x5f, 0x74, 0x7f, 0x50, 0x0, 0x5f, 0x40, 0xa,
    0xf2, 0x0, 0x5f, 0x40, 0x0, 0xdd, 0x0, 0x5f,
    0x40, 0x0, 0x3f, 0xa0, 0x5f, 0x40, 0x0, 0x7,
    0xf6,

    /* U+0053 "S" */
    0x5, 0xcf, 0xfe, 0xc4, 0x4, 0xfd, 0x87, 0x8a,
    0x70, 0xbf, 0x0, 0x0, 0x0, 0xc, 0xd0, 0x0,
    0x0, 0x0, 0xaf, 0x20, 0x0, 0x0, 0x3, 0xff,
    0xb6, 0x20, 0x0, 0x1, 0x8d, 0xff, 0xb1, 0x0,
    0x0, 0x2, 0x8f, 0xc0, 0x0, 0x0, 0x0, 0x9f,
    0x10, 0x0, 0x0, 0x7, 0xf2, 0x0, 0x0, 0x0,
    0xbf, 0xd, 0xa8, 0x78, 0xcf, 0x90, 0xad, 0xff,
    0xfc, 0x70, 0x0,

    /* U+0054 "T" */
    0xbf, 0xff, 0xff, 0xff, 0xf3, 0x57, 0x77, 0xfc,
    0x77, 0x71, 0x0, 0x1, 0xf9, 0x0, 0x0, 0x0,
    0x1, 0xf9, 0x0, 0x0, 0x0, 0x1, 0xf9, 0x0,
    0x0, 0x0, 0x1, 0xf9, 0x0, 0x0, 0x0, 0x1,
    0xf9, 0x0, 0x0, 0x0, 0x1, 0xf9, 0x0, 0x0,
    0x0, 0x1, 0xf9, 0x0, 0x0, 0x0, 0x1, 0xf9,
    0x0, 0x0, 0x0, 0x1, 0xf9, 0x0, 0x0, 0x0,
    0x1, 0xf9, 0x0, 0x0, 0x0, 0x1, 0xf9, 0x0,
    0x0,

    /* U+0055 "U" */
    0x6f, 0x30, 0x0, 0x0, 0x9f, 0x6, 0xf3, 0x0,
    0x0, 0x9, 0xf0, 0x6f, 0x30, 0x0, 0x0, 0x9f,
    0x6, 0xf3, 0x0, 0x0, 0x9, 0xf0, 0x6f, 0x30,
    0x0, 0x0, 0x9f, 0x6, 0xf3, 0x0, 0x0, 0x9,
    0xf0, 0x6f, 0x30, 0x0, 0x0, 0x9f, 0x6, 0xf3,
    0x0, 0x0, 0x9, 0xf0, 0x6f, 0x30, 0x0, 0x0,
    0x9f, 0x5, 0xf4, 0x0, 0x0, 0xa, 0xf0, 0x2f,
    0xb0, 0x0, 0x1, 0xfc, 0x0, 0xaf, 0xc7, 0x78,
    0xef, 0x40, 0x0, 0x7c, 0xff, 0xea, 0x30, 0x0,

    /* U+0056 "V" */
    0x8f, 0x20, 0x0, 0x0, 0xe, 0xa3, 0xf7, 0x0,
    0x0, 0x4, 0xf5, 0xe, 0xc0, 0x0, 0x0, 0x9f,
    0x0, 0x9f, 0x10, 0x0, 0xe, 0xa0, 0x4, 0xf6,
    0x0, 0x3, 0xf5, 0x0, 0xe, 0xb0, 0x0, 0x8f,
    0x0, 0x0, 0x9f, 0x10, 0xd, 0xa0, 0x0, 0x4,
    0xf5, 0x2, 0xf5, 0x0, 0x0, 0xe, 0xa0, 0x7f,
    0x0, 0x0, 0x0, 0xaf, 0xd, 0xa0, 0x0, 0x0,
    0x4, 0xf7, 0xf5, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0,

    /* U+0057 "W" */
    0x6f, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf9,
    0x3f, 0x70, 0x0, 0x7, 0x71, 0x0, 0x4, 0xf5,
    0xf, 0xa0, 0x0, 0x1f, 0xf5, 0x0, 0x7, 0xf2,
    0xb, 0xe0, 0x0, 0x6f, 0xd9, 0x0, 0xa, 0xe0,
    0x8, 0xf1, 0x0, 0xab, 0x9e, 0x0, 0xe, 0xa0,
    0x4, 0xf5, 0x0, 0xe7, 0x4f, 0x20, 0x1f, 0x70,
    0x1, 0xf8, 0x3, 0xf2, 0xf, 0x60, 0x5f, 0x30,
    0x0, 0xdb, 0x7, 0xe0, 0xc, 0xa0, 0x8f, 0x0,
    0x0, 0x9f, 0xb, 0xa0, 0x8, 0xe0, 0xbc, 0x0,
    0x0, 0x6f, 0x3f, 0x60, 0x4, 0xf3, 0xf8, 0x0,
    0x0, 0x2f, 0xaf, 0x10, 0x0, 0xfa, 0xf4, 0x0,
    0x0, 0xf, 0xfd, 0x0, 0x0, 0xbf, 0xf1, 0x0,
    0x0, 0xb, 0xf9, 0x0, 0x0, 0x7f, 0xd0, 0x0,

    /* U+0058 "X" */
    0xe, 0xe0, 0x0, 0x0, 0x6f, 0x50, 0x5f, 0x70,
    0x0, 0x1e, 0xb0, 0x0, 0xcf, 0x10, 0x9, 0xf2,
    0x0, 0x3, 0xf9, 0x3, 0xf7, 0x0, 0x0, 0x9,
    0xf2, 0xcd, 0x0, 0x0, 0x0, 0x1e, 0xef, 0x30,
    0x0, 0x0, 0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0,
    0x3f, 0xbf, 0x70, 0x0, 0x0, 0xc, 0xe0, 0xaf,
    0x10, 0x0, 0x6, 0xf5, 0x2, 0xfb, 0x0, 0x1,
    0xfb, 0x0, 0x8, 0xf4, 0x0, 0xaf, 0x20, 0x0,
    0xe, 0xd0, 0x4f, 0x80, 0x0, 0x0, 0x6f, 0x70,

    /* U+0059 "Y" */
    0x9f, 0x40, 0x0, 0x0, 0x7f, 0x41, 0xed, 0x0,
    0x0, 0x1f, 0xa0, 0x6, 0xf6, 0x0, 0xa, 0xf1,
    0x0, 0xc, 0xe1, 0x3, 0xf7, 0x0, 0x0, 0x3f,
    0x80, 0xcd, 0x0, 0x0, 0x0, 0xaf, 0x7f, 0x40,
    0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x8, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x20,
    0x0, 0x0, 0x0, 0x7, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x20, 0x0, 0x0, 0x0, 0x7, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x20, 0x0, 0x0,

    /* U+005A "Z" */
    0x9f, 0xff, 0xff, 0xff, 0xf1, 0x48, 0x88, 0x88,
    0x8f, 0xf0, 0x0, 0x0, 0x0, 0x8f, 0x60, 0x0,
    0x0, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x1e, 0xd0,
    0x0, 0x0, 0x0, 0xbf, 0x30, 0x0, 0x0, 0x7,
    0xf7, 0x0, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0,
    0x0, 0xde, 0x10, 0x0, 0x0, 0xa, 0xf4, 0x0,
    0x0, 0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0xef,
    0x88, 0x88, 0x88, 0x84, 0xff, 0xff, 0xff, 0xff,
    0xf8,

    /* U+005B "[" */
    0xbf, 0xfa, 0xbe, 0x53, 0xbd, 0x0, 0xbd, 0x0,
    0xbd, 0x0, 0xbd, 0x0, 0xbd, 0x0, 0xbd, 0x0,
    0xbd, 0x0, 0xbd, 0x0, 0xbd, 0x0, 0xbd, 0x0,
    0xbd, 0x0, 0xbd, 0x0, 0xbd, 0x0, 0xbd, 0x0,
    0xbe, 0x53, 0xbf, 0xfa,

    /* U+005C "\\" */
    0x6f, 0x10, 0x0, 0x0, 0x0, 0xe8, 0x0, 0x0,
    0x0, 0x7, 0xe0, 0x0, 0x0, 0x0, 0x1f, 0x60,
    0x0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x2,
    0xf5, 0x0, 0x0, 0x0, 0xb, 0xc0, 0x0, 0x0,
    0x0, 0x4f, 0x30, 0x0, 0x0, 0x0, 0xda, 0x0,
    0x0, 0x0, 0x5, 0xf1, 0x0, 0x0, 0x0, 0xe,
    0x80, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0,
    0x1, 0xf6, 0x0, 0x0, 0x0, 0x9, 0xd0, 0x0,
    0x0, 0x0, 0x2f, 0x40, 0x0, 0x0, 0x0, 0x65,

    /* U+005D "]" */
    0x6f, 0xff, 0x2, 0x5b, 0xf0, 0x0, 0x8f, 0x0,
    0x8, 0xf0, 0x0, 0x8f, 0x0, 0x8, 0xf0, 0x0,
    0x8f, 0x0, 0x8, 0xf0, 0x0, 0x8f, 0x0, 0x8,
    0xf0, 0x0, 0x8f, 0x0, 0x8, 0xf0, 0x0, 0x8f,
    0x0, 0x8, 0xf0, 0x0, 0x8f, 0x0, 0x8, 0xf0,
    0x25, 0xbf, 0x6, 0xff, 0xf0,

    /* U+005E "^" */
    0x0, 0x4f, 0xf3, 0x0, 0x0, 0xad, 0xea, 0x0,
    0x1, 0xf6, 0x7f, 0x10, 0x7, 0xf1, 0x1f, 0x60,
    0xe, 0xa0, 0xb, 0xd0, 0x4f, 0x30, 0x4, 0xf3,
    0xbd, 0x0, 0x0, 0xe9,

    /* U+005F "_" */
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0x33, 0x33, 0x33, 0x33, 0x30,

    /* U+0060 "`" */
    0x68, 0x10, 0x2e, 0x90, 0x3, 0xf3,

    /* U+0061 "a" */
    0x0, 0xa, 0xff, 0xeb, 0x30, 0x0, 0x4, 0x54,
    0x6e, 0xf1, 0x0, 0x0, 0x0, 0x5, 0xf4, 0x0,
    0x0, 0x0, 0x3, 0xf5, 0x0, 0x8d, 0xff, 0xff,
    0xf5, 0x9, 0xf7, 0x43, 0x35, 0xf5, 0xf, 0xb0,
    0x0, 0x3, 0xf5, 0xf, 0xa0, 0x0, 0x9, 0xf5,
    0xa, 0xf5, 0x24, 0xad, 0xf5, 0x1, 0xae, 0xfd,
    0x81, 0xf5,

    /* U+0062 "b" */
    0x5f, 0x30, 0x0, 0x0, 0x0, 0x5f, 0x30, 0x0,
    0x0, 0x0, 0x5f, 0x30, 0x0, 0x0, 0x0, 0x5f,
    0x30, 0x0, 0x0, 0x0, 0x5f, 0x39, 0xef, 0xd8,
    0x0, 0x5f, 0xcb, 0x65, 0xaf, 0x90, 0x5f, 0xb0,
    0x0, 0xb, 0xf0, 0x5f, 0x40, 0x0, 0x6, 0xf3,
    0x5f, 0x20, 0x0, 0x4, 0xf4, 0x5f, 0x20, 0x0,
    0x4, 0xf4, 0x5f, 0x40, 0x0, 0x6, 0xf3, 0x5f,
    0xb0, 0x0, 0xb, 0xf0, 0x5f, 0xcb, 0x65, 0xbf,
    0x80, 0x5f, 0x19, 0xef, 0xd7, 0x0,

    /* U+0063 "c" */
    0x2, 0xad, 0xfe, 0xc0, 0x2f, 0xe8, 0x67, 0xa1,
    0x9f, 0x20, 0x0, 0x0, 0xdd, 0x0, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x0,
    0xdd, 0x0, 0x0, 0x0, 0x9f, 0x20, 0x0, 0x0,
    0x2f, 0xe8, 0x67, 0xa1, 0x2, 0xad, 0xff, 0xc1,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xb, 0xd0, 0x0, 0x0, 0x0,
    0xbd, 0x0, 0x0, 0x0, 0xb, 0xd0, 0x0, 0x0,
    0x0, 0xbd, 0x2, 0xbf, 0xfc, 0x3b, 0xd2, 0xfe,
    0x75, 0x8e, 0xdd, 0x9f, 0x30, 0x0, 0x4f, 0xdc,
    0xd0, 0x0, 0x0, 0xed, 0xec, 0x0, 0x0, 0xb,
    0xde, 0xc0, 0x0, 0x0, 0xbd, 0xcd, 0x0, 0x0,
    0xd, 0xd9, 0xf1, 0x0, 0x2, 0xfd, 0x2f, 0xd4,
    0x25, 0xdd, 0xd0, 0x3c, 0xff, 0xc4, 0x9d,

    /* U+0065 "e" */
    0x2, 0xad, 0xfe, 0x80, 0x2, 0xfe, 0x65, 0x7f,
    0xb0, 0x9f, 0x10, 0x0, 0x6f, 0x2d, 0xc0, 0x0,
    0x3, 0xf4, 0xee, 0xab, 0xcd, 0xef, 0x5e, 0xd5,
    0x55, 0x55, 0x51, 0xdd, 0x0, 0x0, 0x0, 0x9,
    0xf3, 0x0, 0x0, 0x0, 0x2f, 0xf8, 0x67, 0x9b,
    0x0, 0x2a, 0xdf, 0xfd, 0x70,

    /* U+0066 "f" */
    0x0, 0x1b, 0xef, 0x40, 0xa, 0xf6, 0x41, 0x0,
    0xfa, 0x0, 0x0, 0xf, 0x80, 0x0, 0xaf, 0xff,
    0xfc, 0x3, 0x5f, 0xa4, 0x30, 0x1, 0xf7, 0x0,
    0x0, 0x1f, 0x70, 0x0, 0x1, 0xf7, 0x0, 0x0,
    0x1f, 0x70, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x1f,
    0x70, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x1f, 0x70,
    0x0,

    /* U+0067 "g" */
    0x3, 0xbf, 0xfc, 0x3b, 0xd2, 0xfe, 0x75, 0x8e,
    0xcd, 0x9f, 0x30, 0x0, 0x4f, 0xdc, 0xd0, 0x0,
    0x0, 0xdd, 0xec, 0x0, 0x0, 0xb, 0xde, 0xc0,
    0x0, 0x0, 0xbd, 0xcd, 0x0, 0x0, 0xd, 0xd9,
    0xf2, 0x0, 0x3, 0xfd, 0x3f, 0xe6, 0x57, 0xed,
    0xd0, 0x3c, 0xff, 0xc4, 0xcc, 0x0, 0x0, 0x0,
    0xe, 0xb0, 0x0, 0x0, 0x4, 0xf7, 0x2, 0x86,
    0x69, 0xfd, 0x0, 0x3e, 0xff, 0xd8, 0x10,

    /* U+0068 "h" */
    0x5f, 0x30, 0x0, 0x0, 0x5, 0xf3, 0x0, 0x0,
    0x0, 0x5f, 0x30, 0x0, 0x0, 0x5, 0xf3, 0x0,
    0x0, 0x0, 0x5f, 0x49, 0xef, 0xd6, 0x5, 0xfd,
    0xb7, 0x7d, 0xf3, 0x5f, 0xa0, 0x0, 0x2f, 0x75,
    0xf3, 0x0, 0x0, 0xf8, 0x5f, 0x30, 0x0, 0xf,
    0x95, 0xf3, 0x0, 0x0, 0xf9, 0x5f, 0x30, 0x0,
    0xf, 0x95, 0xf3, 0x0, 0x0, 0xf9, 0x5f, 0x30,
    0x0, 0xf, 0x95, 0xf3, 0x0, 0x0, 0xf9,

    /* U+0069 "i" */
    0x6f, 0x35, 0xd3, 0x0, 0x0, 0x0, 0x5f, 0x35,
    0xf3, 0x5f, 0x35, 0xf3, 0x5f, 0x35, 0xf3, 0x5f,
    0x35, 0xf3, 0x5f, 0x35, 0xf3,

    /* U+006A "j" */
    0x0, 0x6, 0xf3, 0x0, 0x5, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf3, 0x0,
    0x5, 0xf3, 0x0, 0x5, 0xf3, 0x0, 0x5, 0xf3,
    0x0, 0x5, 0xf3, 0x0, 0x5, 0xf3, 0x0, 0x5,
    0xf3, 0x0, 0x5, 0xf3, 0x0, 0x5, 0xf3, 0x0,
    0x5, 0xf3, 0x0, 0x6, 0xf2, 0x0, 0x7, 0xf1,
    0x15, 0x6e, 0xc0, 0x3f, 0xec, 0x20,

    /* U+006B "k" */
    0x5f, 0x30, 0x0, 0x0, 0x5, 0xf3, 0x0, 0x0,
    0x0, 0x5f, 0x30, 0x0, 0x0, 0x5, 0xf3, 0x0,
    0x0, 0x0, 0x5f, 0x30, 0x5, 0xf9, 0x5, 0xf3,
    0x3, 0xfa, 0x0, 0x5f, 0x31, 0xec, 0x0, 0x5,
    0xf3, 0xcd, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0,
    0x5, 0xf6, 0xfb, 0x0, 0x0, 0x5f, 0x35, 0xf7,
    0x0, 0x5, 0xf3, 0x9, 0xf4, 0x0, 0x5f, 0x30,
    0xd, 0xe2, 0x5, 0xf3, 0x0, 0x2f, 0xd0,

    /* U+006C "l" */
    0x5f, 0x30, 0x5f, 0x30, 0x5f, 0x30, 0x5f, 0x30,
    0x5f, 0x30, 0x5f, 0x30, 0x5f, 0x30, 0x5f, 0x30,
    0x5f, 0x30, 0x5f, 0x30, 0x5f, 0x30, 0x5f, 0x30,
    0x4f, 0x94, 0xa, 0xfc,

    /* U+006D "m" */
    0x5f, 0x4b, 0xff, 0xb1, 0x4c, 0xfe, 0x90, 0x5,
    0xfe, 0xa6, 0x9f, 0xcf, 0x97, 0xaf, 0x90, 0x5f,
    0x80, 0x0, 0xaf, 0x60, 0x0, 0xbe, 0x5, 0xf3,
    0x0, 0x7, 0xf1, 0x0, 0x8, 0xf0, 0x5f, 0x30,
    0x0, 0x7f, 0x10, 0x0, 0x8f, 0x5, 0xf3, 0x0,
    0x7, 0xf1, 0x0, 0x8, 0xf0, 0x5f, 0x30, 0x0,
    0x7f, 0x10, 0x0, 0x8f, 0x5, 0xf3, 0x0, 0x7,
    0xf1, 0x0, 0x8, 0xf0, 0x5f, 0x30, 0x0, 0x7f,
    0x10, 0x0, 0x8f, 0x5, 0xf3, 0x0, 0x7, 0xf1,
    0x0, 0x8, 0xf0,

    /* U+006E "n" */
    0x5f, 0x49, 0xef, 0xd6, 0x5, 0xfd, 0xb7, 0x7d,
    0xf3, 0x5f, 0xa0, 0x0, 0x3f, 0x75, 0xf3, 0x0,
    0x0, 0xf8, 0x5f, 0x30, 0x0, 0xf, 0x95, 0xf3,
    0x0, 0x0, 0xf9, 0x5f, 0x30, 0x0, 0xf, 0x95,
    0xf3, 0x0, 0x0, 0xf9, 0x5f, 0x30, 0x0, 0xf,
    0x95, 0xf3, 0x0, 0x0, 0xf9,

    /* U+006F "o" */
    0x2, 0xad, 0xfe, 0xb3, 0x0, 0x2f, 0xe7, 0x57,
    0xdf, 0x30, 0x9f, 0x20, 0x0, 0x1f, 0xa0, 0xdd,
    0x0, 0x0, 0xb, 0xe0, 0xeb, 0x0, 0x0, 0x9,
    0xf0, 0xeb, 0x0, 0x0, 0x9, 0xf0, 0xdd, 0x0,
    0x0, 0xb, 0xe0, 0x9f, 0x20, 0x0, 0xe, 0xb0,
    0x2f, 0xe7, 0x56, 0xdf, 0x30, 0x2, 0xad, 0xfe,
    0xb3, 0x0,

    /* U+0070 "p" */
    0x5f, 0x39, 0xef, 0xd8, 0x0, 0x5f, 0xcb, 0x65,
    0xaf, 0x90, 0x5f, 0xb0, 0x0, 0xb, 0xf0, 0x5f,
    0x40, 0x0, 0x6, 0xf3, 0x5f, 0x20, 0x0, 0x4,
    0xf4, 0x5f, 0x20, 0x0, 0x4, 0xf4, 0x5f, 0x40,
    0x0, 0x6, 0xf3, 0x5f, 0xb0, 0x0, 0xb, 0xf0,
    0x5f, 0xcb, 0x65, 0xbf, 0x80, 0x5f, 0x39, 0xef,
    0xd7, 0x0, 0x5f, 0x30, 0x0, 0x0, 0x0, 0x5f,
    0x30, 0x0, 0x0, 0x0, 0x5f, 0x30, 0x0, 0x0,
    0x0, 0x5f, 0x30, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x2, 0xbf, 0xfc, 0x4b, 0xd2, 0xfe, 0x75, 0x8e,
    0xdd, 0x9f, 0x30, 0x0, 0x4f, 0xdc, 0xd0, 0x0,
    0x0, 0xdd, 0xec, 0x0, 0x0, 0xb, 0xde, 0xc0,
    0x0, 0x0, 0xbd, 0xcd, 0x0, 0x0, 0xd, 0xd9,
    0xf3, 0x0, 0x4, 0xfd, 0x2f, 0xe7, 0x58, 0xec,
    0xd0, 0x3c, 0xff, 0xc3, 0xbd, 0x0, 0x0, 0x0,
    0xb, 0xd0, 0x0, 0x0, 0x0, 0xbd, 0x0, 0x0,
    0x0, 0xb, 0xd0, 0x0, 0x0, 0x0, 0xbd,

    /* U+0072 "r" */
    0x5f, 0x39, 0xe7, 0x5f, 0xca, 0x51, 0x5f, 0xa0,
    0x0, 0x5f, 0x30, 0x0, 0x5f, 0x30, 0x0, 0x5f,
    0x30, 0x0, 0x5f, 0x30, 0x0, 0x5f, 0x30, 0x0,
    0x5f, 0x30, 0x0, 0x5f, 0x30, 0x0,

    /* U+0073 "s" */
    0x7, 0xdf, 0xfd, 0x17, 0xf7, 0x45, 0x80, 0xbb,
    0x0, 0x0, 0xa, 0xe1, 0x0, 0x0, 0x2e, 0xfc,
    0x71, 0x0, 0x4, 0x9e, 0xe1, 0x0, 0x0, 0x1f,
    0x70, 0x0, 0x0, 0xe8, 0x75, 0x45, 0xaf, 0x3c,
    0xff, 0xfc, 0x50,

    /* U+0074 "t" */
    0x2, 0x82, 0x0, 0x4, 0xf4, 0x0, 0x4, 0xf4,
    0x0, 0xbf, 0xff, 0xfa, 0x38, 0xf7, 0x53, 0x4,
    0xf4, 0x0, 0x4, 0xf4, 0x0, 0x4, 0xf4, 0x0,
    0x4, 0xf4, 0x0, 0x4, 0xf4, 0x0, 0x4, 0xf4,
    0x0, 0x1, 0xfb, 0x54, 0x0, 0x6e, 0xfa,

    /* U+0075 "u" */
    0x7f, 0x10, 0x0, 0x2f, 0x67, 0xf1, 0x0, 0x2,
    0xf6, 0x7f, 0x10, 0x0, 0x2f, 0x67, 0xf1, 0x0,
    0x2, 0xf6, 0x7f, 0x10, 0x0, 0x2f, 0x67, 0xf1,
    0x0, 0x2, 0xf6, 0x6f, 0x10, 0x0, 0x2f, 0x65,
    0xf3, 0x0, 0x7, 0xf6, 0x1f, 0xc4, 0x38, 0xef,
    0x60, 0x4d, 0xff, 0xb2, 0xf6,

    /* U+0076 "v" */
    0xae, 0x0, 0x0, 0x8, 0xf1, 0x4f, 0x40, 0x0,
    0xd, 0xb0, 0xe, 0xa0, 0x0, 0x3f, 0x50, 0x8,
    0xf0, 0x0, 0x8f, 0x0, 0x2, 0xf5, 0x0, 0xda,
    0x0, 0x0, 0xda, 0x3, 0xf4, 0x0, 0x0, 0x7f,
    0x8, 0xe0, 0x0, 0x0, 0x1f, 0x6e, 0x80, 0x0,
    0x0, 0xb, 0xef, 0x30, 0x0, 0x0, 0x5, 0xfd,
    0x0, 0x0,

    /* U+0077 "w" */
    0x7f, 0x10, 0x0, 0xaf, 0x80, 0x0, 0x1f, 0x63,
    0xf4, 0x0, 0xe, 0xfc, 0x0, 0x5, 0xf1, 0xf,
    0x80, 0x3, 0xf7, 0xf1, 0x0, 0x9d, 0x0, 0xbc,
    0x0, 0x7d, 0xf, 0x60, 0xd, 0x90, 0x6, 0xf0,
    0xc, 0x90, 0xba, 0x1, 0xf5, 0x0, 0x2f, 0x41,
    0xf4, 0x6, 0xf0, 0x5f, 0x0, 0x0, 0xe8, 0x5f,
    0x0, 0x2f, 0x49, 0xc0, 0x0, 0xa, 0xca, 0xb0,
    0x0, 0xd9, 0xd8, 0x0, 0x0, 0x5f, 0xe6, 0x0,
    0x8, 0xef, 0x30, 0x0, 0x1, 0xff, 0x10, 0x0,
    0x3f, 0xf0, 0x0,

    /* U+0078 "x" */
    0x3f, 0x70, 0x0, 0x2f, 0x80, 0x9, 0xf2, 0x0,
    0xcd, 0x0, 0x0, 0xeb, 0x6, 0xf4, 0x0, 0x0,
    0x4f, 0x6e, 0x90, 0x0, 0x0, 0xa, 0xfe, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x20, 0x0, 0x0, 0x7f,
    0x4d, 0xc0, 0x0, 0x2, 0xf9, 0x4, 0xf6, 0x0,
    0xc, 0xe0, 0x0, 0xaf, 0x10, 0x6f, 0x40, 0x0,
    0x1f, 0xb0,

    /* U+0079 "y" */
    0xaf, 0x0, 0x0, 0x6, 0xf2, 0x3f, 0x60, 0x0,
    0xc, 0xc0, 0xd, 0xb0, 0x0, 0x1f, 0x60, 0x7,
    0xf1, 0x0, 0x6f, 0x10, 0x1, 0xf6, 0x0, 0xbb,
    0x0, 0x0, 0xbc, 0x1, 0xf5, 0x0, 0x0, 0x5f,
    0x26, 0xf0, 0x0, 0x0, 0xf, 0x7b, 0xa0, 0x0,
    0x0, 0x9, 0xdf, 0x40, 0x0, 0x0, 0x3, 0xfe,
    0x0, 0x0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0,
    0x8, 0xf2, 0x0, 0x0, 0x5, 0x9f, 0x70, 0x0,
    0x0, 0xc, 0xd7, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xc, 0xff, 0xff, 0xff, 0x20, 0x45, 0x55, 0x6f,
    0xd0, 0x0, 0x0, 0x9, 0xf3, 0x0, 0x0, 0x5,
    0xf6, 0x0, 0x0, 0x2, 0xfb, 0x0, 0x0, 0x0,
    0xce, 0x10, 0x0, 0x0, 0x8f, 0x30, 0x0, 0x0,
    0x4f, 0x70, 0x0, 0x0, 0x1e, 0xe6, 0x66, 0x66,
    0x23, 0xff, 0xff, 0xff, 0xf7,

    /* U+007B "{" */
    0x0, 0x1b, 0xb0, 0xa, 0xf6, 0x0, 0xea, 0x0,
    0xf, 0x90, 0x0, 0xf9, 0x0, 0xf, 0x90, 0x0,
    0xf9, 0x0, 0xf, 0x90, 0x4, 0xf7, 0x2, 0xfe,
    0x0, 0x7, 0xf6, 0x0, 0xf, 0x90, 0x0, 0xf9,
    0x0, 0xf, 0x90, 0x0, 0xf9, 0x0, 0xe, 0x90,
    0x0, 0xbe, 0x60, 0x2, 0xcb,

    /* U+007C "|" */
    0x1f, 0x61, 0xf6, 0x1f, 0x61, 0xf6, 0x1f, 0x61,
    0xf6, 0x1f, 0x61, 0xf6, 0x1f, 0x61, 0xf6, 0x1f,
    0x61, 0xf6, 0x1f, 0x61, 0xf6, 0x1f, 0x61, 0xf6,
    0x1f, 0x61, 0xf6,

    /* U+007D "}" */
    0x6d, 0x40, 0x2, 0xcf, 0x10, 0x4, 0xf4, 0x0,
    0x3f, 0x50, 0x3, 0xf5, 0x0, 0x3f, 0x50, 0x3,
    0xf5, 0x0, 0x3f, 0x50, 0x1, 0xf9, 0x10, 0x8,
    0xf8, 0x1, 0xfb, 0x20, 0x3f, 0x50, 0x3, 0xf5,
    0x0, 0x3f, 0x50, 0x3, 0xf5, 0x0, 0x3f, 0x40,
    0x2c, 0xf2, 0x6, 0xd6, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x90,
    0x0, 0xf5, 0x2f, 0x97, 0xfc, 0x37, 0xf2, 0x6f,
    0x0, 0x2d, 0xff, 0x90, 0x12, 0x0, 0x0, 0x33,
    0x0,

    /* U+4ED6 "他" */
    0x0, 0x0, 0x32, 0x0, 0x0, 0x93, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x90, 0x0, 0xe, 0x50, 0x0,
    0x0, 0x0, 0x3, 0xf4, 0x4c, 0x0, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x5, 0xe0, 0xe, 0x50,
    0x16, 0x30, 0x0, 0x1f, 0x70, 0x5e, 0x0, 0xea,
    0xcf, 0xf5, 0x0, 0x9, 0xf5, 0x5, 0xf7, 0xcf,
    0xe8, 0x3f, 0x40, 0x2, 0xff, 0x68, 0xdf, 0xe8,
    0xf5, 0x0, 0xf4, 0x0, 0xcc, 0xe6, 0xdb, 0xe0,
    0xe, 0x50, 0xf, 0x30, 0x6f, 0x2e, 0x50, 0x5e,
    0x0, 0xe5, 0x1, 0xf3, 0x0, 0x40, 0xe5, 0x5,
    0xe0, 0xe, 0x50, 0x3f, 0x20, 0x0, 0xe, 0x50,
    0x5e, 0x0, 0xe5, 0xcf, 0xe0, 0x0, 0x0, 0xe5,
    0x5, 0xe0, 0xe, 0x56, 0x61, 0x0, 0x0, 0xe,
    0x50, 0x5e, 0x0, 0xe5, 0x0, 0x7, 0x40, 0x0,
    0xe5, 0x5, 0xe0, 0x1, 0x0, 0x0, 0xc8, 0x0,
    0xe, 0x50, 0x4f, 0x73, 0x33, 0x34, 0x6f, 0x50,
    0x0, 0xe5, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0xe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5176 "其" */
    0x0, 0x0, 0x84, 0x0, 0x0, 0x0, 0x66, 0x0,
    0x0, 0x0, 0xd, 0x70, 0x0, 0x0, 0xa, 0xb0,
    0x0, 0x1, 0x44, 0xea, 0x44, 0x44, 0x44, 0xcc,
    0x44, 0x10, 0x5e, 0xef, 0xfe, 0xee, 0xee, 0xef,
    0xfe, 0xe3, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0,
    0xab, 0x0, 0x0, 0x0, 0xd, 0xa5, 0x55, 0x55,
    0x5c, 0xb0, 0x0, 0x0, 0x0, 0xde, 0xdd, 0xdd,
    0xdd, 0xfb, 0x0, 0x0, 0x0, 0xd, 0x70, 0x0,
    0x0, 0xa, 0xb0, 0x0, 0x0, 0x0, 0xde, 0xdd,
    0xdd, 0xdd, 0xfb, 0x0, 0x0, 0x0, 0xd, 0xa6,
    0x66, 0x66, 0x6c, 0xb0, 0x0, 0x0, 0x0, 0xd7,
    0x0, 0x0, 0x0, 0xab, 0x0, 0x1, 0xdd, 0xdf,
    0xed, 0xdd, 0xdd, 0xdf, 0xfd, 0xdc, 0x5, 0x55,
    0x55, 0x65, 0x55, 0x55, 0x55, 0x55, 0x50, 0x0,
    0x0, 0x6e, 0x30, 0x7, 0xd7, 0x10, 0x0, 0x0,
    0x6, 0xdf, 0x70, 0x0, 0x17, 0xef, 0x91, 0x0,
    0x6e, 0xf8, 0x10, 0x0, 0x0, 0x0, 0x5d, 0xf4,
    0x3, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x10,

    /* U+7528 "用" */
    0x0, 0x15, 0x55, 0x55, 0x55, 0x55, 0x55, 0x52,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x4f, 0x10, 0x0, 0xe7, 0x0, 0x0, 0xf6,
    0x0, 0x4f, 0x10, 0x0, 0xe7, 0x0, 0x0, 0xf6,
    0x0, 0x4f, 0x10, 0x0, 0xe7, 0x0, 0x0, 0xf6,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x4f, 0x55, 0x55, 0xf9, 0x55, 0x55, 0xf6,
    0x0, 0x4f, 0x10, 0x0, 0xe7, 0x0, 0x0, 0xf6,
    0x0, 0x5f, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xf6,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x7f, 0x66, 0x66, 0xfa, 0x66, 0x66, 0xf6,
    0x0, 0xbc, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xf6,
    0x0, 0xf7, 0x0, 0x0, 0xe7, 0x0, 0x0, 0xf6,
    0x7, 0xf1, 0x0, 0x0, 0xe7, 0x2, 0x58, 0xf5,
    0x1f, 0x80, 0x0, 0x0, 0xd6, 0x3, 0xff, 0xa0,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8F66 "车" */
    0x0, 0x0, 0x1, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x14, 0x44, 0xdc, 0x44, 0x44, 0x44, 0x44, 0x30,
    0x0, 0x4, 0xf4, 0x4, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xb0, 0x9, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x20, 0x9, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xee, 0xef, 0xfe, 0xee, 0xeb, 0x0,
    0x0, 0x66, 0x66, 0x6c, 0xe6, 0x66, 0x65, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x55, 0x55, 0x55, 0x5b, 0xe5, 0x55, 0x55, 0x52,
    0x0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x0,

    /* U+8F86 "辆" */
    0x0, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xf0, 0x3, 0x77, 0x77, 0x77, 0x77,
    0x75, 0x3, 0x7d, 0x33, 0x6b, 0xbe, 0xdb, 0xfc,
    0xbb, 0x82, 0xff, 0xff, 0xf4, 0x0, 0xc6, 0xe,
    0x40, 0x0, 0x0, 0xd5, 0x0, 0x0, 0xc, 0x60,
    0xe4, 0x0, 0x0, 0x2f, 0x67, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x6, 0xd7, 0xa0, 0x6d, 0x4d,
    0x84, 0xf6, 0x7e, 0x0, 0xb8, 0x7a, 0x6, 0xc0,
    0xd4, 0xf, 0x24, 0xe0, 0xe, 0xff, 0xff, 0x7c,
    0xf, 0x81, 0xf2, 0x4e, 0x0, 0x45, 0xac, 0x56,
    0xc1, 0xff, 0x6f, 0x94, 0xe0, 0x0, 0x7, 0xa0,
    0x6c, 0x4d, 0xbf, 0xdf, 0x7e, 0x0, 0x0, 0x7c,
    0x77, 0xc9, 0x92, 0xd7, 0x9e, 0xe0, 0x4b, 0xdf,
    0xfd, 0x9d, 0xe4, 0x2f, 0x22, 0xce, 0x5, 0xa7,
    0xaa, 0x6, 0xeb, 0x9, 0xb0, 0x4, 0xe0, 0x0,
    0x7, 0xa0, 0x6c, 0x0, 0x33, 0x0, 0x4e, 0x0,
    0x0, 0x7a, 0x6, 0xc0, 0x0, 0x0, 0x8d, 0xd0,
    0x0, 0x7, 0xa0, 0x4a, 0x0, 0x0, 0xb, 0xa3,
    0x0,

    /* U+901A "通" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x50, 0xb, 0xdd, 0xdd, 0xdd, 0xdd,
    0x40, 0x0, 0x8e, 0x0, 0x45, 0x95, 0x55, 0x7f,
    0xd2, 0x0, 0x0, 0xe7, 0x0, 0x2e, 0xc2, 0x6f,
    0xb1, 0x0, 0x0, 0x6, 0xb0, 0x0, 0x1b, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x5, 0x55, 0x0, 0xe6, 0x22,
    0xe6, 0x22, 0xd6, 0x2, 0xff, 0xf2, 0xe, 0x62,
    0x2e, 0x62, 0x2d, 0x60, 0x2, 0x4f, 0x20, 0xef,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x2, 0xf2, 0xe,
    0x50, 0xe, 0x50, 0xd, 0x60, 0x0, 0x2f, 0x20,
    0xef, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x2, 0xf2,
    0xe, 0x62, 0x2e, 0x62, 0x2d, 0x60, 0x0, 0x3f,
    0x20, 0xe5, 0x0, 0xe5, 0x3, 0xf6, 0x0, 0x2e,
    0xfc, 0x1e, 0x50, 0xc, 0x47, 0xfd, 0x10, 0x3f,
    0xd2, 0xce, 0xa6, 0x43, 0x22, 0x33, 0x33, 0x31,
    0xb1, 0x0, 0x6b, 0xef, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0,
    0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 74, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 104, .box_w = 3, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 20, .adv_w = 117, .box_w = 5, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 35, .adv_w = 173, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 107, .adv_w = 166, .box_w = 8, .box_h = 17, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 175, .adv_w = 236, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 260, .adv_w = 184, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 338, .adv_w = 73, .box_w = 3, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 347, .adv_w = 103, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 392, .adv_w = 103, .box_w = 6, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 446, .adv_w = 124, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 474, .adv_w = 159, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 519, .adv_w = 87, .box_w = 4, .box_h = 5, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 529, .adv_w = 127, .box_w = 6, .box_h = 3, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 538, .adv_w = 87, .box_w = 3, .box_h = 3, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 543, .adv_w = 144, .box_w = 7, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 592, .adv_w = 166, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 657, .adv_w = 166, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 696, .adv_w = 166, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 755, .adv_w = 166, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 807, .adv_w = 166, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 872, .adv_w = 166, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 931, .adv_w = 166, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 990, .adv_w = 166, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1055, .adv_w = 166, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1120, .adv_w = 166, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1179, .adv_w = 109, .box_w = 3, .box_h = 10, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1194, .adv_w = 109, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1220, .adv_w = 159, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1275, .adv_w = 159, .box_w = 10, .box_h = 6, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1305, .adv_w = 159, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1360, .adv_w = 124, .box_w = 8, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1412, .adv_w = 248, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1524, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1602, .adv_w = 179, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1661, .adv_w = 173, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1726, .adv_w = 201, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1798, .adv_w = 160, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1857, .adv_w = 152, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1909, .adv_w = 197, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1974, .adv_w = 204, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2046, .adv_w = 79, .box_w = 3, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2066, .adv_w = 78, .box_w = 6, .box_h = 17, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 2117, .adv_w = 177, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2182, .adv_w = 146, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2234, .adv_w = 239, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2319, .adv_w = 212, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2391, .adv_w = 214, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2469, .adv_w = 172, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2528, .adv_w = 214, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2624, .adv_w = 177, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2689, .adv_w = 163, .box_w = 9, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2748, .adv_w = 152, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2813, .adv_w = 202, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2885, .adv_w = 178, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2957, .adv_w = 259, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3061, .adv_w = 180, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3133, .adv_w = 171, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3205, .adv_w = 180, .box_w = 10, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3270, .adv_w = 85, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3306, .adv_w = 147, .box_w = 9, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3378, .adv_w = 85, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3423, .adv_w = 159, .box_w = 8, .box_h = 7, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 3451, .adv_w = 144, .box_w = 9, .box_h = 3, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3465, .adv_w = 100, .box_w = 4, .box_h = 3, .ofs_x = 1, .ofs_y = 11},
    {.bitmap_index = 3471, .adv_w = 176, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3521, .adv_w = 183, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3591, .adv_w = 143, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3631, .adv_w = 183, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3694, .adv_w = 166, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3739, .adv_w = 101, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3788, .adv_w = 183, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3851, .adv_w = 178, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3914, .adv_w = 77, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3935, .adv_w = 77, .box_w = 6, .box_h = 18, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 3989, .adv_w = 149, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4052, .adv_w = 82, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4080, .adv_w = 265, .box_w = 15, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4155, .adv_w = 178, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4200, .adv_w = 178, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4250, .adv_w = 183, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4320, .adv_w = 183, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4383, .adv_w = 109, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4413, .adv_w = 138, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4448, .adv_w = 97, .box_w = 6, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4487, .adv_w = 177, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4532, .adv_w = 151, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4582, .adv_w = 238, .box_w = 15, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4657, .adv_w = 149, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4707, .adv_w = 152, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4777, .adv_w = 146, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4822, .adv_w = 86, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4867, .adv_w = 53, .box_w = 3, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4894, .adv_w = 86, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4939, .adv_w = 159, .box_w = 10, .box_h = 5, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 4964, .adv_w = 283, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5109, .adv_w = 283, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5254, .adv_w = 283, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5382, .adv_w = 283, .box_w = 16, .box_h = 17, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5518, .adv_w = 283, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5663, .adv_w = 283, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x2a0, 0x2652, 0x4090, 0x40b0, 0x4144
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20182, .range_length = 16709, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 6, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    3, 3,
    3, 8,
    3, 13,
    3, 15,
    8, 3,
    8, 8,
    8, 13,
    8, 15,
    9, 75,
    13, 3,
    13, 8,
    13, 18,
    13, 24,
    13, 26,
    15, 3,
    15, 8,
    15, 18,
    15, 24,
    15, 26,
    16, 16,
    18, 13,
    18, 15,
    18, 27,
    18, 28,
    24, 13,
    24, 15,
    24, 27,
    24, 28,
    27, 18,
    27, 24,
    28, 18,
    28, 24,
    34, 3,
    34, 8,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 71,
    34, 77,
    34, 85,
    34, 87,
    34, 90,
    35, 36,
    35, 40,
    35, 48,
    35, 50,
    35, 53,
    35, 55,
    35, 57,
    35, 58,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 56,
    37, 57,
    37, 58,
    37, 59,
    37, 66,
    38, 75,
    39, 13,
    39, 15,
    39, 34,
    39, 66,
    39, 73,
    39, 76,
    40, 55,
    40, 58,
    43, 43,
    44, 34,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 53,
    44, 54,
    44, 55,
    44, 56,
    44, 58,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 77,
    44, 80,
    44, 82,
    44, 84,
    44, 85,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 87,
    45, 88,
    45, 90,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 56,
    48, 57,
    48, 58,
    48, 59,
    48, 66,
    49, 13,
    49, 15,
    49, 34,
    49, 53,
    49, 55,
    49, 56,
    49, 57,
    49, 58,
    49, 59,
    49, 66,
    50, 13,
    50, 15,
    50, 34,
    50, 43,
    50, 53,
    50, 55,
    50, 56,
    50, 57,
    50, 58,
    50, 59,
    50, 66,
    51, 36,
    51, 40,
    51, 48,
    51, 50,
    51, 53,
    51, 54,
    51, 55,
    51, 56,
    51, 57,
    51, 58,
    51, 68,
    51, 69,
    51, 70,
    51, 72,
    51, 80,
    51, 82,
    51, 85,
    51, 86,
    51, 87,
    51, 89,
    51, 90,
    52, 13,
    52, 15,
    52, 52,
    52, 53,
    52, 55,
    52, 56,
    52, 58,
    52, 85,
    52, 87,
    52, 88,
    52, 89,
    52, 90,
    53, 3,
    53, 8,
    53, 13,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 48,
    53, 50,
    53, 52,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 74,
    53, 75,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 85,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    54, 57,
    54, 89,
    55, 13,
    55, 15,
    55, 27,
    55, 28,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 52,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 73,
    55, 76,
    55, 80,
    55, 82,
    55, 84,
    55, 87,
    55, 90,
    56, 13,
    56, 15,
    56, 27,
    56, 28,
    56, 34,
    56, 36,
    56, 40,
    56, 48,
    56, 50,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 84,
    57, 34,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 53,
    57, 54,
    57, 55,
    57, 56,
    57, 58,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 77,
    57, 80,
    57, 82,
    57, 84,
    57, 85,
    57, 86,
    57, 87,
    57, 88,
    57, 90,
    58, 3,
    58, 8,
    58, 13,
    58, 15,
    58, 27,
    58, 28,
    58, 34,
    58, 36,
    58, 40,
    58, 48,
    58, 50,
    58, 52,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 73,
    58, 74,
    58, 76,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    60, 75,
    66, 53,
    66, 55,
    66, 58,
    66, 87,
    66, 88,
    66, 90,
    67, 13,
    67, 15,
    67, 53,
    67, 55,
    67, 56,
    67, 57,
    67, 58,
    67, 87,
    67, 89,
    67, 90,
    70, 13,
    70, 15,
    70, 53,
    70, 58,
    70, 75,
    71, 3,
    71, 8,
    71, 10,
    71, 11,
    71, 13,
    71, 15,
    71, 32,
    71, 53,
    71, 55,
    71, 56,
    71, 57,
    71, 58,
    71, 62,
    71, 66,
    71, 68,
    71, 69,
    71, 70,
    71, 71,
    71, 72,
    71, 73,
    71, 74,
    71, 75,
    71, 76,
    71, 77,
    71, 80,
    71, 82,
    71, 84,
    71, 85,
    71, 94,
    73, 53,
    73, 58,
    73, 87,
    73, 90,
    74, 3,
    74, 8,
    74, 10,
    74, 32,
    74, 53,
    74, 62,
    74, 94,
    75, 53,
    76, 53,
    76, 54,
    76, 56,
    76, 66,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 77,
    76, 80,
    76, 82,
    76, 86,
    77, 71,
    77, 87,
    77, 88,
    77, 90,
    78, 53,
    78, 58,
    78, 87,
    78, 90,
    79, 53,
    79, 58,
    79, 87,
    79, 90,
    80, 13,
    80, 15,
    80, 53,
    80, 55,
    80, 56,
    80, 57,
    80, 58,
    80, 87,
    80, 89,
    80, 90,
    81, 13,
    81, 15,
    81, 53,
    81, 55,
    81, 56,
    81, 57,
    81, 58,
    81, 87,
    81, 89,
    81, 90,
    82, 53,
    82, 58,
    82, 75,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 80,
    83, 82,
    83, 85,
    84, 53,
    84, 55,
    84, 56,
    84, 57,
    84, 58,
    84, 87,
    84, 89,
    84, 90,
    85, 71,
    86, 53,
    86, 58,
    87, 13,
    87, 15,
    87, 53,
    87, 57,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    88, 53,
    88, 57,
    88, 66,
    89, 53,
    89, 54,
    89, 56,
    89, 66,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 77,
    89, 80,
    89, 82,
    89, 86,
    90, 13,
    90, 15,
    90, 53,
    90, 57,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 80,
    90, 82,
    91, 53,
    92, 73,
    92, 75,
    92, 76
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -16, -16, -52, -52, -16, -16, -52, -52,
    17, -46, -46, -40, -12, -11, -46, -46,
    -40, -12, -11, -35, -29, -29, -29, -29,
    -46, -46, -29, -29, -23, -17, -23, -17,
    -29, -29, -6, -6, -6, -6, -23, -3,
    -20, -11, -23, -6, -3, -6, -6, -6,
    -3, -3, -3, -3, -12, -6, -6, -6,
    -17, -17, -6, -10, -9, -5, -6, -12,
    -6, -11, 5, -46, -46, -12, -17, 0,
    0, -3, -6, 2, -5, -6, -6, -6,
    -6, -5, -11, -11, -11, -12, -11, -11,
    -11, -11, -11, -11, -11, -5, -3, -12,
    -17, -12, -17, -29, -29, 6, -17, -17,
    -17, -17, -23, -5, -23, -11, -29, -12,
    -6, -12, -17, -17, -6, -10, -9, -5,
    -6, -12, -6, -11, -57, -57, -17, -8,
    -3, -3, -14, -6, -11, -17, -17, -17,
    -6, 2, -10, -9, -5, -6, -12, -6,
    -11, -4, -4, -4, -4, -12, -3, -6,
    -3, -8, -12, -6, -6, -6, -6, -6,
    -6, -6, -3, -3, 6, -3, -6, -6,
    0, 0, -6, -3, -6, 0, -6, -3,
    0, -6, 1, 1, -44, -44, -23, -6,
    -6, -6, -6, 0, -23, -17, -17, -17,
    -17, -6, -5, -17, -17, -17, -17, -17,
    -17, -17, 6, -23, -11, -5, -11, -11,
    -17, -3, -5, 0, -40, -40, -12, -12,
    -20, -9, -9, -9, -9, -3, -12, -6,
    -6, -6, -6, 0, 0, -6, -6, -6,
    0, 0, -23, -23, -11, -11, -12, -5,
    -5, -5, -5, -12, -6, -6, -6, -6,
    -6, -6, -6, -5, -6, -6, -6, -6,
    -5, -11, -11, -11, -12, -11, -11, -11,
    -11, -11, -11, -11, -5, -3, -12, -17,
    -12, -17, 0, 0, -41, -41, -17, -17,
    -23, -12, -12, -12, -12, 0, -29, -17,
    -17, -17, 0, -17, 0, -5, 0, -12,
    -12, -17, -12, -17, -12, -18, 0, -12,
    -12, -12, -12, -12, -6, -6, -6, -6,
    23, -17, -6, -18, -3, -3, -3, -12,
    -12, -17, -6, -6, -11, -17, -3, -6,
    -3, -6, -6, -17, -12, 6, 1, 1,
    0, 18, 0, 0, 12, 12, 6, 6,
    6, 0, 6, -8, -5, -5, -5, 0,
    -11, -5, -5, -5, -5, -5, -5, -5,
    -5, 0, 1, -17, -11, -3, -3, 1,
    1, 1, 1, -6, 12, 6, -6, -11,
    0, -6, -6, -6, -6, -6, -6, -6,
    -6, -6, -3, -6, -5, -3, -5, -17,
    -11, -3, -3, -17, -11, -3, -3, -12,
    -12, -17, -6, -6, -11, -17, -3, -6,
    -3, -12, -12, -17, -6, -6, -11, -17,
    -3, -6, -3, -5, -5, 6, 1, 1,
    -26, -26, -14, -5, -5, -5, 0, -5,
    -5, 6, -17, -6, -6, -5, -17, -5,
    -3, -5, 0, -12, -5, -26, -26, -11,
    -11, -10, -3, -3, -3, -3, -3, -14,
    -14, -5, -11, -9, -11, 0, -6, -6,
    -6, -6, -6, -6, -6, -6, -6, -3,
    -26, -26, -11, -11, -10, -3, -3, -3,
    -3, -3, -17, 1, 23, 1
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 486,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_AlibabaPuHui18 = {
#else
lv_font_t ui_font_AlibabaPuHui18 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 19,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_ALIBABAPUHUI18*/

