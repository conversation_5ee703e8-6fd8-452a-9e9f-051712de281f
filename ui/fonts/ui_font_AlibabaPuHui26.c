/*******************************************************************************
 * Size: 26 px
 * Bpp: 4
 * Opts: --bpp 4 --size 26 --font E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts/AlibabaPuHuiTi-3-55-Regular.ttf -o E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts\ui_font_AlibabaPuHui26.c --format lvgl -r 0x20-0x7f --symbols 设置 --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_ALIBABAPUHUI26
#define UI_FONT_ALIBABAPUHUI26 1
#endif

#if UI_FONT_ALIBABAPUHUI26

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x9, 0xfe, 0x0, 0x9f, 0xe0, 0x8, 0xfd, 0x0,
    0x8f, 0xd0, 0x7, 0xfc, 0x0, 0x7f, 0xc0, 0x6,
    0xfb, 0x0, 0x6f, 0xb0, 0x5, 0xfa, 0x0, 0x5f,
    0xa0, 0x4, 0xf9, 0x0, 0x4f, 0x90, 0x2, 0x84,
    0x0, 0x0, 0x0, 0x0, 0x31, 0x0, 0xdf, 0xf2,
    0xf, 0xff, 0x40, 0xbf, 0xd1,

    /* U+0022 "\"" */
    0xff, 0x90, 0xff, 0x7e, 0xf7, 0xf, 0xf6, 0xcf,
    0x60, 0xef, 0x5b, 0xf5, 0xc, 0xf4, 0xaf, 0x30,
    0xbf, 0x29, 0xf2, 0xa, 0xf1, 0x7f, 0x10, 0x8f,
    0x3, 0x80, 0x4, 0x80,

    /* U+0023 "#" */
    0x0, 0x0, 0x9, 0xf8, 0x0, 0x4, 0xfc, 0x0,
    0x0, 0x0, 0xc, 0xf5, 0x0, 0x8, 0xf9, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x0, 0xb, 0xf6, 0x0,
    0x0, 0x0, 0x2f, 0xe0, 0x0, 0xe, 0xf3, 0x0,
    0x0, 0x0, 0x5f, 0xb0, 0x0, 0x1f, 0xf0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x6c, 0xcc, 0xff, 0xdc, 0xcc, 0xef, 0xec, 0xc1,
    0x0, 0x0, 0xff, 0x10, 0x0, 0xbf, 0x50, 0x0,
    0x0, 0x4, 0xfd, 0x0, 0x0, 0xff, 0x20, 0x0,
    0x0, 0x7, 0xfa, 0x0, 0x2, 0xfe, 0x0, 0x0,
    0x0, 0xb, 0xf6, 0x0, 0x6, 0xfb, 0x0, 0x0,
    0x6c, 0xcf, 0xfd, 0xcc, 0xce, 0xfe, 0xcc, 0xc1,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x5f, 0xc0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x8f, 0x80, 0x0, 0x4f, 0xd0, 0x0, 0x0,
    0x0, 0xcf, 0x50, 0x0, 0x7f, 0xa0, 0x0, 0x0,
    0x0, 0xff, 0x20, 0x0, 0xaf, 0x60, 0x0, 0x0,
    0x2, 0xfe, 0x0, 0x0, 0xef, 0x30, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x9, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf1, 0x0, 0x0, 0x0, 0x3b, 0xef, 0xff,
    0xec, 0x90, 0x3, 0xff, 0xee, 0xfd, 0xef, 0xf1,
    0xb, 0xf8, 0x9, 0xf1, 0x0, 0x30, 0xf, 0xf1,
    0x9, 0xf1, 0x0, 0x0, 0x1f, 0xf0, 0x9, 0xf1,
    0x0, 0x0, 0xf, 0xf1, 0x9, 0xf1, 0x0, 0x0,
    0xd, 0xfa, 0x9, 0xf1, 0x0, 0x0, 0x5, 0xff,
    0xed, 0xf1, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xff,
    0xb5, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x9, 0xf2, 0x7f, 0xf7, 0x0, 0x0,
    0x9, 0xf1, 0x7, 0xfc, 0x0, 0x0, 0x9, 0xf1,
    0x2, 0xfe, 0x0, 0x0, 0x9, 0xf1, 0x1, 0xfe,
    0x0, 0x0, 0x9, 0xf1, 0x5, 0xfc, 0x35, 0x20,
    0x9, 0xf1, 0x4e, 0xf7, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x2a, 0xce, 0xff, 0xff, 0xc6, 0x0,
    0x0, 0x0, 0x9, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x9d, 0xfd, 0x80, 0x0, 0x0, 0x7, 0xf4,
    0x0, 0x0, 0x9f, 0xa6, 0xbf, 0x70, 0x0, 0x1,
    0xfb, 0x0, 0x0, 0xf, 0xc0, 0x0, 0xde, 0x0,
    0x0, 0x9f, 0x20, 0x0, 0x2, 0xf9, 0x0, 0x9,
    0xf1, 0x0, 0x2f, 0x90, 0x0, 0x0, 0x3f, 0x80,
    0x0, 0x9f, 0x20, 0xb, 0xf1, 0x0, 0x0, 0x3,
    0xf8, 0x0, 0x9, 0xf1, 0x4, 0xf7, 0x0, 0x0,
    0x0, 0xf, 0xb0, 0x0, 0xcf, 0x0, 0xde, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x51, 0x5f, 0x90, 0x6f,
    0x50, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xc1,
    0xe, 0xc0, 0x4d, 0xff, 0xb2, 0x0, 0x0, 0x13,
    0x10, 0x8, 0xf3, 0x2f, 0xc5, 0x5e, 0xd0, 0x0,
    0x0, 0x0, 0x1, 0xfa, 0x8, 0xf3, 0x0, 0x6f,
    0x40, 0x0, 0x0, 0x0, 0xaf, 0x20, 0xbf, 0x0,
    0x3, 0xf7, 0x0, 0x0, 0x0, 0x3f, 0x80, 0xc,
    0xf0, 0x0, 0x2f, 0x80, 0x0, 0x0, 0xb, 0xe0,
    0x0, 0xcf, 0x0, 0x2, 0xf8, 0x0, 0x0, 0x4,
    0xf6, 0x0, 0xb, 0xf0, 0x0, 0x3f, 0x70, 0x0,
    0x0, 0xdd, 0x0, 0x0, 0x8f, 0x30, 0x6, 0xf4,
    0x0, 0x0, 0x6f, 0x40, 0x0, 0x2, 0xfd, 0x67,
    0xfd, 0x0, 0x0, 0x1e, 0xb0, 0x0, 0x0, 0x4,
    0xcf, 0xeb, 0x20,

    /* U+0026 "&" */
    0x0, 0x0, 0x3b, 0xff, 0xc5, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xe, 0xfa, 0x0, 0x9f, 0xf0, 0x0, 0x0,
    0x0, 0x4f, 0xf1, 0x0, 0x2f, 0xf2, 0x0, 0x0,
    0x0, 0x4f, 0xf1, 0x0, 0x2f, 0xf1, 0x0, 0x0,
    0x0, 0x3f, 0xf4, 0x0, 0xaf, 0xc0, 0x0, 0x0,
    0x0, 0xd, 0xfb, 0x3c, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xff, 0xf8, 0x0, 0x0, 0x13, 0x20,
    0x4, 0xff, 0xec, 0xfe, 0x10, 0x0, 0x8f, 0xa0,
    0x2f, 0xfb, 0x1, 0xdf, 0xc0, 0x0, 0xcf, 0x70,
    0x9f, 0xd0, 0x0, 0x2f, 0xfa, 0x2, 0xff, 0x20,
    0xdf, 0x70, 0x0, 0x5, 0xff, 0x7a, 0xfc, 0x0,
    0xff, 0x70, 0x0, 0x0, 0x8f, 0xff, 0xf3, 0x0,
    0xcf, 0xc0, 0x0, 0x0, 0xb, 0xff, 0x90, 0x0,
    0x6f, 0xfa, 0x20, 0x2, 0x9f, 0xff, 0xf6, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0x7d, 0xff, 0xf9,
    0x0, 0x4b, 0xef, 0xec, 0x71, 0x1, 0x8d, 0xfa,

    /* U+0027 "'" */
    0xff, 0x9e, 0xf7, 0xcf, 0x6b, 0xf5, 0xaf, 0x39,
    0xf2, 0x7f, 0x13, 0x80,

    /* U+0028 "(" */
    0x0, 0x0, 0x7f, 0xb0, 0x0, 0x1, 0xff, 0x10,
    0x0, 0xa, 0xf8, 0x0, 0x0, 0x3f, 0xe0, 0x0,
    0x0, 0xaf, 0x80, 0x0, 0x1, 0xff, 0x10, 0x0,
    0x7, 0xfc, 0x0, 0x0, 0xb, 0xf8, 0x0, 0x0,
    0xf, 0xf4, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0,
    0x3f, 0xf0, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0,
    0x4f, 0xf0, 0x0, 0x0, 0x4f, 0xf0, 0x0, 0x0,
    0x2f, 0xf1, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x0,
    0xc, 0xf8, 0x0, 0x0, 0x8, 0xfc, 0x0, 0x0,
    0x2, 0xff, 0x10, 0x0, 0x0, 0xbf, 0x70, 0x0,
    0x0, 0x4f, 0xe0, 0x0, 0x0, 0xc, 0xf6, 0x0,
    0x0, 0x2, 0xfe, 0x10, 0x0, 0x0, 0x8f, 0x90,

    /* U+0029 ")" */
    0x4f, 0xd0, 0x0, 0x0, 0xaf, 0x70, 0x0, 0x2,
    0xff, 0x20, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x2f,
    0xf1, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x7, 0xfd,
    0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0xff, 0x50,
    0x0, 0xd, 0xf7, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0xa, 0xf9, 0x0, 0x0, 0xaf, 0x90, 0x0, 0xb,
    0xf8, 0x0, 0x0, 0xdf, 0x70, 0x0, 0xf, 0xf4,
    0x0, 0x3, 0xff, 0x10, 0x0, 0x7f, 0xc0, 0x0,
    0xd, 0xf6, 0x0, 0x3, 0xfe, 0x0, 0x0, 0xaf,
    0x80, 0x0, 0x3f, 0xe1, 0x0, 0xc, 0xf6, 0x0,
    0x6, 0xfc, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x7f, 0xa0, 0x0, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0x0, 0x8, 0x71, 0x5f, 0x80, 0x6a,
    0x0, 0xff, 0xfc, 0xfc, 0xef, 0xf2, 0x6, 0x9d,
    0xff, 0xfe, 0xa6, 0x10, 0x0, 0x3f, 0xff, 0x50,
    0x0, 0x0, 0x1e, 0xf5, 0xff, 0x30, 0x0, 0xc,
    0xfa, 0x7, 0xfe, 0x0, 0x0, 0x2b, 0x10, 0xa,
    0x30, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x40, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x2, 0x22, 0x22, 0xff, 0x62, 0x22, 0x21,
    0x0, 0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x44,
    0x10, 0x0, 0x0,

    /* U+002C "," */
    0x0, 0x4, 0x84, 0x0, 0xb, 0xf7, 0x0, 0xe,
    0xf3, 0x0, 0x3f, 0xd0, 0x0, 0xaf, 0x60, 0x2,
    0xfd, 0x0, 0xb, 0xf3, 0x0,

    /* U+002D "-" */
    0x1d, 0xdd, 0xdd, 0xdd, 0x72, 0xff, 0xff, 0xff,
    0xf8,

    /* U+002E "." */
    0x2, 0x20, 0x9f, 0xf6, 0xbf, 0xf8, 0x6f, 0xe4,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x8,
    0xf7, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x4f, 0xb0, 0x0, 0x0, 0xa, 0xf5, 0x0, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0xc, 0xf3, 0x0, 0x0, 0x2, 0xfd, 0x0,
    0x0, 0x0, 0x8f, 0x70, 0x0, 0x0, 0xd, 0xf2,
    0x0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x0, 0x9f,
    0x60, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x5,
    0xfa, 0x0, 0x0, 0x0, 0xbf, 0x40, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x0, 0x7, 0xf8, 0x0, 0x0,
    0x0, 0xdf, 0x20, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x5, 0xbe, 0xfe, 0xb5, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x5, 0xff, 0x71,
    0x1, 0x8f, 0xf4, 0x0, 0xdf, 0xa0, 0x0, 0x0,
    0xaf, 0xc0, 0x2f, 0xf3, 0x0, 0x0, 0x4, 0xff,
    0x15, 0xff, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x7f,
    0xe0, 0x0, 0x0, 0x0, 0xef, 0x78, 0xfc, 0x0,
    0x0, 0x0, 0xd, 0xf8, 0x9f, 0xc0, 0x0, 0x0,
    0x0, 0xdf, 0x89, 0xfc, 0x0, 0x0, 0x0, 0xc,
    0xf8, 0x8f, 0xc0, 0x0, 0x0, 0x0, 0xdf, 0x87,
    0xfe, 0x0, 0x0, 0x0, 0xe, 0xf7, 0x5f, 0xf0,
    0x0, 0x0, 0x0, 0xff, 0x42, 0xff, 0x30, 0x0,
    0x0, 0x4f, 0xf1, 0xd, 0xf9, 0x0, 0x0, 0xa,
    0xfc, 0x0, 0x5f, 0xf7, 0x0, 0x7, 0xff, 0x40,
    0x0, 0xaf, 0xff, 0xef, 0xff, 0x90, 0x0, 0x0,
    0x5b, 0xef, 0xeb, 0x50, 0x0,

    /* U+0031 "1" */
    0x1, 0x47, 0xbe, 0xf7, 0x5f, 0xff, 0xff, 0xf7,
    0x5d, 0xa6, 0x3e, 0xf7, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0xe, 0xf7,
    0x0, 0x0, 0xe, 0xf7, 0x0, 0x0, 0xe, 0xf7,

    /* U+0032 "2" */
    0x2, 0xbd, 0xef, 0xfd, 0x92, 0x0, 0x0, 0x4f,
    0xff, 0xef, 0xff, 0xf5, 0x0, 0x1, 0x30, 0x0,
    0x4, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x70, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xe3, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xd2,
    0x0, 0x0, 0x0, 0x3, 0xef, 0xb0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x58, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5,

    /* U+0033 "3" */
    0x2, 0xbd, 0xef, 0xfc, 0x81, 0x0, 0x5, 0xff,
    0xfe, 0xff, 0xff, 0x40, 0x1, 0x30, 0x0, 0x3,
    0xcf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xe0, 0x0, 0x0, 0x1, 0x39, 0xff, 0x40,
    0x0, 0xf, 0xff, 0xff, 0xa2, 0x0, 0x0, 0xc,
    0xcc, 0xef, 0xfb, 0x20, 0x0, 0x0, 0x0, 0x2,
    0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf9, 0x3, 0x0, 0x0, 0x4, 0xcf, 0xf2,
    0x3f, 0xff, 0xef, 0xff, 0xff, 0x50, 0x2c, 0xef,
    0xff, 0xec, 0x81, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xfd, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x99, 0xfa, 0x0, 0x0, 0x0, 0x4, 0xfd,
    0x9, 0xfa, 0x0, 0x0, 0x0, 0x1e, 0xf4, 0x9,
    0xfa, 0x0, 0x0, 0x0, 0xaf, 0x90, 0x9, 0xfa,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x9, 0xfa, 0x0,
    0x0, 0x1f, 0xf3, 0x0, 0x9, 0xfa, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0x9, 0xfa, 0x0, 0x7, 0xfd,
    0x0, 0x0, 0x9, 0xfa, 0x0, 0xf, 0xf3, 0x0,
    0x0, 0x9, 0xfa, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x1e, 0xee, 0xee, 0xee, 0xef,
    0xff, 0xeb, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfa, 0x0,

    /* U+0035 "5" */
    0x8, 0xff, 0xff, 0xff, 0xff, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0xd, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xda, 0x40, 0x0,
    0xaf, 0xdc, 0xcd, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x17, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xc0, 0x31, 0x0, 0x0, 0x3b, 0xff, 0x40,
    0xcf, 0xfe, 0xff, 0xff, 0xf6, 0x0, 0x8d, 0xff,
    0xfe, 0xc7, 0x10, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x1, 0x6b, 0xdf, 0x20, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xf1, 0x0, 0x0, 0xa, 0xff,
    0xa4, 0x10, 0x0, 0x0, 0x8, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x80, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf2, 0x39, 0xbb, 0x93, 0x0, 0x2, 0xff, 0xbf,
    0xff, 0xff, 0xfa, 0x0, 0x5f, 0xff, 0x72, 0x2,
    0x7f, 0xf8, 0x6, 0xff, 0x50, 0x0, 0x0, 0x6f,
    0xf0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0xff, 0x37,
    0xfb, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x6f, 0xc0,
    0x0, 0x0, 0x0, 0xdf, 0x43, 0xff, 0x0, 0x0,
    0x0, 0xf, 0xf2, 0xe, 0xf5, 0x0, 0x0, 0x6,
    0xfe, 0x0, 0x7f, 0xf5, 0x0, 0x6, 0xff, 0x70,
    0x0, 0xbf, 0xff, 0xef, 0xff, 0xa0, 0x0, 0x0,
    0x5b, 0xef, 0xeb, 0x50, 0x0,

    /* U+0037 "7" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x59, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x7, 0xce, 0xfe, 0xc6, 0x0, 0x0, 0x1d,
    0xff, 0xfd, 0xff, 0xfc, 0x0, 0x9, 0xfe, 0x40,
    0x0, 0x5e, 0xf8, 0x0, 0xff, 0x40, 0x0, 0x0,
    0x6f, 0xe0, 0x1f, 0xf0, 0x0, 0x0, 0x2, 0xff,
    0x0, 0xff, 0x10, 0x0, 0x0, 0x2f, 0xf0, 0xc,
    0xf6, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x3f, 0xf6,
    0x10, 0x17, 0xff, 0x30, 0x0, 0x2d, 0xff, 0xff,
    0xfc, 0x20, 0x0, 0x1b, 0xfe, 0xba, 0xcf, 0xfb,
    0x10, 0xd, 0xf8, 0x0, 0x0, 0x9, 0xfd, 0x6,
    0xfc, 0x0, 0x0, 0x0, 0xd, 0xf5, 0xaf, 0x90,
    0x0, 0x0, 0x0, 0xaf, 0x9a, 0xf9, 0x0, 0x0,
    0x0, 0x9, 0xf9, 0x8f, 0xd0, 0x0, 0x0, 0x0,
    0xdf, 0x72, 0xff, 0xa2, 0x0, 0x2, 0xbf, 0xf2,
    0x6, 0xff, 0xfe, 0xde, 0xff, 0xf6, 0x0, 0x2,
    0x9d, 0xff, 0xfd, 0x92, 0x0,

    /* U+0039 "9" */
    0x0, 0x5, 0xbe, 0xfe, 0xb4, 0x0, 0x0, 0xb,
    0xff, 0xfe, 0xff, 0xf8, 0x0, 0x9, 0xfe, 0x40,
    0x0, 0x7f, 0xf4, 0x1, 0xff, 0x50, 0x0, 0x0,
    0x8f, 0xc0, 0x5f, 0xe0, 0x0, 0x0, 0x1, 0xff,
    0x7, 0xfc, 0x0, 0x0, 0x0, 0xe, 0xf3, 0x7f,
    0xb0, 0x0, 0x0, 0x0, 0xdf, 0x46, 0xfd, 0x0,
    0x0, 0x0, 0xf, 0xf5, 0x2f, 0xf3, 0x0, 0x0,
    0x7, 0xff, 0x40, 0xaf, 0xe6, 0x10, 0x28, 0xff,
    0xf2, 0x1, 0xcf, 0xff, 0xff, 0xfa, 0xff, 0x0,
    0x0, 0x49, 0xbb, 0x83, 0x4f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x60, 0x0, 0x0, 0x1, 0x4b, 0xff, 0x80, 0x0,
    0x0, 0x3f, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x4,
    0xed, 0xa6, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x6f, 0xe4, 0xbf, 0xf8, 0x9f, 0xf6, 0x2, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x9f, 0xf6,
    0xbf, 0xf8, 0x6f, 0xe4,

    /* U+003B ";" */
    0x0, 0x6f, 0xe4, 0x0, 0xbf, 0xf8, 0x0, 0x9f,
    0xf6, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x61, 0x0, 0xf, 0xf3, 0x0, 0x2f, 0xe0, 0x0,
    0x7f, 0x90, 0x0, 0xdf, 0x20, 0x4, 0xfa, 0x0,
    0xd, 0xe1, 0x0, 0x3, 0x20, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xc6, 0x0, 0x0,
    0x0, 0x0, 0x28, 0xef, 0xf7, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xfc, 0x60, 0x0, 0x2, 0x9e, 0xff,
    0xf9, 0x30, 0x0, 0x6, 0xcf, 0xff, 0xc6, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xd7, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x7e, 0xff, 0xfa, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x4b, 0xff, 0xfd, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x17, 0xef, 0xff, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xbf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x75,

    /* U+003D "=" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x21, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0x0, 0x4, 0xaf, 0xff,
    0xe8, 0x10, 0x0, 0x0, 0x0, 0x1, 0x7d, 0xff,
    0xfb, 0x40, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xff,
    0xfe, 0x81, 0x0, 0x0, 0x0, 0x0, 0x17, 0xdf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2c, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x5b, 0xff, 0xf6, 0x0,
    0x0, 0x2, 0x8e, 0xff, 0xfa, 0x30, 0x0, 0x5,
    0xbf, 0xff, 0xd6, 0x10, 0x0, 0x8, 0xef, 0xff,
    0xa3, 0x0, 0x0, 0x0, 0x1f, 0xfd, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x1a, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x8c, 0xef, 0xfe, 0xc7, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xc0, 0x63, 0x20, 0x2, 0xaf, 0xf5, 0x0,
    0x0, 0x0, 0xe, 0xf9, 0x0, 0x0, 0x0, 0xc,
    0xf9, 0x0, 0x0, 0x0, 0xd, 0xf9, 0x0, 0x0,
    0x0, 0x4f, 0xf5, 0x0, 0x0, 0x3, 0xef, 0xc0,
    0x0, 0x0, 0x5f, 0xfc, 0x10, 0x0, 0x4, 0xff,
    0x90, 0x0, 0x0, 0xd, 0xf8, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x0, 0xc, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x20, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0,
    0x0, 0x9f, 0xfa, 0x0, 0x0, 0x0, 0x5f, 0xf5,
    0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x3, 0x8c, 0xef, 0xed, 0xa6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4c, 0xff, 0xfd,
    0xcd, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x8f,
    0xfb, 0x40, 0x0, 0x0, 0x39, 0xff, 0x80, 0x0,
    0x0, 0x9f, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x40, 0x0, 0x7f, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfd, 0x0, 0x1f, 0xf3, 0x0,
    0x4, 0xbf, 0xfa, 0x3f, 0xb0, 0xe, 0xf2, 0x9,
    0xf9, 0x0, 0x8, 0xff, 0xbc, 0xfe, 0xf8, 0x0,
    0xaf, 0x60, 0xef, 0x20, 0x5, 0xfe, 0x20, 0x5,
    0xff, 0x50, 0x7, 0xf8, 0x3f, 0xd0, 0x0, 0xdf,
    0x50, 0x0, 0xf, 0xf2, 0x0, 0x7f, 0x86, 0xfa,
    0x0, 0x2f, 0xe0, 0x0, 0x0, 0xff, 0x0, 0x8,
    0xf8, 0x7f, 0x80, 0x5, 0xfb, 0x0, 0x0, 0x2f,
    0xc0, 0x0, 0x9f, 0x58, 0xf8, 0x0, 0x7f, 0x90,
    0x0, 0x6, 0xf9, 0x0, 0xd, 0xf2, 0x7f, 0x90,
    0x5, 0xfa, 0x0, 0x0, 0xef, 0x80, 0x3, 0xfc,
    0x5, 0xfb, 0x0, 0x2f, 0xf4, 0x3, 0xcf, 0xfd,
    0x12, 0xdf, 0x40, 0x1f, 0xf0, 0x0, 0x9f, 0xff,
    0xff, 0x9a, 0xff, 0xff, 0x70, 0x0, 0xbf, 0x70,
    0x0, 0x59, 0xa8, 0x30, 0x6, 0xa8, 0x30, 0x0,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfe, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xc7, 0x31, 0x0, 0x12, 0x59, 0x80, 0x0, 0x0,
    0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0x9b, 0xcb,
    0xa9, 0x62, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x8f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x1b,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfb,
    0x5, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf5, 0x0, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xa0, 0x0, 0x5f, 0xf1, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x40, 0x0, 0xf, 0xf7, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0xa, 0xfd,
    0x0, 0x0, 0x0, 0xd, 0xf9, 0x0, 0x0, 0x5,
    0xff, 0x30, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x2f, 0xf5, 0x0, 0x6, 0xff,
    0x10, 0x0, 0x0, 0x0, 0xc, 0xfb, 0x0, 0xc,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10,
    0x2f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x70, 0x8f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xd0,

    /* U+0042 "B" */
    0x9f, 0xff, 0xff, 0xfd, 0xb5, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x9f, 0xb0, 0x0,
    0x2, 0xaf, 0xf4, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0xef, 0x80, 0x9f, 0xb0, 0x0, 0x0, 0xa, 0xfa,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x9f,
    0xb0, 0x0, 0x0, 0x1e, 0xf5, 0x9, 0xfb, 0x0,
    0x1, 0x5d, 0xfc, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x9, 0xff, 0xcc, 0xcd, 0xff, 0xfa,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x5e, 0xfa, 0x9,
    0xfb, 0x0, 0x0, 0x0, 0x6f, 0xf1, 0x9f, 0xb0,
    0x0, 0x0, 0x2, 0xff, 0x39, 0xfb, 0x0, 0x0,
    0x0, 0x2f, 0xf3, 0x9f, 0xb0, 0x0, 0x0, 0x5,
    0xff, 0x19, 0xfb, 0x0, 0x0, 0x16, 0xef, 0xc0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x9, 0xff,
    0xff, 0xff, 0xec, 0x81, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x6, 0xad, 0xff, 0xfd, 0xa2, 0x0,
    0x4, 0xef, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x4f,
    0xfe, 0x73, 0x10, 0x13, 0x62, 0x0, 0xef, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xfd, 0x63, 0x11, 0x13, 0x74,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x28, 0xce, 0xff, 0xec, 0x92,

    /* U+0044 "D" */
    0x9f, 0xff, 0xff, 0xfe, 0xb8, 0x20, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x9f,
    0xb0, 0x0, 0x2, 0x7e, 0xff, 0x90, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x30, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x1f, 0xfa, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x29, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf3, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x49, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf4, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x39, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf1, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0xa,
    0xfd, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x90, 0x9f, 0xb0, 0x0, 0x0, 0x1, 0xcf, 0xf1,
    0x9, 0xfb, 0x0, 0x1, 0x37, 0xef, 0xf6, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xdb, 0x61, 0x0, 0x0,

    /* U+0045 "E" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0x60, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xfd, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xc1, 0x11, 0x11, 0x11, 0x10,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xf1,

    /* U+0046 "F" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0x69, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xfe, 0x9, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x9f,
    0xb0, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x9,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x5, 0xad, 0xef, 0xfe, 0xb8, 0x10,
    0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x4f, 0xfe, 0x83, 0x10, 0x12, 0x48, 0x20, 0xe,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xb1, 0xff, 0x50, 0x0, 0x1,
    0xff, 0xff, 0xfc, 0xf, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xc0, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x7, 0xfc, 0xc, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xc0, 0x8f, 0xf1, 0x0, 0x0, 0x0, 0x7,
    0xfc, 0x1, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x7f,
    0xc0, 0x7, 0xff, 0xd6, 0x20, 0x0, 0x2a, 0xfc,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x2, 0x8b, 0xef, 0xff, 0xec, 0x94,

    /* U+0048 "H" */
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x9,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf0, 0x9f,
    0xb0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf0, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x9, 0xff, 0xee, 0xee,
    0xee, 0xee, 0xff, 0xf0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf0, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x9,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf0,

    /* U+0049 "I" */
    0x9f, 0xb9, 0xfb, 0x9f, 0xb9, 0xfb, 0x9f, 0xb9,
    0xfb, 0x9f, 0xb9, 0xfb, 0x9f, 0xb9, 0xfb, 0x9f,
    0xb9, 0xfb, 0x9f, 0xb9, 0xfb, 0x9f, 0xb9, 0xfb,
    0x9f, 0xb9, 0xfb,

    /* U+004A "J" */
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x9, 0xfc, 0x0,
    0x0, 0x9f, 0xc0, 0x0, 0x9, 0xfc, 0x0, 0x0,
    0x9f, 0xc0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x9f,
    0xc0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x9f, 0xc0,
    0x0, 0x9, 0xfc, 0x0, 0x0, 0x9f, 0xc0, 0x0,
    0x9, 0xfc, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x9,
    0xfc, 0x0, 0x0, 0x9f, 0xc0, 0x0, 0x9, 0xfc,
    0x0, 0x0, 0x9f, 0xc0, 0x0, 0x9, 0xfc, 0x0,
    0x0, 0xaf, 0xb0, 0x0, 0xe, 0xf9, 0x10, 0x2a,
    0xff, 0x3f, 0xff, 0xff, 0x90, 0xef, 0xfc, 0x60,
    0x0,

    /* U+004B "K" */
    0x9f, 0xb0, 0x0, 0x0, 0x2, 0xef, 0xc0, 0x9f,
    0xb0, 0x0, 0x0, 0xd, 0xfe, 0x10, 0x9f, 0xb0,
    0x0, 0x0, 0xaf, 0xf3, 0x0, 0x9f, 0xb0, 0x0,
    0x7, 0xff, 0x50, 0x0, 0x9f, 0xb0, 0x0, 0x4f,
    0xf7, 0x0, 0x0, 0x9f, 0xb0, 0x1, 0xef, 0xa0,
    0x0, 0x0, 0x9f, 0xb0, 0xc, 0xfc, 0x0, 0x0,
    0x0, 0x9f, 0xb0, 0x9f, 0xe1, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x9f, 0xf3, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0xc,
    0xfe, 0x20, 0x0, 0x0, 0x9f, 0xb0, 0x1, 0xef,
    0xd0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x3f, 0xfb,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x5, 0xff, 0x80,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x9f, 0xf6, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0xb, 0xff, 0x30, 0x9f,
    0xb0, 0x0, 0x0, 0x1, 0xdf, 0xe2,

    /* U+004C "L" */
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xb0, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x9,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0,
    0x0, 0x0, 0x9, 0xfc, 0x11, 0x11, 0x11, 0x10,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0x99, 0xff, 0xff,
    0xff, 0xff, 0xf9,

    /* U+004D "M" */
    0x9f, 0xff, 0x50, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xf3, 0x9f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xf3, 0x9f, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x7f, 0xdf, 0xf3, 0x9f, 0xbb, 0xf8, 0x0, 0x0,
    0x0, 0xef, 0x6f, 0xf3, 0x9f, 0xb4, 0xfe, 0x0,
    0x0, 0x4, 0xfd, 0x2f, 0xf3, 0x9f, 0xb0, 0xef,
    0x50, 0x0, 0xb, 0xf6, 0x2f, 0xf3, 0x9f, 0xb0,
    0x7f, 0xb0, 0x0, 0x1f, 0xf1, 0x2f, 0xf3, 0x9f,
    0xb0, 0x1f, 0xf1, 0x0, 0x7f, 0xa0, 0x2f, 0xf3,
    0x9f, 0xb0, 0xa, 0xf8, 0x0, 0xdf, 0x30, 0x2f,
    0xf3, 0x9f, 0xb0, 0x4, 0xfe, 0x4, 0xfd, 0x0,
    0x2f, 0xf3, 0x9f, 0xb0, 0x0, 0xdf, 0x4a, 0xf6,
    0x0, 0x2f, 0xf3, 0x9f, 0xb0, 0x0, 0x7f, 0xcf,
    0xf1, 0x0, 0x2f, 0xf3, 0x9f, 0xb0, 0x0, 0x1f,
    0xff, 0xa0, 0x0, 0x2f, 0xf3, 0x9f, 0xb0, 0x0,
    0x9, 0xee, 0x30, 0x0, 0x2f, 0xf3, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x9f,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf3, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf3,

    /* U+004E "N" */
    0x9f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x9f, 0xc9,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x9f,
    0xce, 0xf6, 0x0, 0x0, 0x0, 0x9f, 0xc9, 0xfb,
    0x6f, 0xe0, 0x0, 0x0, 0x9, 0xfc, 0x9f, 0xb0,
    0xdf, 0x80, 0x0, 0x0, 0x9f, 0xc9, 0xfb, 0x4,
    0xff, 0x10, 0x0, 0x9, 0xfc, 0x9f, 0xb0, 0xb,
    0xfa, 0x0, 0x0, 0x9f, 0xc9, 0xfb, 0x0, 0x3f,
    0xf3, 0x0, 0x9, 0xfc, 0x9f, 0xb0, 0x0, 0x9f,
    0xb0, 0x0, 0x9f, 0xc9, 0xfb, 0x0, 0x1, 0xff,
    0x40, 0x9, 0xfc, 0x9f, 0xb0, 0x0, 0x8, 0xfd,
    0x0, 0x9f, 0xc9, 0xfb, 0x0, 0x0, 0xe, 0xf6,
    0x9, 0xfc, 0x9f, 0xb0, 0x0, 0x0, 0x6f, 0xe0,
    0x9f, 0xc9, 0xfb, 0x0, 0x0, 0x0, 0xdf, 0x79,
    0xfc, 0x9f, 0xb0, 0x0, 0x0, 0x4, 0xff, 0xaf,
    0xc9, 0xfb, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfc,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xc9,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfc,

    /* U+004F "O" */
    0x0, 0x0, 0x39, 0xde, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0x0, 0x0, 0x9f, 0xfa, 0x30, 0x0, 0x27, 0xff,
    0xd0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x70, 0x9, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xfd, 0x0, 0xcf, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x40, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf5, 0x1f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x61, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf6, 0xf, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x50, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4, 0xc, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x9f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x3,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0,
    0xa, 0xff, 0x93, 0x0, 0x2, 0x7e, 0xfd, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0x0, 0x4, 0xad, 0xff, 0xfe, 0xb6, 0x0,
    0x0,

    /* U+0050 "P" */
    0x9f, 0xff, 0xff, 0xfd, 0xa4, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x9f, 0xb0, 0x0,
    0x14, 0xbf, 0xf6, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0xdf, 0xb0, 0x9f, 0xb0, 0x0, 0x0, 0x8, 0xfe,
    0x9, 0xfb, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x9f,
    0xb0, 0x0, 0x0, 0x6, 0xff, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x8f, 0xe0, 0x9f, 0xb0, 0x0, 0x0,
    0xd, 0xfa, 0x9, 0xfb, 0x0, 0x1, 0x4b, 0xff,
    0x30, 0x9f, 0xff, 0xff, 0xff, 0xff, 0x60, 0x9,
    0xff, 0xee, 0xed, 0xb8, 0x20, 0x0, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x39, 0xde, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0x0, 0x0, 0x9f, 0xfa, 0x30, 0x0, 0x27, 0xff,
    0xd0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x70, 0x9, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xfd, 0x0, 0xcf, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x40, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf5, 0x1f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x61, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf6, 0xf, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x50, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf4, 0xc, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x10, 0x9f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xd0, 0x3,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0,
    0xa, 0xff, 0x93, 0x0, 0x2, 0x7e, 0xfd, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0x0, 0x4, 0xad, 0xff, 0xff, 0xb6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x78, 0x60, 0x0,

    /* U+0052 "R" */
    0x9f, 0xff, 0xff, 0xed, 0x94, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x9f, 0xb0,
    0x0, 0x14, 0xcf, 0xf4, 0x0, 0x9f, 0xb0, 0x0,
    0x0, 0xd, 0xfa, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x9, 0xfc, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x8,
    0xfd, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0x9, 0xfc,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x9f, 0xb0, 0x0, 0x14, 0xbf, 0xf3, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x9f, 0xfe,
    0xee, 0xff, 0x91, 0x0, 0x0, 0x9f, 0xb0, 0x0,
    0xbf, 0xc0, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x1e,
    0xf8, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x5, 0xff,
    0x40, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0xaf, 0xe1,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0xe, 0xfb, 0x0,
    0x9f, 0xb0, 0x0, 0x0, 0x3, 0xff, 0x70, 0x9f,
    0xb0, 0x0, 0x0, 0x0, 0x8f, 0xf3,

    /* U+0053 "S" */
    0x0, 0x18, 0xce, 0xff, 0xec, 0x91, 0x0, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xc, 0xfe, 0x51,
    0x1, 0x24, 0x82, 0x2, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe,
    0xa5, 0x10, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xff,
    0xd7, 0x0, 0x0, 0x0, 0x3, 0x7b, 0xff, 0xfd,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x15, 0x74, 0x20, 0x0, 0x28, 0xff, 0xa0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x3, 0x9c,
    0xdf, 0xff, 0xeb, 0x60, 0x0,

    /* U+0054 "T" */
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x5, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0xbf, 0xa0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xbf,
    0xa0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xbf, 0xa0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xbf, 0xa0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xbf, 0xa0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xbf, 0xa0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xbf,
    0xa0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xbf, 0xa0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xaf, 0xa0, 0x0, 0x0,
    0x0, 0x6, 0xfe, 0x9f, 0xd0, 0x0, 0x0, 0x0,
    0x9, 0xfc, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0xe,
    0xf8, 0xe, 0xff, 0x72, 0x0, 0x15, 0xcf, 0xf2,
    0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x7, 0xbe, 0xff, 0xec, 0x71, 0x0,

    /* U+0056 "V" */
    0x6f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf7,
    0x1f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xc0,
    0x6, 0xff, 0x10, 0x0, 0x0, 0x0, 0xef, 0x70,
    0x1, 0xff, 0x60, 0x0, 0x0, 0x3, 0xff, 0x10,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0x8, 0xfc, 0x0,
    0x0, 0x5f, 0xf0, 0x0, 0x0, 0xd, 0xf6, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x3f, 0xf1, 0x0,
    0x0, 0xb, 0xfa, 0x0, 0x0, 0x8f, 0xb0, 0x0,
    0x0, 0x5, 0xff, 0x0, 0x0, 0xdf, 0x50, 0x0,
    0x0, 0x0, 0xff, 0x50, 0x3, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xa0, 0x8, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf0, 0xd, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf5, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfa, 0x8f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf9, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x3f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x80, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf4, 0xb,
    0xfa, 0x0, 0x0, 0x0, 0xbe, 0xe3, 0x0, 0x0,
    0x3, 0xff, 0x10, 0x8f, 0xe0, 0x0, 0x0, 0x1f,
    0xff, 0x80, 0x0, 0x0, 0x7f, 0xd0, 0x4, 0xff,
    0x10, 0x0, 0x5, 0xfe, 0xfc, 0x0, 0x0, 0xa,
    0xf9, 0x0, 0xf, 0xf5, 0x0, 0x0, 0x9f, 0x6f,
    0xf1, 0x0, 0x0, 0xef, 0x50, 0x0, 0xcf, 0x80,
    0x0, 0xe, 0xf1, 0xbf, 0x50, 0x0, 0x1f, 0xf1,
    0x0, 0x9, 0xfc, 0x0, 0x2, 0xfc, 0x7, 0xfa,
    0x0, 0x5, 0xfe, 0x0, 0x0, 0x5f, 0xf0, 0x0,
    0x7f, 0x80, 0x3f, 0xe0, 0x0, 0x8f, 0xa0, 0x0,
    0x1, 0xff, 0x30, 0xb, 0xf3, 0x0, 0xef, 0x20,
    0xc, 0xf6, 0x0, 0x0, 0xd, 0xf7, 0x0, 0xff,
    0x0, 0xa, 0xf7, 0x0, 0xff, 0x20, 0x0, 0x0,
    0xaf, 0xa0, 0x4f, 0xa0, 0x0, 0x5f, 0xb0, 0x3f,
    0xe0, 0x0, 0x0, 0x6, 0xfe, 0x9, 0xf6, 0x0,
    0x1, 0xff, 0x7, 0xfa, 0x0, 0x0, 0x0, 0x2f,
    0xf1, 0xdf, 0x10, 0x0, 0xd, 0xf4, 0xaf, 0x70,
    0x0, 0x0, 0x0, 0xef, 0x7f, 0xd0, 0x0, 0x0,
    0x8f, 0x9e, 0xf3, 0x0, 0x0, 0x0, 0xb, 0xfe,
    0xf8, 0x0, 0x0, 0x4, 0xfe, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x40, 0x0, 0x0, 0xf,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0,

    /* U+0058 "X" */
    0x9, 0xff, 0x20, 0x0, 0x0, 0x0, 0x7f, 0xf1,
    0x1, 0xef, 0xb0, 0x0, 0x0, 0x2, 0xff, 0x60,
    0x0, 0x5f, 0xf4, 0x0, 0x0, 0xb, 0xfc, 0x0,
    0x0, 0xc, 0xfd, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x2, 0xff, 0x60, 0x0, 0xef, 0x70, 0x0,
    0x0, 0x0, 0x8f, 0xe1, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf9, 0x3f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xdf, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xfe, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf5, 0x1f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xa0, 0x6, 0xff, 0x20, 0x0,
    0x0, 0x7, 0xff, 0x10, 0x0, 0xdf, 0xc0, 0x0,
    0x0, 0x2f, 0xf6, 0x0, 0x0, 0x3f, 0xf6, 0x0,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0xa, 0xfe, 0x10,
    0x6, 0xff, 0x20, 0x0, 0x0, 0x1, 0xff, 0xa0,
    0x2f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf4,

    /* U+0059 "Y" */
    0x8f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe0,
    0xd, 0xfc, 0x0, 0x0, 0x0, 0x2, 0xff, 0x50,
    0x4, 0xff, 0x50, 0x0, 0x0, 0xc, 0xfb, 0x0,
    0x0, 0xaf, 0xe0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x1f, 0xf8, 0x0, 0x0, 0xef, 0x70, 0x0,
    0x0, 0x6, 0xff, 0x20, 0x8, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xb0, 0x2f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf5, 0xbf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x1,
    0x11, 0x11, 0x11, 0x11, 0x8f, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x72, 0x22, 0x22, 0x22, 0x22, 0x20,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x27,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,

    /* U+005B "[" */
    0x3f, 0xff, 0xfb, 0x3f, 0xfe, 0xea, 0x3f, 0xf1,
    0x0, 0x3f, 0xf1, 0x0, 0x3f, 0xf1, 0x0, 0x3f,
    0xf1, 0x0, 0x3f, 0xf1, 0x0, 0x3f, 0xf1, 0x0,
    0x3f, 0xf1, 0x0, 0x3f, 0xf1, 0x0, 0x3f, 0xf1,
    0x0, 0x3f, 0xf1, 0x0, 0x3f, 0xf1, 0x0, 0x3f,
    0xf1, 0x0, 0x3f, 0xf1, 0x0, 0x3f, 0xf1, 0x0,
    0x3f, 0xf1, 0x0, 0x3f, 0xf1, 0x0, 0x3f, 0xf1,
    0x0, 0x3f, 0xf1, 0x0, 0x3f, 0xf1, 0x0, 0x3f,
    0xf1, 0x0, 0x3f, 0xfe, 0xea, 0x3f, 0xff, 0xfb,

    /* U+005C "\\" */
    0x2f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf6,

    /* U+005D "]" */
    0x1f, 0xff, 0xfd, 0x1e, 0xee, 0xfd, 0x0, 0x7,
    0xfd, 0x0, 0x7, 0xfd, 0x0, 0x7, 0xfd, 0x0,
    0x7, 0xfd, 0x0, 0x7, 0xfd, 0x0, 0x7, 0xfd,
    0x0, 0x7, 0xfd, 0x0, 0x7, 0xfd, 0x0, 0x7,
    0xfd, 0x0, 0x7, 0xfd, 0x0, 0x7, 0xfd, 0x0,
    0x7, 0xfd, 0x0, 0x7, 0xfd, 0x0, 0x7, 0xfd,
    0x0, 0x7, 0xfd, 0x0, 0x7, 0xfd, 0x0, 0x7,
    0xfd, 0x0, 0x7, 0xfd, 0x0, 0x7, 0xfd, 0x0,
    0x7, 0xfd, 0x1e, 0xef, 0xfd, 0x1f, 0xff, 0xfd,

    /* U+005E "^" */
    0x0, 0x0, 0x38, 0x86, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0x20, 0x0, 0x0, 0x2, 0xff, 0xbf,
    0x90, 0x0, 0x0, 0x9, 0xf9, 0x3f, 0xf0, 0x0,
    0x0, 0x1f, 0xf2, 0xc, 0xf6, 0x0, 0x0, 0x7f,
    0xb0, 0x6, 0xfd, 0x0, 0x0, 0xef, 0x40, 0x0,
    0xef, 0x40, 0x5, 0xfd, 0x0, 0x0, 0x8f, 0xa0,
    0xc, 0xf7, 0x0, 0x0, 0x1f, 0xf1, 0x2f, 0xf1,
    0x0, 0x0, 0xa, 0xf8,

    /* U+005F "_" */
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+0060 "`" */
    0x28, 0x83, 0x0, 0x9, 0xfe, 0x0, 0x0, 0xaf,
    0x90, 0x0, 0xb, 0xf3,

    /* U+0061 "a" */
    0x0, 0x3, 0xef, 0xff, 0xeb, 0x50, 0x0, 0x0,
    0x4f, 0xdc, 0xdf, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x80, 0x1, 0x69, 0xbc, 0xcc, 0xce, 0xf8, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x81, 0xff, 0xa2,
    0x0, 0x0, 0xb, 0xf8, 0x6f, 0xe0, 0x0, 0x0,
    0x0, 0xbf, 0x88, 0xfc, 0x0, 0x0, 0x0, 0xd,
    0xf8, 0x8f, 0xd0, 0x0, 0x0, 0x7, 0xff, 0x84,
    0xff, 0x60, 0x0, 0x29, 0xff, 0xf8, 0xb, 0xff,
    0xfd, 0xff, 0xf8, 0x9f, 0x80, 0x7, 0xcf, 0xfe,
    0xa3, 0x7, 0xf8,

    /* U+0062 "b" */
    0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf9, 0x6, 0xce, 0xfe, 0xa3, 0x0, 0xaf,
    0x9a, 0xff, 0xde, 0xff, 0xf6, 0xa, 0xfd, 0xe5,
    0x0, 0x2, 0xcf, 0xf2, 0xaf, 0xf4, 0x0, 0x0,
    0x1, 0xff, 0x8a, 0xfd, 0x0, 0x0, 0x0, 0xa,
    0xfb, 0xaf, 0xa0, 0x0, 0x0, 0x0, 0x7f, 0xea,
    0xf8, 0x0, 0x0, 0x0, 0x6, 0xfe, 0xaf, 0x80,
    0x0, 0x0, 0x0, 0x6f, 0xea, 0xfa, 0x0, 0x0,
    0x0, 0x7, 0xfd, 0xaf, 0xd0, 0x0, 0x0, 0x0,
    0xaf, 0xba, 0xff, 0x40, 0x0, 0x0, 0x1f, 0xf7,
    0xaf, 0xdf, 0x60, 0x0, 0x2c, 0xff, 0x1a, 0xf7,
    0xbf, 0xfe, 0xef, 0xff, 0x40, 0xaf, 0x50, 0x8d,
    0xff, 0xd9, 0x20, 0x0,

    /* U+0063 "c" */
    0x0, 0x5, 0xbe, 0xff, 0xec, 0x60, 0xb, 0xff,
    0xff, 0xff, 0xfb, 0x8, 0xff, 0x71, 0x0, 0x2,
    0x40, 0xef, 0x80, 0x0, 0x0, 0x0, 0x3f, 0xf2,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0xf,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf7, 0x10,
    0x0, 0x24, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x5b, 0xef, 0xfe, 0xc7,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf2, 0x0, 0x6, 0xce, 0xfe, 0xa3,
    0x1f, 0xf2, 0x0, 0xbf, 0xff, 0xde, 0xff, 0x5f,
    0xf2, 0x8, 0xff, 0x60, 0x0, 0x1a, 0xef, 0xf2,
    0xe, 0xf8, 0x0, 0x0, 0x0, 0xbf, 0xf2, 0x3f,
    0xf2, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x4f, 0xf0,
    0x0, 0x0, 0x0, 0x2f, 0xf2, 0x6f, 0xf0, 0x0,
    0x0, 0x0, 0xf, 0xf2, 0x6f, 0xf0, 0x0, 0x0,
    0x0, 0xf, 0xf2, 0x5f, 0xf0, 0x0, 0x0, 0x0,
    0x2f, 0xf2, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x4f,
    0xf2, 0xf, 0xf8, 0x0, 0x0, 0x0, 0xbf, 0xf2,
    0x9, 0xff, 0x60, 0x0, 0x2a, 0xef, 0xf2, 0x0,
    0xcf, 0xff, 0xee, 0xff, 0x4e, 0xf2, 0x0, 0x7,
    0xce, 0xfe, 0xa3, 0xc, 0xf2,

    /* U+0065 "e" */
    0x0, 0x5, 0xbe, 0xff, 0xd8, 0x10, 0x0, 0xb,
    0xff, 0xfd, 0xdf, 0xfe, 0x20, 0x8, 0xff, 0x50,
    0x0, 0x1a, 0xfd, 0x0, 0xff, 0x60, 0x0, 0x0,
    0xf, 0xf3, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0xcf,
    0x66, 0xff, 0x0, 0x0, 0x23, 0x4c, 0xf7, 0x7f,
    0xfd, 0xef, 0xff, 0xff, 0xff, 0x77, 0xff, 0x88,
    0x88, 0x88, 0x88, 0x83, 0x6f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf9, 0x20, 0x0, 0x25, 0x80, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x5b, 0xef,
    0xfe, 0xc9, 0x40,

    /* U+0066 "f" */
    0x0, 0x0, 0x5c, 0xef, 0xe1, 0x0, 0x6, 0xff,
    0xec, 0xd1, 0x0, 0xd, 0xf9, 0x0, 0x0, 0x0,
    0x1f, 0xf2, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0x50, 0x6d, 0xdf,
    0xfd, 0xdd, 0x40, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0,
    0x3f, 0xf0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0,
    0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x3f, 0xf0, 0x0, 0x0, 0x0, 0x3f, 0xf0,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x6, 0xce, 0xfe, 0xa2, 0x1f, 0xf2, 0x0,
    0xbf, 0xff, 0xde, 0xff, 0x4f, 0xf2, 0x8, 0xff,
    0x60, 0x0, 0x1a, 0xdf, 0xf2, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0xbf, 0xf2, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x4f, 0xf2, 0x4f, 0xf0, 0x0, 0x0, 0x0,
    0x2f, 0xf2, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0xf2, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0xf, 0xf2,
    0x5f, 0xf0, 0x0, 0x0, 0x0, 0x2f, 0xf2, 0x4f,
    0xf2, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0xf, 0xf8,
    0x0, 0x0, 0x0, 0xbf, 0xf2, 0x9, 0xff, 0x60,
    0x0, 0x19, 0xef, 0xf2, 0x1, 0xdf, 0xff, 0xde,
    0xff, 0x5f, 0xf2, 0x0, 0x7, 0xce, 0xfe, 0xb4,
    0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xb0,
    0x0, 0x1, 0x0, 0x0, 0x3b, 0xff, 0x30, 0x0,
    0x1f, 0xfe, 0xef, 0xff, 0xf5, 0x0, 0x0, 0x1c,
    0xef, 0xfe, 0xc8, 0x20, 0x0,

    /* U+0068 "h" */
    0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90,
    0x7c, 0xff, 0xd9, 0x10, 0xaf, 0xad, 0xff, 0xff,
    0xff, 0xe1, 0xaf, 0xfe, 0x61, 0x0, 0x6f, 0xf7,
    0xaf, 0xf3, 0x0, 0x0, 0xb, 0xfa, 0xaf, 0xb0,
    0x0, 0x0, 0x8, 0xfc, 0xaf, 0x90, 0x0, 0x0,
    0x7, 0xfd, 0xaf, 0x90, 0x0, 0x0, 0x7, 0xfd,
    0xaf, 0x90, 0x0, 0x0, 0x7, 0xfd, 0xaf, 0x90,
    0x0, 0x0, 0x7, 0xfd, 0xaf, 0x90, 0x0, 0x0,
    0x7, 0xfd, 0xaf, 0x90, 0x0, 0x0, 0x7, 0xfd,
    0xaf, 0x90, 0x0, 0x0, 0x7, 0xfd, 0xaf, 0x90,
    0x0, 0x0, 0x7, 0xfd, 0xaf, 0x90, 0x0, 0x0,
    0x7, 0xfd,

    /* U+0069 "i" */
    0xaf, 0x9d, 0xfc, 0x49, 0x40, 0x0, 0x0, 0xa,
    0xf9, 0xaf, 0x9a, 0xf9, 0xaf, 0x9a, 0xf9, 0xaf,
    0x9a, 0xf9, 0xaf, 0x9a, 0xf9, 0xaf, 0x9a, 0xf9,
    0xaf, 0x9a, 0xf9, 0xaf, 0x90,

    /* U+006A "j" */
    0x0, 0x0, 0xaf, 0x90, 0x0, 0xd, 0xfc, 0x0,
    0x0, 0x49, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0xaf,
    0x90, 0x0, 0xa, 0xf9, 0x0, 0x0, 0xaf, 0x90,
    0x0, 0xa, 0xf9, 0x0, 0x0, 0xaf, 0x90, 0x0,
    0xa, 0xf9, 0x0, 0x0, 0xaf, 0x90, 0x0, 0xa,
    0xf9, 0x0, 0x0, 0xaf, 0x90, 0x0, 0xa, 0xf9,
    0x0, 0x0, 0xaf, 0x90, 0x0, 0xa, 0xf9, 0x0,
    0x0, 0xaf, 0x90, 0x0, 0xa, 0xf9, 0x0, 0x0,
    0xcf, 0x70, 0x0, 0x4f, 0xf3, 0xbd, 0xdf, 0xfc,
    0xc, 0xff, 0xd9, 0x10,

    /* U+006B "k" */
    0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90,
    0x0, 0x2, 0xff, 0x90, 0xaf, 0x90, 0x0, 0x1d,
    0xfb, 0x0, 0xaf, 0x90, 0x0, 0xbf, 0xd0, 0x0,
    0xaf, 0x90, 0x9, 0xfe, 0x10, 0x0, 0xaf, 0x90,
    0x6f, 0xf2, 0x0, 0x0, 0xaf, 0x93, 0xff, 0x40,
    0x0, 0x0, 0xaf, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0xaf, 0xed, 0xfc, 0x0, 0x0, 0x0, 0xaf, 0x91,
    0xef, 0xa0, 0x0, 0x0, 0xaf, 0x90, 0x3f, 0xf7,
    0x0, 0x0, 0xaf, 0x90, 0x5, 0xff, 0x50, 0x0,
    0xaf, 0x90, 0x0, 0x8f, 0xf3, 0x0, 0xaf, 0x90,
    0x0, 0xb, 0xfe, 0x20, 0xaf, 0x90, 0x0, 0x1,
    0xdf, 0xd1,

    /* U+006C "l" */
    0xaf, 0x90, 0xa, 0xf9, 0x0, 0xaf, 0x90, 0xa,
    0xf9, 0x0, 0xaf, 0x90, 0xa, 0xf9, 0x0, 0xaf,
    0x90, 0xa, 0xf9, 0x0, 0xaf, 0x90, 0xa, 0xf9,
    0x0, 0xaf, 0x90, 0xa, 0xf9, 0x0, 0xaf, 0x90,
    0xa, 0xf9, 0x0, 0xaf, 0x90, 0xa, 0xf9, 0x0,
    0x9f, 0xc0, 0x5, 0xff, 0xed, 0x8, 0xef, 0xe0,

    /* U+006D "m" */
    0xaf, 0x91, 0x9e, 0xfe, 0xb3, 0x0, 0x7c, 0xff,
    0xd8, 0x0, 0xaf, 0xbe, 0xff, 0xff, 0xff, 0x4b,
    0xff, 0xff, 0xff, 0xc0, 0xaf, 0xfd, 0x40, 0x2,
    0xdf, 0xff, 0x81, 0x0, 0x7f, 0xf5, 0xaf, 0xf1,
    0x0, 0x0, 0x6f, 0xf8, 0x0, 0x0, 0xd, 0xf9,
    0xaf, 0xa0, 0x0, 0x0, 0x2f, 0xf2, 0x0, 0x0,
    0x9, 0xfb, 0xaf, 0x90, 0x0, 0x0, 0x1f, 0xf2,
    0x0, 0x0, 0x8, 0xfb, 0xaf, 0x90, 0x0, 0x0,
    0x1f, 0xf2, 0x0, 0x0, 0x8, 0xfb, 0xaf, 0x90,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x8, 0xfb,
    0xaf, 0x90, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0,
    0x8, 0xfb, 0xaf, 0x90, 0x0, 0x0, 0x1f, 0xf2,
    0x0, 0x0, 0x8, 0xfb, 0xaf, 0x90, 0x0, 0x0,
    0x1f, 0xf2, 0x0, 0x0, 0x8, 0xfb, 0xaf, 0x90,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x8, 0xfb,
    0xaf, 0x90, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0,
    0x8, 0xfb, 0xaf, 0x90, 0x0, 0x0, 0x1f, 0xf2,
    0x0, 0x0, 0x8, 0xfb,

    /* U+006E "n" */
    0xaf, 0x90, 0x7c, 0xff, 0xd9, 0x10, 0xaf, 0xad,
    0xff, 0xff, 0xff, 0xe1, 0xaf, 0xfe, 0x61, 0x0,
    0x6f, 0xf7, 0xaf, 0xf3, 0x0, 0x0, 0xb, 0xfa,
    0xaf, 0xb0, 0x0, 0x0, 0x8, 0xfc, 0xaf, 0x90,
    0x0, 0x0, 0x7, 0xfd, 0xaf, 0x90, 0x0, 0x0,
    0x7, 0xfd, 0xaf, 0x90, 0x0, 0x0, 0x7, 0xfd,
    0xaf, 0x90, 0x0, 0x0, 0x7, 0xfd, 0xaf, 0x90,
    0x0, 0x0, 0x7, 0xfd, 0xaf, 0x90, 0x0, 0x0,
    0x7, 0xfd, 0xaf, 0x90, 0x0, 0x0, 0x7, 0xfd,
    0xaf, 0x90, 0x0, 0x0, 0x7, 0xfd, 0xaf, 0x90,
    0x0, 0x0, 0x7, 0xfd,

    /* U+006F "o" */
    0x0, 0x5, 0xbe, 0xff, 0xeb, 0x50, 0x0, 0x0,
    0xbf, 0xff, 0xdd, 0xff, 0xfb, 0x0, 0x8, 0xff,
    0x60, 0x0, 0x5, 0xff, 0x80, 0xe, 0xf7, 0x0,
    0x0, 0x0, 0x7f, 0xf0, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x1f, 0xf4, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0xf, 0xf6, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0xe,
    0xf7, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0xe, 0xf7,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0xe, 0xf6, 0x3f,
    0xf2, 0x0, 0x0, 0x0, 0x1f, 0xf4, 0xf, 0xf8,
    0x0, 0x0, 0x0, 0x6f, 0xf0, 0x8, 0xff, 0x60,
    0x0, 0x5, 0xff, 0x80, 0x0, 0xbf, 0xff, 0xee,
    0xff, 0xfb, 0x0, 0x0, 0x5, 0xbe, 0xff, 0xeb,
    0x60, 0x0,

    /* U+0070 "p" */
    0xaf, 0x90, 0x6c, 0xef, 0xea, 0x30, 0xa, 0xf9,
    0xaf, 0xfd, 0xef, 0xff, 0x60, 0xaf, 0xde, 0x50,
    0x0, 0x2c, 0xff, 0x2a, 0xff, 0x40, 0x0, 0x0,
    0x1f, 0xf8, 0xaf, 0xd0, 0x0, 0x0, 0x0, 0xaf,
    0xba, 0xfa, 0x0, 0x0, 0x0, 0x7, 0xfe, 0xaf,
    0x80, 0x0, 0x0, 0x0, 0x6f, 0xea, 0xf8, 0x0,
    0x0, 0x0, 0x6, 0xfe, 0xaf, 0xa0, 0x0, 0x0,
    0x0, 0x7f, 0xda, 0xfd, 0x0, 0x0, 0x0, 0xa,
    0xfb, 0xaf, 0xf4, 0x0, 0x0, 0x1, 0xff, 0x7a,
    0xfe, 0xf6, 0x0, 0x2, 0xcf, 0xf1, 0xaf, 0x9b,
    0xff, 0xee, 0xff, 0xf4, 0xa, 0xf9, 0x8, 0xdf,
    0xfd, 0x92, 0x0, 0xaf, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x6, 0xce, 0xfe, 0xa3, 0xf, 0xf3, 0x0,
    0xbf, 0xff, 0xde, 0xff, 0x5f, 0xf3, 0x8, 0xff,
    0x60, 0x0, 0x1a, 0xdf, 0xf3, 0xe, 0xf8, 0x0,
    0x0, 0x0, 0xbf, 0xf3, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x4f, 0xf3, 0x4f, 0xf0, 0x0, 0x0, 0x0,
    0x2f, 0xf3, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0xf3, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0xf, 0xf3,
    0x5f, 0xf0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x3f,
    0xf2, 0x0, 0x0, 0x0, 0x4f, 0xf3, 0xf, 0xf8,
    0x0, 0x0, 0x0, 0xbf, 0xf3, 0x9, 0xff, 0x60,
    0x0, 0x2a, 0xdf, 0xf3, 0x0, 0xcf, 0xff, 0xee,
    0xff, 0x4f, 0xf3, 0x0, 0x7, 0xce, 0xfd, 0xa3,
    0xf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf3,

    /* U+0072 "r" */
    0xaf, 0x90, 0x6c, 0xf4, 0xaf, 0x9a, 0xff, 0xf4,
    0xaf, 0xdf, 0x92, 0x0, 0xaf, 0xf6, 0x0, 0x0,
    0xaf, 0xd0, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0xaf, 0x90, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x4b, 0xef, 0xfe, 0xc3, 0x7, 0xff, 0xec,
    0xde, 0xf4, 0xf, 0xf5, 0x0, 0x0, 0x10, 0x3f,
    0xe0, 0x0, 0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0,
    0x0, 0xd, 0xfc, 0x51, 0x0, 0x0, 0x3, 0xdf,
    0xff, 0xd8, 0x10, 0x0, 0x4, 0x8c, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x1c, 0xfb, 0x0, 0x0, 0x0,
    0x3, 0xfe, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x10,
    0x0, 0x0, 0x1b, 0xfb, 0x7f, 0xdc, 0xcd, 0xff,
    0xf3, 0x4c, 0xff, 0xff, 0xd9, 0x20,

    /* U+0074 "t" */
    0x0, 0x8f, 0xb0, 0x0, 0x0, 0x8, 0xfb, 0x0,
    0x0, 0x0, 0x8f, 0xb0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xf3, 0x8d, 0xef, 0xfd, 0xdd, 0x20, 0x8,
    0xfb, 0x0, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0x0,
    0x8, 0xfb, 0x0, 0x0, 0x0, 0x8f, 0xb0, 0x0,
    0x0, 0x8, 0xfb, 0x0, 0x0, 0x0, 0x8f, 0xb0,
    0x0, 0x0, 0x8, 0xfb, 0x0, 0x0, 0x0, 0x8f,
    0xb0, 0x0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0,
    0x5f, 0xf2, 0x0, 0x0, 0x1, 0xff, 0xfd, 0xe4,
    0x0, 0x3, 0xcf, 0xff, 0x30,

    /* U+0075 "u" */
    0xbf, 0x80, 0x0, 0x0, 0xa, 0xf9, 0xbf, 0x80,
    0x0, 0x0, 0xa, 0xf9, 0xbf, 0x80, 0x0, 0x0,
    0xa, 0xf9, 0xbf, 0x80, 0x0, 0x0, 0xa, 0xf9,
    0xbf, 0x80, 0x0, 0x0, 0xa, 0xf9, 0xbf, 0x80,
    0x0, 0x0, 0xa, 0xf9, 0xbf, 0x80, 0x0, 0x0,
    0xa, 0xf9, 0xbf, 0x80, 0x0, 0x0, 0xa, 0xf9,
    0xbf, 0x80, 0x0, 0x0, 0xa, 0xf9, 0xbf, 0x80,
    0x0, 0x0, 0xb, 0xf9, 0x9f, 0xb0, 0x0, 0x0,
    0x3f, 0xf9, 0x7f, 0xf6, 0x0, 0x6, 0xff, 0xf9,
    0xd, 0xff, 0xff, 0xff, 0xc8, 0xf9, 0x1, 0x9d,
    0xff, 0xd8, 0x6, 0xf9,

    /* U+0076 "v" */
    0x8f, 0xb0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x2f,
    0xf1, 0x0, 0x0, 0x0, 0x7f, 0xd0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0xdf, 0x70, 0x6, 0xfc, 0x0,
    0x0, 0x2, 0xff, 0x10, 0x1, 0xff, 0x20, 0x0,
    0x8, 0xfb, 0x0, 0x0, 0xaf, 0x80, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0x3f, 0xf0,
    0x0, 0x0, 0xe, 0xf3, 0x0, 0x9f, 0x90, 0x0,
    0x0, 0x8, 0xf9, 0x0, 0xef, 0x30, 0x0, 0x0,
    0x2, 0xfe, 0x4, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x59, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xbf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x50,
    0x0, 0x0,

    /* U+0077 "w" */
    0x4f, 0xe0, 0x0, 0x0, 0xb, 0xff, 0x30, 0x0,
    0x0, 0x5f, 0xd0, 0xff, 0x30, 0x0, 0x0, 0xff,
    0xf8, 0x0, 0x0, 0x9, 0xf9, 0xb, 0xf7, 0x0,
    0x0, 0x5f, 0xdf, 0xd0, 0x0, 0x0, 0xdf, 0x40,
    0x7f, 0xb0, 0x0, 0xa, 0xf5, 0xef, 0x20, 0x0,
    0x1f, 0xf0, 0x3, 0xfe, 0x0, 0x0, 0xef, 0x19,
    0xf7, 0x0, 0x5, 0xfb, 0x0, 0xe, 0xf3, 0x0,
    0x3f, 0xb0, 0x4f, 0xb0, 0x0, 0x9f, 0x70, 0x0,
    0xaf, 0x70, 0x8, 0xf7, 0x0, 0xff, 0x10, 0xd,
    0xf2, 0x0, 0x6, 0xfb, 0x0, 0xdf, 0x20, 0xa,
    0xf5, 0x1, 0xfe, 0x0, 0x0, 0x1f, 0xe0, 0x1f,
    0xd0, 0x0, 0x6f, 0xa0, 0x5f, 0x90, 0x0, 0x0,
    0xdf, 0x36, 0xf8, 0x0, 0x1, 0xff, 0xa, 0xf5,
    0x0, 0x0, 0x9, 0xf7, 0xbf, 0x30, 0x0, 0xc,
    0xf4, 0xef, 0x10, 0x0, 0x0, 0x4f, 0xcf, 0xe0,
    0x0, 0x0, 0x7f, 0xbf, 0xc0, 0x0, 0x0, 0x0,
    0xff, 0xf9, 0x0, 0x0, 0x2, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x40, 0x0, 0x0, 0xd,
    0xff, 0x30, 0x0,

    /* U+0078 "x" */
    0x1e, 0xf7, 0x0, 0x0, 0x1, 0xef, 0x70, 0x5,
    0xff, 0x20, 0x0, 0x9, 0xfc, 0x0, 0x0, 0xbf,
    0xb0, 0x0, 0x4f, 0xf2, 0x0, 0x0, 0x1e, 0xf6,
    0x0, 0xdf, 0x70, 0x0, 0x0, 0x5, 0xfe, 0x18,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xcf, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x8e, 0xf6, 0x0, 0x0, 0x0,
    0x9, 0xfc, 0x5, 0xff, 0x10, 0x0, 0x0, 0x4f,
    0xf2, 0x0, 0xbf, 0xb0, 0x0, 0x0, 0xef, 0x80,
    0x0, 0x2f, 0xf6, 0x0, 0x9, 0xfd, 0x0, 0x0,
    0x7, 0xff, 0x10, 0x4f, 0xf3, 0x0, 0x0, 0x0,
    0xcf, 0xb0,

    /* U+0079 "y" */
    0x8f, 0xd0, 0x0, 0x0, 0x0, 0xf, 0xf3, 0x2f,
    0xf3, 0x0, 0x0, 0x0, 0x5f, 0xd0, 0xb, 0xf9,
    0x0, 0x0, 0x0, 0xbf, 0x70, 0x5, 0xff, 0x0,
    0x0, 0x1, 0xff, 0x10, 0x0, 0xef, 0x50, 0x0,
    0x6, 0xfb, 0x0, 0x0, 0x8f, 0xb0, 0x0, 0xc,
    0xf5, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0xb, 0xf7, 0x0, 0x7f, 0x90, 0x0,
    0x0, 0x4, 0xfd, 0x0, 0xdf, 0x30, 0x0, 0x0,
    0x0, 0xef, 0x32, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x98, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xee, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xcf, 0xd0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x4, 0xfd,
    0x81, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x3e, 0xee,
    0xee, 0xee, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x3f, 0xf5, 0x0,
    0x0, 0x0, 0x1, 0xef, 0x90, 0x0, 0x0, 0x0,
    0xb, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe2,
    0x0, 0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x1e, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xc0, 0x0, 0x0, 0x0, 0x8, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xf3,

    /* U+007B "{" */
    0x0, 0x6, 0xcd, 0x0, 0x6f, 0xfe, 0x0, 0xdf,
    0x91, 0x0, 0xff, 0x30, 0x1, 0xff, 0x30, 0x1,
    0xff, 0x30, 0x1, 0xff, 0x30, 0x1, 0xff, 0x30,
    0x1, 0xff, 0x30, 0x1, 0xff, 0x30, 0x6, 0xff,
    0x10, 0xbf, 0xf7, 0x0, 0xbf, 0xf7, 0x0, 0x6,
    0xff, 0x10, 0x1, 0xff, 0x30, 0x1, 0xff, 0x30,
    0x1, 0xff, 0x30, 0x1, 0xff, 0x30, 0x1, 0xff,
    0x30, 0x1, 0xff, 0x30, 0x0, 0xff, 0x30, 0x0,
    0xdf, 0x91, 0x0, 0x6f, 0xfe, 0x0, 0x6, 0xcd,

    /* U+007C "|" */
    0xbf, 0x7b, 0xf7, 0xbf, 0x7b, 0xf7, 0xbf, 0x7b,
    0xf7, 0xbf, 0x7b, 0xf7, 0xbf, 0x7b, 0xf7, 0xbf,
    0x7b, 0xf7, 0xbf, 0x7b, 0xf7, 0xbf, 0x7b, 0xf7,
    0xbf, 0x7b, 0xf7, 0xbf, 0x7b, 0xf7, 0xbf, 0x7b,
    0xf7, 0xbf, 0x7b, 0xf7,

    /* U+007D "}" */
    0x1e, 0xb4, 0x0, 0x1, 0xff, 0xf3, 0x0, 0x1,
    0xcf, 0xa0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x6f,
    0xd0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x6f, 0xd0,
    0x0, 0x6, 0xfd, 0x0, 0x0, 0x6f, 0xd0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x4f, 0xf4, 0x0, 0x0,
    0xbf, 0xf8, 0x0, 0xb, 0xff, 0x80, 0x4, 0xff,
    0x30, 0x0, 0x6f, 0xd0, 0x0, 0x6, 0xfd, 0x0,
    0x0, 0x6f, 0xd0, 0x0, 0x6, 0xfd, 0x0, 0x0,
    0x6f, 0xd0, 0x0, 0x6, 0xfd, 0x0, 0x0, 0x7f,
    0xc0, 0x0, 0x1c, 0xfa, 0x0, 0x1f, 0xff, 0x30,
    0x1, 0xeb, 0x40, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0xfd, 0x81, 0x0, 0x7, 0xd6, 0x8, 0xff,
    0xff, 0xff, 0x61, 0x2d, 0xf5, 0xf, 0xf4, 0x4,
    0xcf, 0xff, 0xff, 0xd0, 0x2c, 0xb0, 0x0, 0x5,
    0xcf, 0xea, 0x10,

    /* U+7F6E "置" */
    0x0, 0x46, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x66, 0x66, 0x60, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0xdf, 0x0, 0x0, 0xfb, 0x0, 0x6, 0xf4,
    0x0, 0xb, 0xf1, 0x0, 0x0, 0xdf, 0x0, 0x0,
    0xfb, 0x0, 0x6, 0xf4, 0x0, 0xb, 0xf1, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x45, 0x55, 0x55,
    0x55, 0x68, 0x65, 0x55, 0x55, 0x55, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x5, 0x77, 0x77, 0x77, 0x77, 0x9f, 0xc7, 0x77,
    0x77, 0x77, 0x77, 0x0, 0x0, 0x0, 0x22, 0x22,
    0x22, 0x6f, 0xa2, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x49, 0xf7, 0x0, 0x0,
    0x0, 0x3, 0xfc, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x49, 0xf7, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x3, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf7, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x3, 0xfc, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x49, 0xf7, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x28, 0xf7, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x3, 0xfb, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x17, 0xf7, 0x0, 0x0,
    0x78, 0x8a, 0xfd, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x8b, 0xfb, 0x88, 0x81, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,

    /* U+8BBE "设" */
    0x0, 0x4, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xc0, 0x0,
    0x0, 0x1b, 0xbb, 0xbb, 0xbb, 0xb7, 0x0, 0x0,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x1, 0xef, 0x80,
    0x0, 0x2f, 0xe0, 0x0, 0x6, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0x0, 0x2f, 0xd0, 0x0,
    0x6, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf3,
    0x0, 0x4f, 0xc0, 0x0, 0x6, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0xaf, 0x70, 0x0,
    0x5, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x10, 0x0, 0x3, 0xff, 0xff, 0xf4,
    0x58, 0x88, 0x87, 0x0, 0x8f, 0xf5, 0x0, 0x0,
    0x0, 0x6b, 0xdd, 0xd2, 0x9f, 0xff, 0xfe, 0x0,
    0x2c, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x12, 0x23, 0xfe, 0x0, 0x1b, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xb1, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0xcf, 0x50, 0x0,
    0x0, 0x3f, 0xd0, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x4f, 0xd0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0xb, 0xf7, 0x0,
    0x6, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x0,
    0x0, 0x2, 0xff, 0x30, 0x3f, 0xf3, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x11, 0x0, 0x6f, 0xe4,
    0xef, 0x60, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x3,
    0xe9, 0x0, 0x9, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x5f, 0xfa, 0x0, 0x6, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0x70, 0x3, 0xcf, 0xfa, 0xff, 0xc3, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xe3, 0x4, 0xbf, 0xfc, 0x20,
    0x4e, 0xff, 0xb4, 0x0, 0x0, 0xa, 0xfc, 0x28,
    0xef, 0xfd, 0x50, 0x0, 0x0, 0x8f, 0xff, 0xd6,
    0x0, 0x0, 0x70, 0xb, 0xfd, 0x60, 0x0, 0x0,
    0x0, 0x1, 0x8f, 0xf4, 0x0, 0x0, 0x0, 0x2,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x50
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 107, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 150, .box_w = 5, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 45, .adv_w = 168, .box_w = 7, .box_h = 8, .ofs_x = 2, .ofs_y = 11},
    {.bitmap_index = 73, .adv_w = 250, .box_w = 16, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 217, .adv_w = 239, .box_w = 12, .box_h = 24, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 361, .adv_w = 342, .box_w = 19, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 532, .adv_w = 266, .box_w = 16, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 676, .adv_w = 105, .box_w = 3, .box_h = 8, .ofs_x = 2, .ofs_y = 11},
    {.bitmap_index = 688, .adv_w = 149, .box_w = 8, .box_h = 24, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 784, .adv_w = 149, .box_w = 7, .box_h = 24, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 868, .adv_w = 179, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 10},
    {.bitmap_index = 918, .adv_w = 230, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1009, .adv_w = 126, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1030, .adv_w = 183, .box_w = 9, .box_h = 2, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 1039, .adv_w = 126, .box_w = 4, .box_h = 4, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1047, .adv_w = 208, .box_w = 9, .box_h = 19, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 1133, .adv_w = 239, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1250, .adv_w = 239, .box_w = 8, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1322, .adv_w = 239, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1439, .adv_w = 239, .box_w = 12, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1547, .adv_w = 239, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1673, .adv_w = 239, .box_w = 12, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1781, .adv_w = 239, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1898, .adv_w = 239, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2015, .adv_w = 239, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2132, .adv_w = 239, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2249, .adv_w = 157, .box_w = 4, .box_h = 14, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 2277, .adv_w = 157, .box_w = 6, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2331, .adv_w = 230, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 2429, .adv_w = 230, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 2492, .adv_w = 230, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 2590, .adv_w = 179, .box_w = 10, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2680, .adv_w = 358, .box_w = 21, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2901, .adv_w = 277, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3063, .adv_w = 258, .box_w = 13, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3180, .adv_w = 249, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3306, .adv_w = 291, .box_w = 15, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3441, .adv_w = 231, .box_w = 12, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3549, .adv_w = 219, .box_w = 11, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3648, .adv_w = 285, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3783, .adv_w = 295, .box_w = 15, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3918, .adv_w = 114, .box_w = 3, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3945, .adv_w = 113, .box_w = 7, .box_h = 23, .ofs_x = -2, .ofs_y = -5},
    {.bitmap_index = 4026, .adv_w = 255, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4152, .adv_w = 211, .box_w = 11, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4251, .adv_w = 345, .box_w = 18, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4413, .adv_w = 307, .box_w = 15, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4548, .adv_w = 309, .box_w = 17, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4701, .adv_w = 248, .box_w = 13, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4818, .adv_w = 309, .box_w = 17, .box_h = 22, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5005, .adv_w = 255, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5131, .adv_w = 235, .box_w = 13, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5248, .adv_w = 220, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5374, .adv_w = 292, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5500, .adv_w = 257, .box_w = 16, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5644, .adv_w = 374, .box_w = 23, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5851, .adv_w = 260, .box_w = 16, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5995, .adv_w = 247, .box_w = 16, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6139, .adv_w = 260, .box_w = 15, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6274, .adv_w = 122, .box_w = 6, .box_h = 24, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6346, .adv_w = 212, .box_w = 13, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6489, .adv_w = 122, .box_w = 6, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 6561, .adv_w = 230, .box_w = 12, .box_h = 10, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 6621, .adv_w = 208, .box_w = 13, .box_h = 2, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 6634, .adv_w = 144, .box_w = 6, .box_h = 4, .ofs_x = 1, .ofs_y = 16},
    {.bitmap_index = 6646, .adv_w = 255, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6737, .adv_w = 264, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6861, .adv_w = 207, .box_w = 11, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6938, .adv_w = 264, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7071, .adv_w = 240, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7162, .adv_w = 145, .box_w = 10, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7257, .adv_w = 265, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7390, .adv_w = 257, .box_w = 12, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7504, .adv_w = 111, .box_w = 3, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7533, .adv_w = 111, .box_w = 7, .box_h = 24, .ofs_x = -2, .ofs_y = -5},
    {.bitmap_index = 7617, .adv_w = 215, .box_w = 12, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7731, .adv_w = 119, .box_w = 5, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7779, .adv_w = 383, .box_w = 20, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7919, .adv_w = 257, .box_w = 12, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8003, .adv_w = 257, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8101, .adv_w = 264, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 8225, .adv_w = 265, .box_w = 14, .box_h = 19, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 8358, .adv_w = 158, .box_w = 8, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8414, .adv_w = 199, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8484, .adv_w = 140, .box_w = 9, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8561, .adv_w = 255, .box_w = 12, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8645, .adv_w = 219, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8743, .adv_w = 344, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8890, .adv_w = 215, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8988, .adv_w = 219, .box_w = 14, .box_h = 19, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 9121, .adv_w = 210, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9205, .adv_w = 124, .box_w = 6, .box_h = 24, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9277, .adv_w = 76, .box_w = 3, .box_h = 24, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9313, .adv_w = 124, .box_w = 7, .box_h = 24, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 9397, .adv_w = 230, .box_w = 14, .box_h = 5, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 9432, .adv_w = 409, .box_w = 24, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 9696, .adv_w = 409, .box_w = 24, .box_h = 24, .ofs_x = 1, .ofs_y = -3}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0xc50
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 32622, .range_length = 3153, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 2, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    3, 3,
    3, 8,
    3, 13,
    3, 15,
    8, 3,
    8, 8,
    8, 13,
    8, 15,
    9, 75,
    13, 3,
    13, 8,
    13, 18,
    13, 24,
    13, 26,
    15, 3,
    15, 8,
    15, 18,
    15, 24,
    15, 26,
    16, 16,
    18, 13,
    18, 15,
    18, 27,
    18, 28,
    24, 13,
    24, 15,
    24, 27,
    24, 28,
    27, 18,
    27, 24,
    28, 18,
    28, 24,
    34, 3,
    34, 8,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 71,
    34, 77,
    34, 85,
    34, 87,
    34, 90,
    35, 36,
    35, 40,
    35, 48,
    35, 50,
    35, 53,
    35, 55,
    35, 57,
    35, 58,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 56,
    37, 57,
    37, 58,
    37, 59,
    37, 66,
    38, 75,
    39, 13,
    39, 15,
    39, 34,
    39, 66,
    39, 73,
    39, 76,
    40, 55,
    40, 58,
    43, 43,
    44, 34,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 53,
    44, 54,
    44, 55,
    44, 56,
    44, 58,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 77,
    44, 80,
    44, 82,
    44, 84,
    44, 85,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 87,
    45, 88,
    45, 90,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 56,
    48, 57,
    48, 58,
    48, 59,
    48, 66,
    49, 13,
    49, 15,
    49, 34,
    49, 53,
    49, 55,
    49, 56,
    49, 57,
    49, 58,
    49, 59,
    49, 66,
    50, 13,
    50, 15,
    50, 34,
    50, 43,
    50, 53,
    50, 55,
    50, 56,
    50, 57,
    50, 58,
    50, 59,
    50, 66,
    51, 36,
    51, 40,
    51, 48,
    51, 50,
    51, 53,
    51, 54,
    51, 55,
    51, 56,
    51, 57,
    51, 58,
    51, 68,
    51, 69,
    51, 70,
    51, 72,
    51, 80,
    51, 82,
    51, 85,
    51, 86,
    51, 87,
    51, 89,
    51, 90,
    52, 13,
    52, 15,
    52, 52,
    52, 53,
    52, 55,
    52, 56,
    52, 58,
    52, 85,
    52, 87,
    52, 88,
    52, 89,
    52, 90,
    53, 3,
    53, 8,
    53, 13,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 48,
    53, 50,
    53, 52,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 74,
    53, 75,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 85,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    54, 57,
    54, 89,
    55, 13,
    55, 15,
    55, 27,
    55, 28,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 52,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 73,
    55, 76,
    55, 80,
    55, 82,
    55, 84,
    55, 87,
    55, 90,
    56, 13,
    56, 15,
    56, 27,
    56, 28,
    56, 34,
    56, 36,
    56, 40,
    56, 48,
    56, 50,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 84,
    57, 34,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 53,
    57, 54,
    57, 55,
    57, 56,
    57, 58,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 77,
    57, 80,
    57, 82,
    57, 84,
    57, 85,
    57, 86,
    57, 87,
    57, 88,
    57, 90,
    58, 3,
    58, 8,
    58, 13,
    58, 15,
    58, 27,
    58, 28,
    58, 34,
    58, 36,
    58, 40,
    58, 48,
    58, 50,
    58, 52,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 73,
    58, 74,
    58, 76,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    60, 75,
    66, 53,
    66, 55,
    66, 58,
    66, 87,
    66, 88,
    66, 90,
    67, 13,
    67, 15,
    67, 53,
    67, 55,
    67, 56,
    67, 57,
    67, 58,
    67, 87,
    67, 89,
    67, 90,
    70, 13,
    70, 15,
    70, 53,
    70, 58,
    70, 75,
    71, 3,
    71, 8,
    71, 10,
    71, 11,
    71, 13,
    71, 15,
    71, 32,
    71, 53,
    71, 55,
    71, 56,
    71, 57,
    71, 58,
    71, 62,
    71, 66,
    71, 68,
    71, 69,
    71, 70,
    71, 71,
    71, 72,
    71, 73,
    71, 74,
    71, 75,
    71, 76,
    71, 77,
    71, 80,
    71, 82,
    71, 84,
    71, 85,
    71, 94,
    73, 53,
    73, 58,
    73, 87,
    73, 90,
    74, 3,
    74, 8,
    74, 10,
    74, 32,
    74, 53,
    74, 62,
    74, 94,
    75, 53,
    76, 53,
    76, 54,
    76, 56,
    76, 66,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 77,
    76, 80,
    76, 82,
    76, 86,
    77, 71,
    77, 87,
    77, 88,
    77, 90,
    78, 53,
    78, 58,
    78, 87,
    78, 90,
    79, 53,
    79, 58,
    79, 87,
    79, 90,
    80, 13,
    80, 15,
    80, 53,
    80, 55,
    80, 56,
    80, 57,
    80, 58,
    80, 87,
    80, 89,
    80, 90,
    81, 13,
    81, 15,
    81, 53,
    81, 55,
    81, 56,
    81, 57,
    81, 58,
    81, 87,
    81, 89,
    81, 90,
    82, 53,
    82, 58,
    82, 75,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 80,
    83, 82,
    83, 85,
    84, 53,
    84, 55,
    84, 56,
    84, 57,
    84, 58,
    84, 87,
    84, 89,
    84, 90,
    85, 71,
    86, 53,
    86, 58,
    87, 13,
    87, 15,
    87, 53,
    87, 57,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    88, 53,
    88, 57,
    88, 66,
    89, 53,
    89, 54,
    89, 56,
    89, 66,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 77,
    89, 80,
    89, 82,
    89, 86,
    90, 13,
    90, 15,
    90, 53,
    90, 57,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 80,
    90, 82,
    91, 53,
    92, 73,
    92, 75,
    92, 76
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -23, -24, -74, -74, -23, -24, -74, -74,
    25, -67, -67, -58, -17, -16, -67, -67,
    -58, -17, -16, -50, -41, -41, -41, -41,
    -67, -67, -41, -41, -33, -25, -33, -25,
    -42, -42, -8, -8, -8, -8, -33, -4,
    -29, -16, -34, -8, -4, -8, -8, -8,
    -4, -4, -4, -4, -17, -8, -8, -9,
    -25, -25, -8, -15, -12, -8, -8, -17,
    -8, -16, 8, -66, -66, -17, -25, 0,
    0, -5, -9, 3, -8, -8, -8, -8,
    -8, -8, -16, -16, -16, -17, -16, -16,
    -16, -16, -16, -16, -16, -8, -4, -17,
    -25, -17, -25, -41, -41, 8, -25, -25,
    -25, -25, -33, -8, -33, -16, -42, -17,
    -9, -17, -25, -25, -8, -15, -12, -8,
    -8, -17, -8, -16, -83, -83, -25, -12,
    -4, -4, -21, -8, -16, -25, -25, -25,
    -8, 3, -15, -12, -8, -8, -17, -8,
    -16, -6, -6, -6, -6, -17, -4, -9,
    -4, -12, -17, -8, -8, -8, -8, -8,
    -8, -8, -4, -4, 8, -4, -8, -8,
    0, 0, -9, -4, -9, 0, -8, -4,
    0, -8, 1, 1, -64, -64, -33, -8,
    -8, -8, -8, 0, -33, -25, -25, -25,
    -25, -8, -8, -25, -25, -25, -24, -25,
    -25, -25, 8, -33, -16, -8, -16, -16,
    -25, -4, -8, 0, -58, -58, -17, -17,
    -29, -12, -12, -12, -12, -4, -17, -9,
    -9, -9, -9, 0, 0, -9, -9, -9,
    0, 0, -33, -33, -16, -16, -17, -8,
    -8, -8, -8, -17, -8, -8, -8, -8,
    -8, -8, -8, -8, -8, -8, -8, -8,
    -8, -16, -16, -16, -17, -16, -16, -16,
    -16, -16, -16, -16, -8, -4, -17, -25,
    -17, -25, 0, 0, -59, -59, -25, -25,
    -34, -17, -17, -17, -17, 0, -42, -25,
    -25, -25, 0, -25, 0, -8, 0, -17,
    -17, -25, -17, -25, -17, -25, 0, -17,
    -17, -17, -17, -17, -8, -8, -8, -8,
    33, -25, -9, -25, -4, -4, -4, -17,
    -17, -25, -9, -8, -16, -25, -4, -8,
    -4, -8, -8, -25, -17, 8, 1, 1,
    0, 25, 0, 0, 17, 17, 9, 9,
    9, 0, 9, -12, -8, -8, -8, 0,
    -16, -8, -8, -8, -8, -8, -8, -8,
    -8, 0, 1, -24, -16, -4, -4, 1,
    1, 1, 1, -8, 17, 9, -8, -16,
    0, -8, -8, -8, -8, -8, -8, -8,
    -8, -8, -4, -8, -8, -4, -8, -24,
    -16, -4, -4, -24, -16, -4, -4, -17,
    -17, -25, -9, -8, -16, -25, -4, -8,
    -4, -17, -17, -25, -9, -8, -16, -25,
    -4, -8, -4, -8, -8, 8, 1, 1,
    -37, -37, -20, -8, -8, -8, 0, -8,
    -8, 8, -25, -9, -8, -8, -25, -8,
    -4, -8, 0, -17, -8, -37, -37, -16,
    -16, -15, -4, -4, -4, -4, -4, -21,
    -21, -8, -16, -12, -16, 0, -8, -8,
    -8, -8, -8, -8, -8, -8, -8, -4,
    -37, -37, -16, -16, -15, -4, -4, -4,
    -4, -4, -25, 1, 33, 1
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 486,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_AlibabaPuHui26 = {
#else
lv_font_t ui_font_AlibabaPuHui26 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 26,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_ALIBABAPUHUI26*/

