/*******************************************************************************
 * Size: 15 px
 * Bpp: 4
 * Opts: --bpp 4 --size 15 --font E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts/AlibabaPuHuiTi-3-55-Regular.ttf -o E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts\ui_font_AlibabaPuHui15.c --format lvgl -r 0x20-0x7f --symbols 亿连设置媒体主页 --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_ALIBABAPUHUI15
#define UI_FONT_ALIBABAPUHUI15 1
#endif

#if UI_FONT_ALIBABAPUHUI15

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xf, 0x60, 0xf5, 0xf, 0x50, 0xf4, 0xe, 0x40,
    0xe3, 0xd, 0x30, 0x20, 0x2c, 0x63, 0xf8,

    /* U+0022 "\"" */
    0xc9, 0x8e, 0xb8, 0x7c, 0xa7, 0x6b, 0x96, 0x4a,
    0x21, 0x12,

    /* U+0023 "#" */
    0x0, 0xf, 0x40, 0x5e, 0x0, 0x3, 0xf0, 0x9,
    0x90, 0xae, 0xff, 0xee, 0xff, 0xa1, 0x1b, 0x81,
    0x2f, 0x31, 0x0, 0xe5, 0x4, 0xf0, 0x0, 0x1f,
    0x20, 0x7c, 0x0, 0xae, 0xfe, 0xef, 0xfe, 0xa1,
    0x8b, 0x11, 0xe6, 0x11, 0xb, 0x80, 0x1f, 0x20,
    0x0, 0xe4, 0x4, 0xe0, 0x0,

    /* U+0024 "$" */
    0x0, 0x2, 0x50, 0x0, 0x0, 0x4, 0xa0, 0x0,
    0x2, 0xcf, 0xfe, 0xc0, 0xb, 0x95, 0xa1, 0x30,
    0xd, 0x54, 0xa0, 0x0, 0xb, 0xa5, 0xa0, 0x0,
    0x3, 0xdf, 0xd6, 0x0, 0x0, 0x6, 0xdd, 0xd0,
    0x0, 0x4, 0xa0, 0xd5, 0x0, 0x4, 0xa0, 0xb6,
    0x5, 0x25, 0xb5, 0xf3, 0xc, 0xef, 0xfd, 0x60,
    0x0, 0x4, 0xa0, 0x0, 0x0, 0x4, 0xa0, 0x0,

    /* U+0025 "%" */
    0x5d, 0xd9, 0x0, 0xb, 0x40, 0xd, 0x20, 0xd2,
    0x5, 0xa0, 0x0, 0xf0, 0xb, 0x40, 0xd2, 0x0,
    0xd, 0x30, 0xd2, 0x78, 0x0, 0x0, 0x4c, 0xc7,
    0x1d, 0x3b, 0xc6, 0x0, 0x0, 0xa, 0x5c, 0x40,
    0xe1, 0x0, 0x3, 0xc0, 0xf1, 0xb, 0x40, 0x0,
    0xc3, 0xf, 0x10, 0xb4, 0x0, 0x69, 0x0, 0xc2,
    0xd, 0x10, 0xd, 0x10, 0x4, 0xba, 0x80,

    /* U+0026 "&" */
    0x0, 0x1b, 0xfd, 0x40, 0x0, 0x0, 0xac, 0x29,
    0xe0, 0x0, 0x0, 0xd8, 0x5, 0xf0, 0x0, 0x0,
    0xad, 0x4e, 0x70, 0x0, 0x0, 0x6f, 0xe4, 0x0,
    0x10, 0x8, 0xe7, 0xf6, 0x5, 0xf0, 0x3f, 0x30,
    0x5f, 0x4a, 0xa0, 0x5f, 0x0, 0x8, 0xff, 0x30,
    0x2f, 0xa2, 0x28, 0xfe, 0x51, 0x4, 0xcf, 0xea,
    0x38, 0xe9,

    /* U+0027 "'" */
    0xc9, 0xb8, 0xa7, 0x96, 0x21,

    /* U+0028 "(" */
    0x0, 0xe, 0x50, 0x8, 0xb0, 0x0, 0xf4, 0x0,
    0x6d, 0x0, 0xb, 0x90, 0x0, 0xe6, 0x0, 0xf,
    0x40, 0x0, 0xf4, 0x0, 0xe, 0x60, 0x0, 0xb9,
    0x0, 0x7, 0xd0, 0x0, 0x1f, 0x30, 0x0, 0x9b,
    0x0, 0x1, 0xe4,

    /* U+0029 ")" */
    0xd, 0x50, 0x0, 0x5e, 0x0, 0x0, 0xd6, 0x0,
    0x7, 0xd0, 0x0, 0x3f, 0x10, 0x0, 0xf4, 0x0,
    0xe, 0x50, 0x0, 0xe5, 0x0, 0xf, 0x40, 0x3,
    0xf1, 0x0, 0x7c, 0x0, 0xe, 0x60, 0x5, 0xe0,
    0x0, 0xe4, 0x0,

    /* U+002A "*" */
    0x0, 0x6d, 0x0, 0x38, 0x6c, 0x57, 0x3a, 0xef,
    0xc7, 0x2, 0xec, 0x80, 0x8, 0x81, 0xd1, 0x0,
    0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x8, 0xc0, 0x0, 0x0, 0x8, 0xc0, 0x0,
    0x0, 0x8, 0xc0, 0x0, 0x7f, 0xff, 0xff, 0xfc,
    0x13, 0x39, 0xd3, 0x32, 0x0, 0x8, 0xc0, 0x0,
    0x0, 0x8, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+002C "," */
    0x1, 0xf2, 0x4, 0xe0, 0xa, 0x80, 0x2e, 0x0,

    /* U+002D "-" */
    0xdf, 0xff, 0x71, 0x11, 0x10,

    /* U+002E "." */
    0x7c, 0x19, 0xf2,

    /* U+002F "/" */
    0x0, 0x0, 0xf2, 0x0, 0x5, 0xc0, 0x0, 0xb,
    0x60, 0x0, 0x1f, 0x10, 0x0, 0x7b, 0x0, 0x0,
    0xd5, 0x0, 0x3, 0xe0, 0x0, 0x9, 0x90, 0x0,
    0xe, 0x30, 0x0, 0x4d, 0x0, 0x0, 0xa7, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x9d, 0xfc, 0x50, 0x8, 0xe4, 0x27, 0xf2,
    0xe, 0x70, 0x0, 0xd8, 0x1f, 0x40, 0x0, 0x9b,
    0x2f, 0x20, 0x0, 0x8c, 0x2f, 0x20, 0x0, 0x8c,
    0x1f, 0x40, 0x0, 0x9b, 0xe, 0x70, 0x0, 0xd8,
    0x8, 0xe4, 0x27, 0xf2, 0x0, 0x9e, 0xfc, 0x50,

    /* U+0031 "1" */
    0x39, 0xcf, 0x74, 0x74, 0xe7, 0x0, 0xe, 0x70,
    0x0, 0xe7, 0x0, 0xe, 0x70, 0x0, 0xe7, 0x0,
    0xe, 0x70, 0x0, 0xe7, 0x0, 0xe, 0x70, 0x0,
    0xe7,

    /* U+0032 "2" */
    0x5, 0xef, 0xeb, 0x30, 0x1, 0x21, 0x2a, 0xe0,
    0x0, 0x0, 0x1, 0xf3, 0x0, 0x0, 0x2, 0xf2,
    0x0, 0x0, 0x8, 0xd0, 0x0, 0x0, 0x6f, 0x30,
    0x0, 0x7, 0xe4, 0x0, 0x0, 0xad, 0x20, 0x0,
    0xd, 0xc2, 0x11, 0x11, 0x2f, 0xff, 0xff, 0xfb,

    /* U+0033 "3" */
    0x5e, 0xfe, 0xb3, 0x2, 0x21, 0x2a, 0xe0, 0x0,
    0x0, 0x3f, 0x10, 0x0, 0x2b, 0xb0, 0xb, 0xff,
    0xc1, 0x0, 0x0, 0x2a, 0xe0, 0x0, 0x0, 0xf,
    0x50, 0x0, 0x0, 0xf4, 0x32, 0x13, 0xae, 0xd,
    0xff, 0xea, 0x20,

    /* U+0034 "4" */
    0x0, 0x0, 0x5f, 0xb0, 0x0, 0x2, 0xec, 0xb0,
    0x0, 0xd, 0x69, 0xb0, 0x0, 0xaa, 0x9, 0xb0,
    0x7, 0xd0, 0x9, 0xb0, 0x3e, 0x20, 0x9, 0xb0,
    0x7f, 0xff, 0xff, 0xfe, 0x12, 0x22, 0x29, 0xb2,
    0x0, 0x0, 0x9, 0xb0, 0x0, 0x0, 0x9, 0xb0,

    /* U+0035 "5" */
    0xf, 0xff, 0xfe, 0x2, 0xf2, 0x22, 0x20, 0x5d,
    0x0, 0x0, 0x7, 0xb0, 0x0, 0x0, 0x9f, 0xfe,
    0xc4, 0x1, 0x0, 0x17, 0xf3, 0x0, 0x0, 0xc,
    0x80, 0x0, 0x0, 0xd8, 0x22, 0x13, 0x9f, 0x29,
    0xff, 0xea, 0x30,

    /* U+0036 "6" */
    0x0, 0x5, 0xbe, 0x60, 0x0, 0x9d, 0x63, 0x0,
    0x5, 0xd0, 0x0, 0x0, 0xc, 0xad, 0xfd, 0x50,
    0xf, 0xc2, 0x5, 0xf3, 0x1f, 0x40, 0x0, 0xa8,
    0x1f, 0x20, 0x0, 0x9a, 0xf, 0x50, 0x0, 0xb8,
    0x9, 0xe4, 0x27, 0xf2, 0x0, 0x8e, 0xfd, 0x40,

    /* U+0037 "7" */
    0x3f, 0xff, 0xff, 0xfb, 0x2, 0x22, 0x22, 0xb8,
    0x0, 0x0, 0x2, 0xf1, 0x0, 0x0, 0xa, 0x90,
    0x0, 0x0, 0x2f, 0x20, 0x0, 0x0, 0xa9, 0x0,
    0x0, 0x3, 0xf2, 0x0, 0x0, 0xb, 0xa0, 0x0,
    0x0, 0x3f, 0x20, 0x0, 0x0, 0xba, 0x0, 0x0,

    /* U+0038 "8" */
    0x1, 0xae, 0xfd, 0x60, 0xa, 0xb2, 0x14, 0xf4,
    0xd, 0x50, 0x0, 0xc7, 0x8, 0xb1, 0x3, 0xe3,
    0x0, 0xcf, 0xff, 0x70, 0xb, 0xa1, 0x3, 0xe6,
    0x2f, 0x10, 0x0, 0x7c, 0x2f, 0x10, 0x0, 0x7c,
    0xd, 0xa2, 0x14, 0xe8, 0x2, 0xae, 0xfd, 0x80,

    /* U+0039 "9" */
    0x0, 0x9d, 0xfc, 0x40, 0xa, 0xc3, 0x26, 0xf2,
    0xf, 0x30, 0x0, 0xb8, 0x1f, 0x20, 0x0, 0x9a,
    0xd, 0xa1, 0x4, 0xfa, 0x2, 0xbf, 0xfa, 0xd8,
    0x0, 0x0, 0x1, 0xf5, 0x0, 0x0, 0xa, 0xe0,
    0x0, 0x14, 0xcf, 0x30, 0x0, 0xdd, 0x92, 0x0,

    /* U+003A ":" */
    0x1f, 0xa0, 0xc8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0x80, 0xfa,

    /* U+003B ";" */
    0x1, 0xfa, 0x0, 0xc8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0x0, 0xe4,
    0x4, 0xd0, 0xa, 0x40,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x9b,
    0x0, 0x6, 0xcf, 0xa3, 0x29, 0xfd, 0x71, 0x0,
    0x7e, 0x40, 0x0, 0x0, 0x4e, 0xe9, 0x20, 0x0,
    0x0, 0x4a, 0xfc, 0x60, 0x0, 0x0, 0x17, 0xdb,
    0x0, 0x0, 0x0, 0x2,

    /* U+003D "=" */
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfc,
    0x13, 0x33, 0x33, 0x32, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xfc, 0x13, 0x33, 0x33, 0x32,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x7b, 0x40, 0x0, 0x0,
    0x18, 0xee, 0x81, 0x0, 0x0, 0x5, 0xbf, 0xb4,
    0x0, 0x0, 0x2, 0xbc, 0x0, 0x1, 0x7d, 0xf7,
    0x4, 0xaf, 0xc6, 0x0, 0x7e, 0x92, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x4d, 0xff, 0xc4, 0x2, 0x42, 0x3b, 0xf0, 0x0,
    0x0, 0x4f, 0x10, 0x0, 0x7, 0xf0, 0x0, 0x7,
    0xf5, 0x0, 0x6, 0xe3, 0x0, 0x0, 0xb8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc9, 0x0, 0x0,
    0xe, 0xb0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x6b, 0xef, 0xea, 0x30, 0x0, 0x1,
    0xcc, 0x41, 0x2, 0x8f, 0x50, 0x0, 0xd9, 0x0,
    0x0, 0x0, 0x5e, 0x0, 0x7c, 0x0, 0x8e, 0xda,
    0x90, 0xe4, 0xd, 0x50, 0x9c, 0x12, 0xf6, 0xb,
    0x61, 0xf1, 0x1f, 0x20, 0xe, 0x30, 0xc5, 0x2f,
    0x3, 0xf0, 0x2, 0xf0, 0xe, 0x31, 0xf1, 0x1f,
    0x20, 0xbf, 0x27, 0xc0, 0xd, 0x50, 0x7e, 0xd8,
    0x7e, 0xc1, 0x0, 0x7d, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xad, 0x40, 0x0, 0x4, 0x10, 0x0,
    0x0, 0x6c, 0xfe, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x1, 0xff, 0x10, 0x0, 0x0, 0x7, 0xdd,
    0x70, 0x0, 0x0, 0xd, 0x77, 0xe0, 0x0, 0x0,
    0x4f, 0x11, 0xf4, 0x0, 0x0, 0xab, 0x0, 0xba,
    0x0, 0x1, 0xf5, 0x0, 0x5f, 0x10, 0x7, 0xff,
    0xff, 0xff, 0x70, 0xd, 0x92, 0x22, 0x29, 0xd0,
    0x3f, 0x20, 0x0, 0x2, 0xf4, 0xab, 0x0, 0x0,
    0x0, 0xca,

    /* U+0042 "B" */
    0xaf, 0xff, 0xeb, 0x10, 0xac, 0x12, 0x4d, 0xa0,
    0xab, 0x0, 0x7, 0xd0, 0xab, 0x0, 0x1c, 0x90,
    0xaf, 0xef, 0xfc, 0x10, 0xac, 0x12, 0x4c, 0xc0,
    0xab, 0x0, 0x3, 0xf2, 0xab, 0x0, 0x3, 0xf2,
    0xac, 0x22, 0x3b, 0xd0, 0xaf, 0xff, 0xfc, 0x30,

    /* U+0043 "C" */
    0x1, 0x9d, 0xff, 0xd2, 0x1d, 0xd5, 0x32, 0x41,
    0x7f, 0x10, 0x0, 0x0, 0xca, 0x0, 0x0, 0x0,
    0xe8, 0x0, 0x0, 0x0, 0xe8, 0x0, 0x0, 0x0,
    0xc9, 0x0, 0x0, 0x0, 0x9e, 0x0, 0x0, 0x0,
    0x1e, 0xc5, 0x33, 0x52, 0x2, 0xad, 0xff, 0xc3,

    /* U+0044 "D" */
    0xaf, 0xff, 0xec, 0x50, 0xa, 0xc2, 0x24, 0x9f,
    0x70, 0xab, 0x0, 0x0, 0x9f, 0xa, 0xb0, 0x0,
    0x2, 0xf4, 0xab, 0x0, 0x0, 0xf, 0x5a, 0xb0,
    0x0, 0x0, 0xf5, 0xab, 0x0, 0x0, 0x2f, 0x4a,
    0xb0, 0x0, 0x9, 0xe0, 0xac, 0x22, 0x4a, 0xf6,
    0xa, 0xff, 0xfd, 0xb4, 0x0,

    /* U+0045 "E" */
    0xaf, 0xff, 0xff, 0x2a, 0xc2, 0x22, 0x20, 0xab,
    0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0xaf, 0xff,
    0xfd, 0xa, 0xc2, 0x22, 0x10, 0xab, 0x0, 0x0,
    0xa, 0xb0, 0x0, 0x0, 0xac, 0x22, 0x22, 0x1a,
    0xff, 0xff, 0xf8,

    /* U+0046 "F" */
    0xaf, 0xff, 0xff, 0x2a, 0xc2, 0x22, 0x20, 0xab,
    0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0xaf, 0xff,
    0xfd, 0xa, 0xc2, 0x22, 0x20, 0xab, 0x0, 0x0,
    0xa, 0xb0, 0x0, 0x0, 0xab, 0x0, 0x0, 0xa,
    0xb0, 0x0, 0x0,

    /* U+0047 "G" */
    0x1, 0x8c, 0xef, 0xe9, 0x1, 0xdd, 0x63, 0x23,
    0x50, 0x7f, 0x10, 0x0, 0x0, 0xc, 0xa0, 0x0,
    0x0, 0x0, 0xe8, 0x0, 0x4f, 0xff, 0x1e, 0x80,
    0x0, 0x25, 0xf1, 0xc9, 0x0, 0x0, 0x3f, 0x19,
    0xe0, 0x0, 0x3, 0xf1, 0x1e, 0xc5, 0x22, 0x6f,
    0x10, 0x2a, 0xdf, 0xfe, 0xb0,

    /* U+0048 "H" */
    0xab, 0x0, 0x0, 0x2f, 0x3a, 0xb0, 0x0, 0x2,
    0xf3, 0xab, 0x0, 0x0, 0x2f, 0x3a, 0xb0, 0x0,
    0x2, 0xf3, 0xaf, 0xff, 0xff, 0xff, 0x3a, 0xc2,
    0x22, 0x23, 0xf3, 0xab, 0x0, 0x0, 0x2f, 0x3a,
    0xb0, 0x0, 0x2, 0xf3, 0xab, 0x0, 0x0, 0x2f,
    0x3a, 0xb0, 0x0, 0x2, 0xf3,

    /* U+0049 "I" */
    0xab, 0xab, 0xab, 0xab, 0xab, 0xab, 0xab, 0xab,
    0xab, 0xab,

    /* U+004A "J" */
    0x0, 0x9, 0xc0, 0x0, 0x9c, 0x0, 0x9, 0xc0,
    0x0, 0x9c, 0x0, 0x9, 0xc0, 0x0, 0x9c, 0x0,
    0x9, 0xc0, 0x0, 0x9c, 0x0, 0x9, 0xc0, 0x0,
    0x9c, 0x0, 0xa, 0xa0, 0x25, 0xf6, 0x2f, 0xe9,
    0x0,

    /* U+004B "K" */
    0xab, 0x0, 0x5, 0xf5, 0xab, 0x0, 0x4f, 0x60,
    0xab, 0x3, 0xf6, 0x0, 0xab, 0x2e, 0x70, 0x0,
    0xaf, 0xfc, 0x0, 0x0, 0xac, 0x5f, 0x50, 0x0,
    0xab, 0x8, 0xf2, 0x0, 0xab, 0x0, 0xbd, 0x10,
    0xab, 0x0, 0x1e, 0xb0, 0xab, 0x0, 0x3, 0xf8,

    /* U+004C "L" */
    0xab, 0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0xab,
    0x0, 0x0, 0xa, 0xb0, 0x0, 0x0, 0xab, 0x0,
    0x0, 0xa, 0xb0, 0x0, 0x0, 0xab, 0x0, 0x0,
    0xa, 0xb0, 0x0, 0x0, 0xac, 0x22, 0x22, 0xa,
    0xff, 0xff, 0xf4,

    /* U+004D "M" */
    0xaf, 0xc0, 0x0, 0x4, 0xff, 0x1a, 0xdf, 0x20,
    0x0, 0xad, 0xf1, 0xab, 0xb9, 0x0, 0x1f, 0x6f,
    0x1a, 0xb4, 0xf0, 0x8, 0xb4, 0xf1, 0xab, 0xd,
    0x60, 0xe4, 0x4f, 0x1a, 0xb0, 0x7c, 0x5d, 0x4,
    0xf1, 0xab, 0x1, 0xfe, 0x70, 0x4f, 0x1a, 0xb0,
    0x7, 0xb1, 0x4, 0xf1, 0xab, 0x0, 0x0, 0x0,
    0x4f, 0x1a, 0xb0, 0x0, 0x0, 0x4, 0xf1,

    /* U+004E "N" */
    0xaf, 0xc0, 0x0, 0xa, 0xaa, 0xcf, 0x50, 0x0,
    0xaa, 0xab, 0x7e, 0x0, 0xa, 0xaa, 0xb0, 0xd7,
    0x0, 0xaa, 0xab, 0x4, 0xf1, 0xa, 0xaa, 0xb0,
    0xb, 0xa0, 0xaa, 0xab, 0x0, 0x2f, 0x3a, 0xaa,
    0xb0, 0x0, 0x8c, 0xaa, 0xab, 0x0, 0x1, 0xef,
    0xaa, 0xb0, 0x0, 0x6, 0xfa,

    /* U+004F "O" */
    0x3, 0xbe, 0xfe, 0xb4, 0x0, 0x2f, 0xb3, 0x13,
    0xaf, 0x40, 0x9d, 0x0, 0x0, 0xb, 0xb0, 0xd9,
    0x0, 0x0, 0x7, 0xe0, 0xe8, 0x0, 0x0, 0x5,
    0xf0, 0xe7, 0x0, 0x0, 0x5, 0xf0, 0xd9, 0x0,
    0x0, 0x7, 0xe0, 0x9d, 0x0, 0x0, 0xb, 0xb0,
    0x2f, 0xb3, 0x13, 0x9f, 0x40, 0x3, 0xbe, 0xff,
    0xc4, 0x0,

    /* U+0050 "P" */
    0xaf, 0xff, 0xea, 0x20, 0xac, 0x22, 0x5d, 0xc0,
    0xab, 0x0, 0x6, 0xf0, 0xab, 0x0, 0x5, 0xf0,
    0xab, 0x0, 0x1c, 0xc0, 0xaf, 0xff, 0xfc, 0x20,
    0xac, 0x22, 0x0, 0x0, 0xab, 0x0, 0x0, 0x0,
    0xab, 0x0, 0x0, 0x0, 0xab, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x3, 0xbe, 0xfe, 0xb4, 0x0, 0x2f, 0xb3, 0x13,
    0xaf, 0x40, 0x9d, 0x0, 0x0, 0xb, 0xb0, 0xd9,
    0x0, 0x0, 0x7, 0xf0, 0xe8, 0x0, 0x0, 0x5,
    0xf0, 0xe7, 0x0, 0x0, 0x5, 0xf0, 0xd9, 0x0,
    0x0, 0x7, 0xe0, 0x9d, 0x0, 0x0, 0xb, 0xb0,
    0x2f, 0xb3, 0x13, 0x9f, 0x30, 0x3, 0xbe, 0xff,
    0xb3, 0x0, 0x0, 0x0, 0x1e, 0x70, 0x0, 0x0,
    0x0, 0x4, 0xf4, 0x0,

    /* U+0052 "R" */
    0xaf, 0xff, 0xea, 0x10, 0xac, 0x22, 0x5e, 0xb0,
    0xab, 0x0, 0x7, 0xe0, 0xab, 0x0, 0x6, 0xf0,
    0xab, 0x0, 0x2c, 0xb0, 0xaf, 0xff, 0xfb, 0x20,
    0xac, 0x24, 0xf4, 0x0, 0xab, 0x0, 0x7e, 0x10,
    0xab, 0x0, 0xb, 0xc0, 0xab, 0x0, 0x1, 0xe9,

    /* U+0053 "S" */
    0x1, 0xae, 0xfe, 0xc0, 0xb, 0xc4, 0x23, 0x60,
    0xf, 0x60, 0x0, 0x0, 0xd, 0xa0, 0x0, 0x0,
    0x3, 0xde, 0xb6, 0x0, 0x0, 0x2, 0x7d, 0xe2,
    0x0, 0x0, 0x0, 0xd8, 0x0, 0x0, 0x0, 0xb9,
    0x16, 0x32, 0x37, 0xf5, 0x1c, 0xef, 0xfc, 0x60,

    /* U+0054 "T" */
    0xcf, 0xff, 0xff, 0xfa, 0x12, 0x2c, 0xb2, 0x21,
    0x0, 0xb, 0xa0, 0x0, 0x0, 0xb, 0xa0, 0x0,
    0x0, 0xb, 0xa0, 0x0, 0x0, 0xb, 0xa0, 0x0,
    0x0, 0xb, 0xa0, 0x0, 0x0, 0xb, 0xa0, 0x0,
    0x0, 0xb, 0xa0, 0x0, 0x0, 0xb, 0xa0, 0x0,

    /* U+0055 "U" */
    0xaa, 0x0, 0x0, 0x2f, 0x3a, 0xa0, 0x0, 0x2,
    0xf3, 0xaa, 0x0, 0x0, 0x2f, 0x3a, 0xa0, 0x0,
    0x2, 0xf3, 0xaa, 0x0, 0x0, 0x2f, 0x3a, 0xa0,
    0x0, 0x2, 0xf3, 0xab, 0x0, 0x0, 0x2f, 0x29,
    0xd0, 0x0, 0x4, 0xf0, 0x4f, 0x93, 0x25, 0xdb,
    0x0, 0x5c, 0xef, 0xd8, 0x10,

    /* U+0056 "V" */
    0x9d, 0x0, 0x0, 0x8, 0xd0, 0x3f, 0x30, 0x0,
    0xd, 0x70, 0xe, 0x80, 0x0, 0x3f, 0x20, 0x8,
    0xd0, 0x0, 0x8c, 0x0, 0x2, 0xf3, 0x0, 0xe6,
    0x0, 0x0, 0xd8, 0x3, 0xf1, 0x0, 0x0, 0x7e,
    0x9, 0xb0, 0x0, 0x0, 0x1f, 0x3e, 0x50, 0x0,
    0x0, 0xc, 0xdf, 0x0, 0x0, 0x0, 0x6, 0xf9,
    0x0, 0x0,

    /* U+0057 "W" */
    0x7e, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf0, 0x4f,
    0x20, 0x2, 0xd9, 0x0, 0xa, 0xb0, 0xf, 0x50,
    0x6, 0xef, 0x0, 0xd, 0x70, 0xc, 0x90, 0xb,
    0x6f, 0x40, 0x1f, 0x30, 0x8, 0xd0, 0xf, 0x2a,
    0x80, 0x4f, 0x0, 0x4, 0xf1, 0x4d, 0x6, 0xd0,
    0x8b, 0x0, 0x0, 0xf4, 0x98, 0x1, 0xf1, 0xc7,
    0x0, 0x0, 0xc8, 0xd4, 0x0, 0xd6, 0xf3, 0x0,
    0x0, 0x8e, 0xf0, 0x0, 0x8e, 0xf0, 0x0, 0x0,
    0x4f, 0xa0, 0x0, 0x4f, 0xb0, 0x0,

    /* U+0058 "X" */
    0x1f, 0x80, 0x0, 0xd, 0x80, 0x6f, 0x20, 0x8,
    0xd0, 0x0, 0xcb, 0x3, 0xf3, 0x0, 0x2, 0xf5,
    0xd8, 0x0, 0x0, 0x8, 0xfd, 0x0, 0x0, 0x0,
    0xae, 0xf1, 0x0, 0x0, 0x4f, 0x2c, 0xb0, 0x0,
    0x1e, 0x70, 0x2f, 0x50, 0xa, 0xd0, 0x0, 0x8e,
    0x15, 0xf3, 0x0, 0x0, 0xda,

    /* U+0059 "Y" */
    0x9e, 0x0, 0x0, 0x1e, 0x70, 0xe9, 0x0, 0x9,
    0xd0, 0x4, 0xf3, 0x3, 0xf3, 0x0, 0xa, 0xc0,
    0xc8, 0x0, 0x0, 0x1e, 0xcd, 0x0, 0x0, 0x0,
    0x6f, 0x40, 0x0, 0x0, 0x3, 0xf1, 0x0, 0x0,
    0x0, 0x3f, 0x10, 0x0, 0x0, 0x3, 0xf1, 0x0,
    0x0, 0x0, 0x3f, 0x10, 0x0,

    /* U+005A "Z" */
    0xd, 0xff, 0xff, 0xff, 0x60, 0x23, 0x33, 0x39,
    0xf3, 0x0, 0x0, 0x3, 0xf6, 0x0, 0x0, 0x1,
    0xe9, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0,
    0x9e, 0x10, 0x0, 0x0, 0x6f, 0x30, 0x0, 0x0,
    0x3f, 0x50, 0x0, 0x0, 0xe, 0xb3, 0x33, 0x33,
    0x22, 0xff, 0xff, 0xff, 0xfc,

    /* U+005B "[" */
    0xff, 0xef, 0x60, 0xf5, 0xf, 0x50, 0xf5, 0xf,
    0x50, 0xf5, 0xf, 0x50, 0xf5, 0xf, 0x50, 0xf5,
    0xf, 0x50, 0xf6, 0x1f, 0xfe,

    /* U+005C "\\" */
    0x6c, 0x0, 0x0, 0x0, 0xe, 0x30, 0x0, 0x0,
    0x8, 0xb0, 0x0, 0x0, 0x1, 0xf2, 0x0, 0x0,
    0x0, 0x9a, 0x0, 0x0, 0x0, 0x2f, 0x10, 0x0,
    0x0, 0xa, 0x80, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0xc7, 0x0, 0x0, 0x0, 0x4e, 0x0,
    0x0, 0x0, 0xd, 0x60, 0x0, 0x0, 0x6, 0xd0,
    0x0, 0x0, 0x0, 0x71,

    /* U+005D "]" */
    0x7f, 0xf5, 0x1, 0xf5, 0x0, 0xf5, 0x0, 0xf5,
    0x0, 0xf5, 0x0, 0xf5, 0x0, 0xf5, 0x0, 0xf5,
    0x0, 0xf5, 0x0, 0xf5, 0x0, 0xf5, 0x0, 0xf5,
    0x1, 0xf5, 0x7f, 0xf5,

    /* U+005E "^" */
    0x0, 0x6, 0x81, 0x0, 0x0, 0x2f, 0xd7, 0x0,
    0x0, 0x9a, 0x6d, 0x0, 0x0, 0xf4, 0xe, 0x40,
    0x6, 0xd0, 0x8, 0xb0, 0xd, 0x60, 0x2, 0xf2,

    /* U+005F "_" */
    0xee, 0xee, 0xee, 0xe7, 0x11, 0x11, 0x11, 0x10,

    /* U+0060 "`" */
    0xa, 0xc0, 0x0, 0xb7,

    /* U+0061 "a" */
    0x0, 0x3f, 0xfe, 0xa1, 0x0, 0x1, 0x2, 0xd9,
    0x0, 0x0, 0x0, 0x8c, 0x2, 0xad, 0xee, 0xfc,
    0xe, 0xa2, 0x11, 0x8c, 0x1f, 0x30, 0x0, 0x9c,
    0xf, 0x60, 0x6, 0xfc, 0x5, 0xde, 0xe9, 0x6c,

    /* U+0062 "b" */
    0xaa, 0x0, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x0,
    0xaa, 0x0, 0x0, 0x0, 0xaa, 0x8e, 0xfd, 0x50,
    0xae, 0x92, 0x18, 0xf2, 0xad, 0x0, 0x0, 0xe7,
    0xaa, 0x0, 0x0, 0xc9, 0xaa, 0x0, 0x0, 0xc9,
    0xad, 0x0, 0x0, 0xe7, 0xae, 0x92, 0x19, 0xf1,
    0xa8, 0x8e, 0xfd, 0x40,

    /* U+0063 "c" */
    0x0, 0x8d, 0xfe, 0x90, 0x9e, 0x42, 0x24, 0xf,
    0x70, 0x0, 0x1, 0xf4, 0x0, 0x0, 0x1f, 0x40,
    0x0, 0x0, 0xf7, 0x0, 0x0, 0x9, 0xe4, 0x12,
    0x40, 0x8, 0xdf, 0xfa,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x1f, 0x20, 0x0, 0x0, 0x1,
    0xf2, 0x0, 0x0, 0x0, 0x1f, 0x20, 0x9, 0xef,
    0xc4, 0xf2, 0x8, 0xe4, 0x14, 0xcf, 0x20, 0xe7,
    0x0, 0x5, 0xf2, 0xf, 0x50, 0x0, 0x1f, 0x20,
    0xf4, 0x0, 0x1, 0xf2, 0xf, 0x60, 0x0, 0x4f,
    0x20, 0x9d, 0x10, 0x2c, 0xf2, 0x0, 0xae, 0xec,
    0x4f, 0x20,

    /* U+0065 "e" */
    0x0, 0x9d, 0xfe, 0x80, 0x9, 0xd3, 0x13, 0xe6,
    0xf, 0x60, 0x0, 0x8b, 0x1f, 0x98, 0x9a, 0xdc,
    0x1f, 0x74, 0x44, 0x43, 0xf, 0x60, 0x0, 0x0,
    0x9, 0xe5, 0x22, 0x53, 0x0, 0x8d, 0xff, 0xc3,

    /* U+0066 "f" */
    0x0, 0x7e, 0xf3, 0x2, 0xf4, 0x0, 0x5, 0xf0,
    0x0, 0xbf, 0xff, 0xd0, 0x6, 0xe0, 0x0, 0x6,
    0xe0, 0x0, 0x6, 0xe0, 0x0, 0x6, 0xe0, 0x0,
    0x6, 0xe0, 0x0, 0x6, 0xe0, 0x0, 0x6, 0xe0,
    0x0,

    /* U+0067 "g" */
    0x0, 0x8e, 0xfc, 0x4f, 0x20, 0x8e, 0x31, 0x4c,
    0xf2, 0xe, 0x70, 0x0, 0x5f, 0x20, 0xf4, 0x0,
    0x1, 0xf2, 0xf, 0x40, 0x0, 0x1f, 0x20, 0xf7,
    0x0, 0x4, 0xf2, 0x9, 0xe3, 0x13, 0xcf, 0x20,
    0x9, 0xef, 0xd5, 0xf2, 0x0, 0x0, 0x0, 0x5f,
    0x0, 0x2, 0x21, 0x4e, 0x90, 0x0, 0xaf, 0xfd,
    0x80, 0x0,

    /* U+0068 "h" */
    0xaa, 0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0xaa,
    0x0, 0x0, 0xa, 0xa9, 0xef, 0xc2, 0xaf, 0x82,
    0x3c, 0xca, 0xc0, 0x0, 0x6f, 0xaa, 0x0, 0x5,
    0xfa, 0xa0, 0x0, 0x5f, 0xaa, 0x0, 0x5, 0xfa,
    0xa0, 0x0, 0x5f, 0xaa, 0x0, 0x5, 0xf0,

    /* U+0069 "i" */
    0xab, 0x44, 0x0, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa,

    /* U+006A "j" */
    0x0, 0xa, 0xb0, 0x0, 0x44, 0x0, 0x0, 0x0,
    0x0, 0xaa, 0x0, 0xa, 0xa0, 0x0, 0xaa, 0x0,
    0xa, 0xa0, 0x0, 0xaa, 0x0, 0xa, 0xa0, 0x0,
    0xaa, 0x0, 0xa, 0xa0, 0x0, 0xaa, 0x0, 0x2e,
    0x70, 0xff, 0xa0,

    /* U+006B "k" */
    0xaa, 0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0xaa,
    0x0, 0x0, 0xa, 0xa0, 0x8, 0xe2, 0xaa, 0x7,
    0xe2, 0xa, 0xa6, 0xe2, 0x0, 0xaf, 0xf5, 0x0,
    0xa, 0xa8, 0xd1, 0x0, 0xaa, 0xc, 0xb0, 0xa,
    0xa0, 0x1e, 0x80, 0xaa, 0x0, 0x5f, 0x50,

    /* U+006C "l" */
    0xaa, 0x0, 0xaa, 0x0, 0xaa, 0x0, 0xaa, 0x0,
    0xaa, 0x0, 0xaa, 0x0, 0xaa, 0x0, 0xaa, 0x0,
    0xaa, 0x0, 0x9c, 0x10, 0x3e, 0xf0,

    /* U+006D "m" */
    0xaa, 0xae, 0xf9, 0x2b, 0xfe, 0x80, 0xaf, 0x72,
    0x5f, 0xe6, 0x25, 0xf4, 0xab, 0x0, 0xc, 0xa0,
    0x0, 0xd8, 0xaa, 0x0, 0xb, 0x90, 0x0, 0xc8,
    0xaa, 0x0, 0xb, 0x90, 0x0, 0xc8, 0xaa, 0x0,
    0xb, 0x90, 0x0, 0xc8, 0xaa, 0x0, 0xb, 0x90,
    0x0, 0xc8, 0xaa, 0x0, 0xb, 0x90, 0x0, 0xc8,

    /* U+006E "n" */
    0xaa, 0x9e, 0xfc, 0x2a, 0xf8, 0x23, 0xcc, 0xac,
    0x0, 0x6, 0xfa, 0xa0, 0x0, 0x5f, 0xaa, 0x0,
    0x5, 0xfa, 0xa0, 0x0, 0x5f, 0xaa, 0x0, 0x5,
    0xfa, 0xa0, 0x0, 0x5f,

    /* U+006F "o" */
    0x0, 0x8d, 0xfe, 0xa2, 0x0, 0x9e, 0x41, 0x2c,
    0xd0, 0xf, 0x70, 0x0, 0x3f, 0x31, 0xf4, 0x0,
    0x0, 0xf5, 0x1f, 0x40, 0x0, 0xf, 0x50, 0xf7,
    0x0, 0x2, 0xf3, 0x9, 0xe4, 0x12, 0xbd, 0x0,
    0x8, 0xdf, 0xea, 0x10,

    /* U+0070 "p" */
    0xaa, 0x8e, 0xfd, 0x50, 0xae, 0x92, 0x18, 0xf2,
    0xad, 0x0, 0x0, 0xe7, 0xaa, 0x0, 0x0, 0xc9,
    0xaa, 0x0, 0x0, 0xc9, 0xad, 0x0, 0x0, 0xe7,
    0xae, 0x92, 0x19, 0xf1, 0xaa, 0x8e, 0xfd, 0x40,
    0xaa, 0x0, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x0,
    0xaa, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x9e, 0xfc, 0x4f, 0x30, 0x8e, 0x31, 0x4c,
    0xf3, 0xe, 0x70, 0x0, 0x5f, 0x30, 0xf4, 0x0,
    0x1, 0xf3, 0xf, 0x40, 0x0, 0x1f, 0x30, 0xf7,
    0x0, 0x4, 0xf3, 0x9, 0xe3, 0x14, 0xcf, 0x30,
    0xa, 0xef, 0xc4, 0xf3, 0x0, 0x0, 0x0, 0x1f,
    0x30, 0x0, 0x0, 0x1, 0xf3, 0x0, 0x0, 0x0,
    0x1f, 0x30,

    /* U+0072 "r" */
    0xaa, 0x8e, 0x5a, 0xf8, 0x10, 0xac, 0x0, 0xa,
    0xa0, 0x0, 0xaa, 0x0, 0xa, 0xa0, 0x0, 0xaa,
    0x0, 0xa, 0xa0, 0x0,

    /* U+0073 "s" */
    0x3, 0xcf, 0xfc, 0x0, 0xd8, 0x0, 0x20, 0xe,
    0x50, 0x0, 0x0, 0x7f, 0xb6, 0x0, 0x0, 0x16,
    0xbe, 0x0, 0x0, 0x0, 0xf4, 0x2, 0x0, 0x4f,
    0x21, 0xef, 0xfd, 0x60,

    /* U+0074 "t" */
    0x9, 0xb0, 0x0, 0x9b, 0x0, 0xcf, 0xff, 0xb0,
    0x9b, 0x0, 0x9, 0xb0, 0x0, 0x9b, 0x0, 0x9,
    0xb0, 0x0, 0x9b, 0x0, 0x7, 0xe1, 0x0, 0x1c,
    0xfb,

    /* U+0075 "u" */
    0xb9, 0x0, 0x7, 0xdb, 0x90, 0x0, 0x7d, 0xb9,
    0x0, 0x7, 0xdb, 0x90, 0x0, 0x7d, 0xb9, 0x0,
    0x7, 0xda, 0xa0, 0x0, 0x8d, 0x8e, 0x10, 0x4e,
    0xd1, 0xaf, 0xfb, 0x7d,

    /* U+0076 "v" */
    0xaa, 0x0, 0x0, 0xc8, 0x4f, 0x0, 0x2, 0xf2,
    0xe, 0x50, 0x8, 0xc0, 0x8, 0xb0, 0xd, 0x60,
    0x2, 0xf1, 0x3f, 0x0, 0x0, 0xc7, 0x9a, 0x0,
    0x0, 0x6d, 0xe4, 0x0, 0x0, 0xf, 0xe0, 0x0,

    /* U+0077 "w" */
    0x8b, 0x0, 0xb, 0xf1, 0x0, 0x4f, 0x4, 0xf0,
    0x0, 0xfd, 0x60, 0x8, 0xa0, 0xf, 0x40, 0x5d,
    0x7b, 0x0, 0xc6, 0x0, 0xb8, 0x9, 0x82, 0xf0,
    0x1f, 0x10, 0x7, 0xc0, 0xe3, 0xd, 0x55, 0xd0,
    0x0, 0x2f, 0x4e, 0x0, 0x8a, 0x98, 0x0, 0x0,
    0xec, 0x90, 0x3, 0xfd, 0x40, 0x0, 0x9, 0xf4,
    0x0, 0xe, 0xf0, 0x0,

    /* U+0078 "x" */
    0x4f, 0x20, 0x5, 0xe1, 0x9, 0xc0, 0x1e, 0x50,
    0x0, 0xe6, 0xab, 0x0, 0x0, 0x4f, 0xe1, 0x0,
    0x0, 0x6e, 0xf3, 0x0, 0x2, 0xf5, 0x8d, 0x0,
    0xc, 0xa0, 0xd, 0x80, 0x7e, 0x10, 0x4, 0xf3,

    /* U+0079 "y" */
    0xab, 0x0, 0x0, 0xb8, 0x3f, 0x10, 0x1, 0xf3,
    0xd, 0x70, 0x6, 0xd0, 0x6, 0xd0, 0xc, 0x70,
    0x1, 0xf3, 0x2f, 0x10, 0x0, 0xa9, 0x7b, 0x0,
    0x0, 0x3e, 0xd5, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xc, 0x80, 0x0, 0x2, 0x8e, 0x10, 0x0,
    0xf, 0xb2, 0x0, 0x0,

    /* U+007A "z" */
    0xf, 0xff, 0xff, 0xc0, 0x1, 0x11, 0x3f, 0x50,
    0x0, 0x0, 0xd9, 0x0, 0x0, 0xa, 0xc0, 0x0,
    0x0, 0x6e, 0x10, 0x0, 0x3, 0xf4, 0x0, 0x0,
    0x1e, 0x92, 0x22, 0x20, 0x5f, 0xff, 0xff, 0xf0,

    /* U+007B "{" */
    0x0, 0x9e, 0x3, 0xf4, 0x4, 0xf0, 0x5, 0xf0,
    0x5, 0xf0, 0x6, 0xe0, 0x4f, 0x80, 0x9, 0xd0,
    0x5, 0xf0, 0x5, 0xf0, 0x5, 0xf0, 0x4, 0xf0,
    0x2, 0xf4, 0x0, 0x8e,

    /* U+007C "|" */
    0x4f, 0x4f, 0x4f, 0x4f, 0x4f, 0x4f, 0x4f, 0x4f,
    0x4f, 0x4f, 0x4f, 0x4f, 0x4f, 0x4f,

    /* U+007D "}" */
    0x7d, 0x30, 0x1b, 0xb0, 0x8, 0xc0, 0x8, 0xc0,
    0x8, 0xc0, 0x7, 0xd0, 0x1, 0xfb, 0x6, 0xe2,
    0x8, 0xc0, 0x8, 0xc0, 0x8, 0xc0, 0x8, 0xc0,
    0x1b, 0xa0, 0x7c, 0x20,

    /* U+007E "~" */
    0xb, 0xec, 0x60, 0x7a, 0x7c, 0x15, 0xdf, 0xf4,
    0x0, 0x0, 0x1, 0x0,

    /* U+4E3B "主" */
    0x0, 0x0, 0x0, 0x95, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8b, 0x0, 0x0, 0x0, 0x2, 0x44,
    0x44, 0x7f, 0x44, 0x44, 0x41, 0x8, 0xdd, 0xdd,
    0xdf, 0xdd, 0xdd, 0xd4, 0x0, 0x0, 0x0, 0x4f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x0,
    0x0, 0x0, 0x0, 0x22, 0x22, 0x6f, 0x22, 0x22,
    0x20, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x4f, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0x0, 0x0, 0x0, 0x2, 0x22, 0x22,
    0x5f, 0x22, 0x22, 0x21, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff,

    /* U+4EBF "亿" */
    0x0, 0x2, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0xf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0xe, 0x60, 0x22, 0x22, 0x4f, 0x60, 0x0, 0x5,
    0xf0, 0x0, 0x0, 0x1d, 0x90, 0x0, 0x0, 0xee,
    0x0, 0x0, 0xb, 0xc0, 0x0, 0x0, 0x8f, 0xe0,
    0x0, 0x8, 0xe1, 0x0, 0x0, 0x3f, 0x6e, 0x0,
    0x4, 0xf3, 0x0, 0x0, 0x1, 0x62, 0xe0, 0x1,
    0xe7, 0x0, 0x0, 0x0, 0x0, 0x2e, 0x0, 0xab,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xe0, 0x3f, 0x20,
    0x0, 0x1, 0xf0, 0x0, 0x2e, 0x9, 0xa0, 0x0,
    0x0, 0x3f, 0x0, 0x2, 0xe0, 0xba, 0x11, 0x1,
    0x19, 0xc0, 0x0, 0x2e, 0x4, 0xdf, 0xff, 0xff,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+4F53 "体" */
    0x0, 0x3, 0x0, 0x0, 0x70, 0x0, 0x0, 0x0,
    0x0, 0xd3, 0x0, 0x2f, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0x0, 0x2, 0xf0, 0x0, 0x0, 0x0, 0x8,
    0xa2, 0x33, 0x5f, 0x33, 0x33, 0x0, 0x0, 0xe6,
    0x9d, 0xdf, 0xff, 0xdd, 0xd1, 0x0, 0x6f, 0x40,
    0x1, 0xef, 0xd1, 0x0, 0x0, 0xe, 0xf4, 0x0,
    0x7a, 0xf8, 0x70, 0x0, 0x8, 0xdd, 0x40, 0x1e,
    0x3f, 0x1e, 0x10, 0x0, 0x82, 0xc4, 0x9, 0x82,
    0xf0, 0x8b, 0x0, 0x0, 0xc, 0x45, 0xd0, 0x2f,
    0x0, 0xd8, 0x0, 0x0, 0xc8, 0xe4, 0x13, 0xf1,
    0x13, 0xe8, 0x0, 0xc, 0xa5, 0x9f, 0xff, 0xff,
    0x93, 0xb0, 0x0, 0xc4, 0x0, 0x2, 0xf0, 0x0,
    0x0, 0x0, 0xb, 0x40, 0x0, 0x2f, 0x0, 0x0,
    0x0,

    /* U+5A92 "媒" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc2, 0x0, 0x3c, 0x0, 0xe, 0x10, 0x0, 0xe1,
    0x9, 0xdf, 0xcc, 0xcf, 0xc6, 0x16, 0xf6, 0x63,
    0x5d, 0x22, 0x2e, 0x31, 0x4e, 0xfe, 0xf3, 0x3c,
    0x0, 0xe, 0x10, 0x2, 0xc0, 0xc2, 0x3f, 0xdd,
    0xdf, 0x10, 0x5, 0xa0, 0xe0, 0x3c, 0x0, 0xe,
    0x10, 0x7, 0x71, 0xe0, 0x3f, 0xff, 0xff, 0x10,
    0xa, 0x55, 0xa0, 0x0, 0x2e, 0x0, 0x0, 0x6,
    0xdc, 0x6c, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x6f,
    0x60, 0x2, 0xdf, 0xb0, 0x0, 0x0, 0xd8, 0xe5,
    0x5e, 0x6e, 0x6c, 0x30, 0xc, 0x90, 0x6e, 0xb2,
    0x2e, 0x4, 0xd9, 0x4a, 0x0, 0x14, 0x0, 0x2e,
    0x0, 0x4,

    /* U+7F6E "置" */
    0x1, 0xfc, 0xce, 0xdc, 0xed, 0xcd, 0xc0, 0x0,
    0x1f, 0x0, 0xb3, 0x8, 0x70, 0x3c, 0x0, 0x1,
    0xcc, 0xcc, 0xce, 0xcc, 0xcc, 0x90, 0x0, 0x58,
    0x88, 0x89, 0xf8, 0x88, 0x88, 0x20, 0x3, 0x55,
    0x55, 0x7e, 0x55, 0x55, 0x51, 0x0, 0x2, 0xdd,
    0xdd, 0xfd, 0xdd, 0xc0, 0x0, 0x0, 0x2f, 0x44,
    0x44, 0x44, 0x6e, 0x0, 0x0, 0x2, 0xf7, 0x77,
    0x77, 0x79, 0xe0, 0x0, 0x0, 0x2f, 0xbb, 0xbb,
    0xbb, 0xce, 0x0, 0x0, 0x2, 0xe0, 0x0, 0x0,
    0x3, 0xe0, 0x0, 0x0, 0x2f, 0xaa, 0xaa, 0xaa,
    0xbe, 0x0, 0x4, 0xee, 0xfe, 0xee, 0xee, 0xee,
    0xfe, 0xe0,

    /* U+8BBE "设" */
    0x0, 0x51, 0x0, 0x1, 0x11, 0x11, 0x0, 0x0,
    0xa, 0xc0, 0x0, 0xfe, 0xee, 0xf1, 0x0, 0x0,
    0xd, 0xa0, 0xf, 0x20, 0x1f, 0x10, 0x0, 0x0,
    0x28, 0x3, 0xf0, 0x1, 0xf2, 0x0, 0x2, 0x22,
    0x2, 0xd9, 0x0, 0xd, 0xff, 0x23, 0xde, 0xe0,
    0x29, 0x11, 0x11, 0x11, 0x0, 0x0, 0x3e, 0x2,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x3, 0xe0, 0x3,
    0xe1, 0x0, 0x99, 0x0, 0x0, 0x3e, 0x0, 0xa,
    0x90, 0x3f, 0x10, 0x0, 0x3, 0xe0, 0x20, 0x1e,
    0x8e, 0x50, 0x0, 0x0, 0x3e, 0x6f, 0x40, 0x7f,
    0xb0, 0x0, 0x0, 0x4, 0xfe, 0x43, 0xbe, 0x6c,
    0xd4, 0x0, 0x0, 0x6b, 0x4d, 0xe8, 0x0, 0x7,
    0xed, 0x30, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0,
    0x60,

    /* U+8FDE "连" */
    0x0, 0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x1b, 0x0, 0x0, 0x6b, 0x0, 0x0, 0x0, 0x0,
    0xb9, 0x19, 0x9d, 0xc9, 0x99, 0x94, 0x0, 0x1,
    0xf4, 0x69, 0xe6, 0x66, 0x66, 0x30, 0x0, 0x5,
    0x10, 0xb7, 0x2a, 0x0, 0x0, 0x0, 0x23, 0x30,
    0x3f, 0x13, 0xe0, 0x0, 0x0, 0xa, 0xde, 0x9,
    0xec, 0xcf, 0xcc, 0xc0, 0x0, 0x2, 0xe0, 0x24,
    0x47, 0xe4, 0x44, 0x0, 0x0, 0x2e, 0x0, 0x0,
    0x3e, 0x0, 0x0, 0x0, 0x2, 0xe5, 0xcc, 0xcc,
    0xfc, 0xcc, 0xa0, 0x0, 0x2e, 0x14, 0x44, 0x7e,
    0x44, 0x43, 0x0, 0xa, 0xf4, 0x0, 0x3, 0xe0,
    0x0, 0x0, 0x9, 0xd4, 0xea, 0x42, 0x23, 0x1,
    0x12, 0x1, 0xd2, 0x1, 0x9d, 0xef, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+9875 "页" */
    0x2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x8c, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22,
    0xd8, 0x22, 0x22, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x8a, 0x0, 0x1, 0x0,
    0x3f, 0x0, 0x0, 0x8a, 0x0, 0x5c, 0x0, 0x3f,
    0x0, 0x0, 0x8a, 0x0, 0x5c, 0x0, 0x3f, 0x0,
    0x0, 0x8a, 0x0, 0x8a, 0x0, 0x3f, 0x0, 0x0,
    0x8a, 0x0, 0xd8, 0x20, 0x3f, 0x0, 0x0, 0x0,
    0xa, 0xd7, 0xfa, 0x20, 0x0, 0x0, 0x17, 0xec,
    0x10, 0x29, 0xfa, 0x20, 0x2c, 0xfc, 0x50, 0x0,
    0x0, 0x2b, 0xf3, 0x7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x40
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 62, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 87, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15, .adv_w = 97, .box_w = 4, .box_h = 5, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 25, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 70, .adv_w = 138, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 126, .adv_w = 197, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 181, .adv_w = 154, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 231, .adv_w = 61, .box_w = 2, .box_h = 5, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 236, .adv_w = 86, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 271, .adv_w = 86, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 306, .adv_w = 103, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 324, .adv_w = 132, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 356, .adv_w = 72, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 364, .adv_w = 106, .box_w = 5, .box_h = 2, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 369, .adv_w = 72, .box_w = 3, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 372, .adv_w = 120, .box_w = 6, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 405, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 445, .adv_w = 138, .box_w = 5, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 470, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 510, .adv_w = 138, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 545, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 585, .adv_w = 138, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 620, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 660, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 700, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 740, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 780, .adv_w = 91, .box_w = 3, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 792, .adv_w = 91, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 812, .adv_w = 132, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 848, .adv_w = 132, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 872, .adv_w = 132, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 908, .adv_w = 103, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 943, .adv_w = 206, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1028, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1078, .adv_w = 149, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1118, .adv_w = 144, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1158, .adv_w = 168, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1203, .adv_w = 133, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1238, .adv_w = 126, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1273, .adv_w = 164, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1318, .adv_w = 170, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1363, .adv_w = 66, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1373, .adv_w = 65, .box_w = 5, .box_h = 13, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 1406, .adv_w = 147, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1446, .adv_w = 122, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1481, .adv_w = 199, .box_w = 11, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1536, .adv_w = 177, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1581, .adv_w = 178, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1631, .adv_w = 143, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1671, .adv_w = 178, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1731, .adv_w = 147, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1771, .adv_w = 136, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1811, .adv_w = 127, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1851, .adv_w = 168, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1896, .adv_w = 148, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1946, .adv_w = 216, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2016, .adv_w = 150, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2061, .adv_w = 143, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2106, .adv_w = 150, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2151, .adv_w = 71, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2172, .adv_w = 122, .box_w = 8, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2224, .adv_w = 71, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2252, .adv_w = 132, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 2276, .adv_w = 120, .box_w = 8, .box_h = 2, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2284, .adv_w = 83, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 2288, .adv_w = 147, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2320, .adv_w = 152, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2364, .adv_w = 119, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2392, .adv_w = 152, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2442, .adv_w = 139, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2474, .adv_w = 84, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2507, .adv_w = 153, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2557, .adv_w = 148, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2596, .adv_w = 64, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2607, .adv_w = 64, .box_w = 5, .box_h = 14, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 2642, .adv_w = 124, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2681, .adv_w = 69, .box_w = 4, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2703, .adv_w = 221, .box_w = 12, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2751, .adv_w = 148, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2779, .adv_w = 148, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2815, .adv_w = 152, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2859, .adv_w = 153, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2909, .adv_w = 91, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2929, .adv_w = 115, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2957, .adv_w = 81, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2982, .adv_w = 147, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3010, .adv_w = 126, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3042, .adv_w = 199, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3094, .adv_w = 124, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3126, .adv_w = 126, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3170, .adv_w = 121, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3202, .adv_w = 72, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3230, .adv_w = 44, .box_w = 2, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3244, .adv_w = 72, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3272, .adv_w = 132, .box_w = 8, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 3284, .adv_w = 236, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3375, .adv_w = 236, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3480, .adv_w = 236, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3585, .adv_w = 236, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3683, .adv_w = 236, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3773, .adv_w = 236, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3878, .adv_w = 236, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3991, .adv_w = 236, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x84, 0x118, 0xc57, 0x3133, 0x3d83, 0x41a3, 0x4a3a
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20027, .range_length = 19003, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 8, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    3, 3,
    3, 8,
    3, 13,
    3, 15,
    8, 3,
    8, 8,
    8, 13,
    8, 15,
    9, 75,
    13, 3,
    13, 8,
    13, 18,
    13, 24,
    13, 26,
    15, 3,
    15, 8,
    15, 18,
    15, 24,
    15, 26,
    16, 16,
    18, 13,
    18, 15,
    18, 27,
    18, 28,
    24, 13,
    24, 15,
    24, 27,
    24, 28,
    27, 18,
    27, 24,
    28, 18,
    28, 24,
    34, 3,
    34, 8,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 71,
    34, 77,
    34, 85,
    34, 87,
    34, 90,
    35, 36,
    35, 40,
    35, 48,
    35, 50,
    35, 53,
    35, 55,
    35, 57,
    35, 58,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 56,
    37, 57,
    37, 58,
    37, 59,
    37, 66,
    38, 75,
    39, 13,
    39, 15,
    39, 34,
    39, 66,
    39, 73,
    39, 76,
    40, 55,
    40, 58,
    43, 43,
    44, 34,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 53,
    44, 54,
    44, 55,
    44, 56,
    44, 58,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 77,
    44, 80,
    44, 82,
    44, 84,
    44, 85,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 87,
    45, 88,
    45, 90,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 56,
    48, 57,
    48, 58,
    48, 59,
    48, 66,
    49, 13,
    49, 15,
    49, 34,
    49, 53,
    49, 55,
    49, 56,
    49, 57,
    49, 58,
    49, 59,
    49, 66,
    50, 13,
    50, 15,
    50, 34,
    50, 43,
    50, 53,
    50, 55,
    50, 56,
    50, 57,
    50, 58,
    50, 59,
    50, 66,
    51, 36,
    51, 40,
    51, 48,
    51, 50,
    51, 53,
    51, 54,
    51, 55,
    51, 56,
    51, 57,
    51, 58,
    51, 68,
    51, 69,
    51, 70,
    51, 72,
    51, 80,
    51, 82,
    51, 85,
    51, 86,
    51, 87,
    51, 89,
    51, 90,
    52, 13,
    52, 15,
    52, 52,
    52, 53,
    52, 55,
    52, 56,
    52, 58,
    52, 85,
    52, 87,
    52, 88,
    52, 89,
    52, 90,
    53, 3,
    53, 8,
    53, 13,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 48,
    53, 50,
    53, 52,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 74,
    53, 75,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 85,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    54, 57,
    54, 89,
    55, 13,
    55, 15,
    55, 27,
    55, 28,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 52,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 73,
    55, 76,
    55, 80,
    55, 82,
    55, 84,
    55, 87,
    55, 90,
    56, 13,
    56, 15,
    56, 27,
    56, 28,
    56, 34,
    56, 36,
    56, 40,
    56, 48,
    56, 50,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 84,
    57, 34,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 53,
    57, 54,
    57, 55,
    57, 56,
    57, 58,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 77,
    57, 80,
    57, 82,
    57, 84,
    57, 85,
    57, 86,
    57, 87,
    57, 88,
    57, 90,
    58, 3,
    58, 8,
    58, 13,
    58, 15,
    58, 27,
    58, 28,
    58, 34,
    58, 36,
    58, 40,
    58, 48,
    58, 50,
    58, 52,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 73,
    58, 74,
    58, 76,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    60, 75,
    66, 53,
    66, 55,
    66, 58,
    66, 87,
    66, 88,
    66, 90,
    67, 13,
    67, 15,
    67, 53,
    67, 55,
    67, 56,
    67, 57,
    67, 58,
    67, 87,
    67, 89,
    67, 90,
    70, 13,
    70, 15,
    70, 53,
    70, 58,
    70, 75,
    71, 3,
    71, 8,
    71, 10,
    71, 11,
    71, 13,
    71, 15,
    71, 32,
    71, 53,
    71, 55,
    71, 56,
    71, 57,
    71, 58,
    71, 62,
    71, 66,
    71, 68,
    71, 69,
    71, 70,
    71, 71,
    71, 72,
    71, 73,
    71, 74,
    71, 75,
    71, 76,
    71, 77,
    71, 80,
    71, 82,
    71, 84,
    71, 85,
    71, 94,
    73, 53,
    73, 58,
    73, 87,
    73, 90,
    74, 3,
    74, 8,
    74, 10,
    74, 32,
    74, 53,
    74, 62,
    74, 94,
    75, 53,
    76, 53,
    76, 54,
    76, 56,
    76, 66,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 77,
    76, 80,
    76, 82,
    76, 86,
    77, 71,
    77, 87,
    77, 88,
    77, 90,
    78, 53,
    78, 58,
    78, 87,
    78, 90,
    79, 53,
    79, 58,
    79, 87,
    79, 90,
    80, 13,
    80, 15,
    80, 53,
    80, 55,
    80, 56,
    80, 57,
    80, 58,
    80, 87,
    80, 89,
    80, 90,
    81, 13,
    81, 15,
    81, 53,
    81, 55,
    81, 56,
    81, 57,
    81, 58,
    81, 87,
    81, 89,
    81, 90,
    82, 53,
    82, 58,
    82, 75,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 80,
    83, 82,
    83, 85,
    84, 53,
    84, 55,
    84, 56,
    84, 57,
    84, 58,
    84, 87,
    84, 89,
    84, 90,
    85, 71,
    86, 53,
    86, 58,
    87, 13,
    87, 15,
    87, 53,
    87, 57,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    88, 53,
    88, 57,
    88, 66,
    89, 53,
    89, 54,
    89, 56,
    89, 66,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 77,
    89, 80,
    89, 82,
    89, 86,
    90, 13,
    90, 15,
    90, 53,
    90, 57,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 80,
    90, 82,
    91, 53,
    92, 73,
    92, 75,
    92, 76
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -13, -14, -43, -43, -13, -14, -43, -43,
    14, -38, -38, -34, -10, -9, -38, -38,
    -34, -10, -9, -29, -24, -24, -24, -24,
    -38, -38, -24, -24, -19, -14, -19, -14,
    -24, -24, -5, -5, -5, -5, -19, -2,
    -17, -9, -19, -5, -2, -5, -5, -5,
    -2, -2, -2, -2, -10, -5, -5, -5,
    -14, -14, -5, -9, -7, -5, -5, -10,
    -5, -9, 5, -38, -38, -10, -14, 0,
    0, -3, -5, 2, -5, -5, -5, -5,
    -5, -5, -9, -9, -9, -10, -9, -9,
    -9, -9, -9, -9, -9, -5, -2, -10,
    -14, -10, -14, -24, -24, 5, -14, -14,
    -14, -14, -19, -5, -19, -9, -24, -10,
    -5, -10, -14, -14, -5, -9, -7, -5,
    -5, -10, -5, -9, -48, -48, -14, -7,
    -2, -2, -12, -5, -9, -14, -14, -14,
    -5, 2, -9, -7, -5, -5, -10, -5,
    -9, -4, -4, -4, -4, -10, -2, -5,
    -2, -7, -10, -5, -5, -5, -5, -5,
    -5, -5, -2, -2, 5, -2, -5, -5,
    0, 0, -5, -2, -5, 0, -5, -2,
    0, -5, 0, 0, -37, -37, -19, -5,
    -5, -5, -5, 0, -19, -14, -14, -14,
    -14, -5, -5, -14, -14, -14, -14, -14,
    -14, -14, 5, -19, -9, -5, -9, -9,
    -14, -2, -5, 0, -34, -34, -10, -10,
    -17, -7, -7, -7, -7, -2, -10, -5,
    -5, -5, -5, 0, 0, -5, -5, -5,
    0, 0, -19, -19, -9, -9, -10, -5,
    -5, -5, -5, -10, -5, -5, -5, -5,
    -5, -5, -5, -5, -5, -5, -5, -5,
    -5, -9, -9, -9, -10, -9, -9, -9,
    -9, -9, -9, -9, -5, -2, -10, -14,
    -10, -14, 0, 0, -34, -34, -14, -14,
    -19, -10, -10, -10, -10, 0, -24, -14,
    -14, -14, 0, -14, 0, -5, 0, -10,
    -10, -14, -10, -14, -10, -15, 0, -10,
    -10, -10, -10, -10, -5, -5, -5, -5,
    19, -14, -5, -15, -2, -2, -2, -10,
    -10, -14, -5, -5, -9, -14, -2, -5,
    -2, -5, -5, -14, -10, 5, 1, 1,
    0, 15, 0, 0, 10, 10, 5, 5,
    5, 0, 5, -7, -5, -5, -5, 0,
    -9, -5, -5, -5, -5, -5, -5, -5,
    -5, 0, 1, -14, -9, -2, -2, 0,
    0, 0, 1, -5, 10, 5, -5, -9,
    0, -5, -5, -5, -5, -5, -5, -5,
    -5, -5, -2, -5, -5, -2, -5, -14,
    -9, -2, -2, -14, -9, -2, -2, -10,
    -10, -14, -5, -5, -9, -14, -2, -5,
    -2, -10, -10, -14, -5, -5, -9, -14,
    -2, -5, -2, -5, -5, 5, 0, 0,
    -22, -22, -12, -5, -5, -5, 0, -5,
    -5, 5, -14, -5, -5, -5, -14, -5,
    -2, -5, 0, -10, -5, -22, -22, -9,
    -9, -8, -2, -2, -2, -2, -2, -12,
    -12, -5, -9, -7, -9, 0, -5, -5,
    -5, -5, -5, -5, -5, -5, -5, -2,
    -22, -22, -9, -9, -8, -2, -2, -2,
    -2, -2, -14, 0, 19, 0
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 486,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_AlibabaPuHui15 = {
#else
lv_font_t ui_font_AlibabaPuHui15 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 17,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_ALIBABAPUHUI15*/

