/*******************************************************************************
 * Size: 14 px
 * Bpp: 4
 * Opts: --bpp 4 --size 14 --font E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts/AlibabaPuHuiTi-3-55-Regular.ttf -o E:/PROJECT/LVGL/prj_ebike_x1/assets/fonts\ui_font_AlibabaPuHui14.c --format lvgl -r 0x20-0x7f --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_ALIBABAPUHUI14
#define UI_FONT_ALIBABAPUHUI14 1
#endif

#if UI_FONT_ALIBABAPUHUI14

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x2f, 0x22, 0xf2, 0x1f, 0x11, 0xf1, 0xf, 0x0,
    0xf0, 0xf, 0x0, 0x20, 0x3d, 0x34, 0xf4,

    /* U+0022 "\"" */
    0xe7, 0xc8, 0xc5, 0xb7, 0xb4, 0x96, 0xa3, 0x85,
    0x20, 0x21,

    /* U+0023 "#" */
    0x0, 0x2f, 0x0, 0xc5, 0x0, 0x6, 0xb0, 0xf,
    0x10, 0xae, 0xff, 0xee, 0xfe, 0x21, 0x1e, 0x51,
    0x8b, 0x10, 0x0, 0xf1, 0xa, 0x70, 0x0, 0x3e,
    0x0, 0xd5, 0x0, 0xae, 0xfe, 0xef, 0xee, 0x21,
    0xa9, 0x14, 0xe1, 0x10, 0xc, 0x50, 0x6b, 0x0,
    0x0, 0xf2, 0x9, 0x80, 0x0,

    /* U+0024 "$" */
    0x0, 0x4, 0x20, 0x0, 0x0, 0x85, 0x0, 0x3,
    0xdf, 0xfe, 0x60, 0xc7, 0x95, 0x22, 0xe, 0x28,
    0x50, 0x0, 0xd7, 0x85, 0x0, 0x4, 0xef, 0xb3,
    0x0, 0x0, 0x9b, 0xf6, 0x0, 0x8, 0x54, 0xd0,
    0x0, 0x85, 0x2f, 0x4, 0x29, 0x79, 0xb1, 0xdf,
    0xff, 0xb2, 0x0, 0x8, 0x50, 0x0, 0x0, 0x85,
    0x0,

    /* U+0025 "%" */
    0x6, 0xdd, 0x40, 0x4, 0xa0, 0x0, 0xe0, 0x2c,
    0x0, 0xc2, 0x0, 0xe, 0x0, 0xe0, 0x68, 0x0,
    0x0, 0xd1, 0x2c, 0xd, 0x10, 0x0, 0x5, 0xcc,
    0x37, 0x78, 0xc9, 0x0, 0x0, 0x1, 0xd4, 0xb0,
    0x95, 0x0, 0x0, 0x86, 0x68, 0x6, 0x80, 0x0,
    0x1c, 0x6, 0x80, 0x68, 0x0, 0xa, 0x40, 0x4a,
    0x8, 0x50, 0x3, 0xb0, 0x0, 0xaa, 0xb0,

    /* U+0026 "&" */
    0x0, 0x3c, 0xfa, 0x0, 0x0, 0xd, 0x92, 0xd7,
    0x0, 0x0, 0xf4, 0xb, 0x80, 0x0, 0xc, 0x97,
    0xe2, 0x0, 0x0, 0x9f, 0xc1, 0x1, 0x0, 0xad,
    0x9e, 0x10, 0xd5, 0x4f, 0x10, 0xac, 0x3f, 0x16,
    0xd0, 0x0, 0xdf, 0xa0, 0x2f, 0x82, 0x4c, 0xfa,
    0x20, 0x5d, 0xfd, 0x84, 0xce,

    /* U+0027 "'" */
    0xe7, 0xc5, 0xb4, 0xa3, 0x20,

    /* U+0028 "(" */
    0x0, 0x2e, 0x10, 0xb, 0x70, 0x2, 0xf1, 0x0,
    0x8a, 0x0, 0xc, 0x60, 0x0, 0xf3, 0x0, 0xf,
    0x20, 0x1, 0xf2, 0x0, 0xf, 0x30, 0x0, 0xd6,
    0x0, 0x8, 0xa0, 0x0, 0x2f, 0x0, 0x0, 0xb7,
    0x0, 0x2, 0xe1,

    /* U+0029 ")" */
    0xe, 0x30, 0x0, 0x7c, 0x0, 0x0, 0xf3, 0x0,
    0xa, 0x90, 0x0, 0x6d, 0x0, 0x3, 0xf0, 0x0,
    0x2f, 0x10, 0x2, 0xf1, 0x0, 0x3f, 0x0, 0x6,
    0xc0, 0x0, 0xa8, 0x0, 0x1f, 0x20, 0x7, 0xb0,
    0x1, 0xe2, 0x0,

    /* U+002A "*" */
    0x0, 0x89, 0x0, 0x37, 0x88, 0x74, 0x4a, 0xff,
    0xa4, 0x3, 0xed, 0x40, 0xa, 0x55, 0xa0, 0x0,
    0x0, 0x0,

    /* U+002B "+" */
    0x0, 0xb, 0x70, 0x0, 0x0, 0xb, 0x70, 0x0,
    0x0, 0xb, 0x70, 0x0, 0x8f, 0xff, 0xff, 0xf3,
    0x13, 0x3c, 0x93, 0x30, 0x0, 0xb, 0x70, 0x0,
    0x0, 0xb, 0x70, 0x0,

    /* U+002C "," */
    0x3, 0xf0, 0x6, 0xb0, 0xc, 0x50, 0x3d, 0x0,

    /* U+002D "-" */
    0xef, 0xff, 0x21, 0x11, 0x10,

    /* U+002E "." */
    0x8b, 0xa, 0xe0,

    /* U+002F "/" */
    0x0, 0x5, 0xb0, 0x0, 0xa6, 0x0, 0xf, 0x10,
    0x5, 0xb0, 0x0, 0xb6, 0x0, 0x1f, 0x10, 0x6,
    0xb0, 0x0, 0xb5, 0x0, 0x1f, 0x0, 0x6, 0xa0,
    0x0, 0xc5, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0xae, 0xea, 0x10, 0x9, 0xc3, 0x3c, 0xa0,
    0xf, 0x40, 0x3, 0xf0, 0x2f, 0x10, 0x0, 0xf3,
    0x3f, 0x0, 0x0, 0xf4, 0x3f, 0x0, 0x0, 0xf4,
    0x2f, 0x10, 0x0, 0xf3, 0xf, 0x40, 0x3, 0xf0,
    0xa, 0xd2, 0x2c, 0xa0, 0x1, 0xae, 0xea, 0x10,

    /* U+0031 "1" */
    0x49, 0xdf, 0x15, 0x76, 0xf1, 0x0, 0x2f, 0x10,
    0x2, 0xf1, 0x0, 0x2f, 0x10, 0x2, 0xf1, 0x0,
    0x2f, 0x10, 0x2, 0xf1, 0x0, 0x2f, 0x10, 0x2,
    0xf1,

    /* U+0032 "2" */
    0x6, 0xef, 0xe9, 0x0, 0x2, 0x21, 0x4e, 0x70,
    0x0, 0x0, 0x7, 0xb0, 0x0, 0x0, 0x8, 0xa0,
    0x0, 0x0, 0xd, 0x60, 0x0, 0x0, 0xab, 0x0,
    0x0, 0xa, 0xc1, 0x0, 0x1, 0xcb, 0x0, 0x0,
    0x1d, 0xa1, 0x11, 0x10, 0x3f, 0xff, 0xff, 0xf3,

    /* U+0033 "3" */
    0x6, 0xef, 0xe8, 0x0, 0x22, 0x13, 0xd7, 0x0,
    0x0, 0x9, 0x90, 0x0, 0x3, 0xe5, 0x0, 0xcf,
    0xf7, 0x0, 0x1, 0x25, 0xe6, 0x0, 0x0, 0x6,
    0xd0, 0x0, 0x0, 0x6d, 0x3, 0x11, 0x4e, 0x80,
    0xef, 0xfd, 0x80,

    /* U+0034 "4" */
    0x0, 0x0, 0x9f, 0x40, 0x0, 0x5, 0xcf, 0x40,
    0x0, 0x2e, 0x2e, 0x40, 0x0, 0xc6, 0xe, 0x40,
    0x9, 0xa0, 0xe, 0x40, 0x4e, 0x0, 0xe, 0x40,
    0x8f, 0xff, 0xff, 0xf6, 0x12, 0x22, 0x2e, 0x50,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0xe, 0x40,

    /* U+0035 "5" */
    0x2f, 0xff, 0xf6, 0x4, 0xd2, 0x22, 0x0, 0x6a,
    0x0, 0x0, 0x8, 0x80, 0x0, 0x0, 0xaf, 0xfe,
    0x91, 0x1, 0x0, 0x2b, 0xb0, 0x0, 0x0, 0x3f,
    0x0, 0x0, 0x3, 0xf0, 0x21, 0x14, 0xd9, 0xb,
    0xff, 0xd8, 0x0,

    /* U+0036 "6" */
    0x0, 0x7, 0xcf, 0x0, 0x0, 0xbc, 0x52, 0x0,
    0x7, 0xb0, 0x0, 0x0, 0xd, 0x8c, 0xd9, 0x10,
    0x1f, 0xb3, 0x2b, 0xb0, 0x2f, 0x20, 0x1, 0xf0,
    0x2f, 0x0, 0x0, 0xf2, 0x1f, 0x30, 0x2, 0xf0,
    0xb, 0xc2, 0x2b, 0xa0, 0x1, 0xae, 0xfa, 0x0,

    /* U+0037 "7" */
    0x4f, 0xff, 0xff, 0xf2, 0x2, 0x22, 0x23, 0xf1,
    0x0, 0x0, 0x8, 0x90, 0x0, 0x0, 0x1f, 0x20,
    0x0, 0x0, 0x8b, 0x0, 0x0, 0x0, 0xe3, 0x0,
    0x0, 0x7, 0xc0, 0x0, 0x0, 0xe, 0x50, 0x0,
    0x0, 0x6d, 0x0, 0x0, 0x0, 0xd6, 0x0, 0x0,

    /* U+0038 "8" */
    0x2, 0xbe, 0xeb, 0x20, 0xc, 0x91, 0x19, 0xc0,
    0xf, 0x30, 0x2, 0xf0, 0xa, 0x90, 0x8, 0xb0,
    0x1, 0xdf, 0xfe, 0x20, 0xc, 0x80, 0x7, 0xd0,
    0x3f, 0x0, 0x0, 0xe4, 0x3f, 0x0, 0x0, 0xe4,
    0xe, 0x82, 0x18, 0xe1, 0x3, 0xbf, 0xfc, 0x30,

    /* U+0039 "9" */
    0x1, 0xae, 0xea, 0x10, 0xc, 0xa2, 0x2b, 0xa0,
    0x1f, 0x10, 0x2, 0xf0, 0x2f, 0x0, 0x0, 0xf2,
    0xe, 0x80, 0x19, 0xf2, 0x3, 0xcf, 0xd9, 0xf0,
    0x0, 0x0, 0x6, 0xd0, 0x0, 0x0, 0x1e, 0x70,
    0x0, 0x25, 0xeb, 0x0, 0x0, 0xfd, 0x70, 0x0,

    /* U+003A ":" */
    0x2f, 0x61, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1c, 0x52, 0xf6,

    /* U+003B ";" */
    0x2, 0xf6, 0x1, 0xc5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc4, 0x0, 0xf1,
    0x6, 0xa0, 0xb, 0x20,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xc3,
    0x0, 0x17, 0xee, 0x70, 0x2a, 0xfc, 0x50, 0x0,
    0x8d, 0x20, 0x0, 0x0, 0x5e, 0xe7, 0x10, 0x0,
    0x0, 0x5c, 0xf9, 0x30, 0x0, 0x0, 0x39, 0xf3,
    0x0, 0x0, 0x0, 0x11,

    /* U+003D "=" */
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xf3,
    0x13, 0x33, 0x33, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xf3, 0x13, 0x33, 0x33, 0x30,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x7a, 0x30, 0x0, 0x0,
    0x29, 0xfc, 0x50, 0x0, 0x0, 0x17, 0xde, 0x81,
    0x0, 0x0, 0x5, 0xf3, 0x0, 0x2, 0x9f, 0xc2,
    0x5, 0xbf, 0xa3, 0x0, 0x8e, 0x71, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x4e, 0xfe, 0xb1, 0x24, 0x24, 0xe8, 0x0, 0x0,
    0x9a, 0x0, 0x0, 0xc9, 0x0, 0xa, 0xd1, 0x0,
    0x9c, 0x10, 0x0, 0xd4, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xc5, 0x0, 0x1, 0xf7, 0x0,

    /* U+0040 "@" */
    0x0, 0x1, 0x8d, 0xff, 0xc5, 0x0, 0x0, 0x3e,
    0x93, 0x1, 0x5e, 0x80, 0x1, 0xe5, 0x0, 0x0,
    0x2, 0xf3, 0x9, 0x90, 0x2b, 0xe9, 0xe0, 0xa7,
    0xe, 0x20, 0xd6, 0xa, 0xc0, 0x89, 0x2f, 0x4,
    0xd0, 0x7, 0x90, 0x88, 0x3e, 0x6, 0xa0, 0xa,
    0x60, 0xa5, 0x2f, 0x5, 0xc0, 0x4f, 0x83, 0xe0,
    0xe, 0x20, 0xbe, 0xc5, 0xdd, 0x40, 0x8, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xca, 0x30, 0x0,
    0x23, 0x0, 0x0, 0x8, 0xdf, 0xef, 0xd5, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x5, 0xfb, 0x0, 0x0, 0x0, 0xb, 0xaf,
    0x10, 0x0, 0x0, 0x1f, 0x2c, 0x60, 0x0, 0x0,
    0x7c, 0x7, 0xc0, 0x0, 0x0, 0xd7, 0x2, 0xf2,
    0x0, 0x2, 0xf1, 0x0, 0xc8, 0x0, 0x8, 0xff,
    0xff, 0xfe, 0x0, 0xe, 0x72, 0x22, 0x3f, 0x40,
    0x4f, 0x0, 0x0, 0xb, 0xa0, 0xaa, 0x0, 0x0,
    0x5, 0xf0,

    /* U+0042 "B" */
    0xbf, 0xff, 0xd6, 0xb, 0x91, 0x27, 0xf2, 0xb8,
    0x0, 0xe, 0x4b, 0x80, 0x4, 0xf1, 0xbf, 0xef,
    0xf6, 0xb, 0x91, 0x26, 0xf4, 0xb8, 0x0, 0xa,
    0x9b, 0x80, 0x0, 0xa9, 0xb9, 0x22, 0x5f, 0x5b,
    0xff, 0xfe, 0x80,

    /* U+0043 "C" */
    0x2, 0xad, 0xfe, 0x82, 0xeb, 0x42, 0x34, 0x9d,
    0x0, 0x0, 0xd, 0x70, 0x0, 0x0, 0xf5, 0x0,
    0x0, 0xf, 0x50, 0x0, 0x0, 0xd7, 0x0, 0x0,
    0xa, 0xc0, 0x0, 0x0, 0x3f, 0xa4, 0x34, 0x50,
    0x3b, 0xef, 0xe8,

    /* U+0044 "D" */
    0xbf, 0xff, 0xd9, 0x10, 0xb9, 0x23, 0x5d, 0xd0,
    0xb8, 0x0, 0x1, 0xf6, 0xb8, 0x0, 0x0, 0xba,
    0xb8, 0x0, 0x0, 0x9c, 0xb8, 0x0, 0x0, 0x9b,
    0xb8, 0x0, 0x0, 0xba, 0xb8, 0x0, 0x1, 0xf6,
    0xb9, 0x23, 0x6e, 0xc0, 0xbf, 0xff, 0xd8, 0x0,

    /* U+0045 "E" */
    0xbf, 0xff, 0xfb, 0xb, 0x92, 0x22, 0x10, 0xb8,
    0x0, 0x0, 0xb, 0x80, 0x0, 0x0, 0xbf, 0xff,
    0xf6, 0xb, 0x92, 0x22, 0x0, 0xb8, 0x0, 0x0,
    0xb, 0x80, 0x0, 0x0, 0xb9, 0x22, 0x22, 0xb,
    0xff, 0xff, 0xf0,

    /* U+0046 "F" */
    0xbf, 0xff, 0xfb, 0xb9, 0x22, 0x21, 0xb8, 0x0,
    0x0, 0xb8, 0x0, 0x0, 0xbf, 0xff, 0xf6, 0xb9,
    0x22, 0x21, 0xb8, 0x0, 0x0, 0xb8, 0x0, 0x0,
    0xb8, 0x0, 0x0, 0xb8, 0x0, 0x0,

    /* U+0047 "G" */
    0x2, 0xad, 0xff, 0xc2, 0x1e, 0xc4, 0x22, 0x41,
    0x9c, 0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0,
    0xf5, 0x0, 0xaf, 0xf7, 0xf5, 0x0, 0x12, 0xc7,
    0xd7, 0x0, 0x0, 0xb7, 0xac, 0x0, 0x0, 0xb7,
    0x3f, 0xa4, 0x23, 0xc7, 0x3, 0xbe, 0xfe, 0xc4,

    /* U+0048 "H" */
    0xb8, 0x0, 0x0, 0xaa, 0xb8, 0x0, 0x0, 0xaa,
    0xb8, 0x0, 0x0, 0xaa, 0xb8, 0x0, 0x0, 0xaa,
    0xbf, 0xff, 0xff, 0xfa, 0xb9, 0x22, 0x22, 0xba,
    0xb8, 0x0, 0x0, 0xaa, 0xb8, 0x0, 0x0, 0xaa,
    0xb8, 0x0, 0x0, 0xaa, 0xb8, 0x0, 0x0, 0xaa,

    /* U+0049 "I" */
    0xb8, 0xb8, 0xb8, 0xb8, 0xb8, 0xb8, 0xb8, 0xb8,
    0xb8, 0xb8,

    /* U+004A "J" */
    0x0, 0xb, 0x90, 0x0, 0xb9, 0x0, 0xb, 0x90,
    0x0, 0xb9, 0x0, 0xb, 0x90, 0x0, 0xb9, 0x0,
    0xb, 0x90, 0x0, 0xb9, 0x0, 0xb, 0x90, 0x0,
    0xb9, 0x0, 0xc, 0x80, 0x25, 0xf4, 0xf, 0xe8,
    0x0,

    /* U+004B "K" */
    0xb8, 0x0, 0xc, 0xb0, 0xb8, 0x0, 0xad, 0x0,
    0xb8, 0x7, 0xe1, 0x0, 0xb8, 0x5f, 0x20, 0x0,
    0xbf, 0xf7, 0x0, 0x0, 0xb9, 0x8e, 0x10, 0x0,
    0xb8, 0xc, 0xb0, 0x0, 0xb8, 0x2, 0xf7, 0x0,
    0xb8, 0x0, 0x6f, 0x30, 0xb8, 0x0, 0xa, 0xe1,

    /* U+004C "L" */
    0xb8, 0x0, 0x0, 0xb8, 0x0, 0x0, 0xb8, 0x0,
    0x0, 0xb8, 0x0, 0x0, 0xb8, 0x0, 0x0, 0xb8,
    0x0, 0x0, 0xb8, 0x0, 0x0, 0xb8, 0x0, 0x0,
    0xb9, 0x22, 0x22, 0xbf, 0xff, 0xfc,

    /* U+004D "M" */
    0xbf, 0x70, 0x0, 0xd, 0xf5, 0xbd, 0xd0, 0x0,
    0x3e, 0xf5, 0xb8, 0xe4, 0x0, 0x98, 0xe5, 0xb8,
    0x8a, 0x0, 0xf2, 0xe5, 0xb8, 0x2f, 0x16, 0xb0,
    0xe5, 0xb8, 0xc, 0x6c, 0x50, 0xe5, 0xb8, 0x5,
    0xef, 0x0, 0xe5, 0xb8, 0x0, 0xb7, 0x0, 0xe5,
    0xb8, 0x0, 0x0, 0x0, 0xe5, 0xb8, 0x0, 0x0,
    0x0, 0xe5,

    /* U+004E "N" */
    0xbf, 0x80, 0x0, 0x3f, 0xb, 0xcf, 0x10, 0x3,
    0xf0, 0xb8, 0xb9, 0x0, 0x3f, 0xb, 0x82, 0xf2,
    0x3, 0xf0, 0xb8, 0x9, 0xa0, 0x3f, 0xb, 0x80,
    0x1f, 0x33, 0xf0, 0xb8, 0x0, 0x8b, 0x3f, 0xb,
    0x80, 0x1, 0xe7, 0xf0, 0xb8, 0x0, 0x7, 0xff,
    0xb, 0x80, 0x0, 0xe, 0xf0,

    /* U+004F "O" */
    0x4, 0xce, 0xfd, 0x80, 0x3, 0xf9, 0x22, 0x5e,
    0x90, 0xaa, 0x0, 0x0, 0x4f, 0x1e, 0x60, 0x0,
    0x0, 0xf4, 0xf5, 0x0, 0x0, 0xf, 0x5f, 0x50,
    0x0, 0x0, 0xf5, 0xe6, 0x0, 0x0, 0xf, 0x4a,
    0xa0, 0x0, 0x4, 0xf1, 0x3f, 0x82, 0x25, 0xea,
    0x0, 0x5c, 0xef, 0xe8, 0x0,

    /* U+0050 "P" */
    0xbf, 0xff, 0xd6, 0xb, 0x92, 0x37, 0xf3, 0xb8,
    0x0, 0xd, 0x7b, 0x80, 0x0, 0xc7, 0xb8, 0x0,
    0x4f, 0x4b, 0xff, 0xff, 0x80, 0xb9, 0x21, 0x0,
    0xb, 0x80, 0x0, 0x0, 0xb8, 0x0, 0x0, 0xb,
    0x80, 0x0, 0x0,

    /* U+0051 "Q" */
    0x4, 0xce, 0xfd, 0x80, 0x3, 0xf9, 0x22, 0x5e,
    0x90, 0xaa, 0x0, 0x0, 0x4f, 0x1e, 0x60, 0x0,
    0x0, 0xf4, 0xf5, 0x0, 0x0, 0xf, 0x5f, 0x50,
    0x0, 0x0, 0xf5, 0xe6, 0x0, 0x0, 0xf, 0x4a,
    0xa0, 0x0, 0x4, 0xf1, 0x3f, 0x82, 0x25, 0xe9,
    0x0, 0x5c, 0xef, 0xe8, 0x0, 0x0, 0x0, 0x6e,
    0x10, 0x0, 0x0, 0x0, 0xbb, 0x0,

    /* U+0052 "R" */
    0xbf, 0xff, 0xd5, 0x0, 0xb9, 0x23, 0x8f, 0x20,
    0xb8, 0x0, 0xe, 0x60, 0xb8, 0x0, 0xd, 0x60,
    0xb8, 0x0, 0x5f, 0x30, 0xbf, 0xff, 0xe7, 0x0,
    0xb9, 0x28, 0xd0, 0x0, 0xb8, 0x0, 0xc9, 0x0,
    0xb8, 0x0, 0x2f, 0x40, 0xb8, 0x0, 0x7, 0xe1,

    /* U+0053 "S" */
    0x2, 0xbe, 0xfe, 0x70, 0xc, 0xa3, 0x24, 0x40,
    0xf, 0x30, 0x0, 0x0, 0xe, 0x70, 0x0, 0x0,
    0x4, 0xee, 0x94, 0x0, 0x0, 0x4, 0x8f, 0x90,
    0x0, 0x0, 0x4, 0xf0, 0x0, 0x0, 0x2, 0xf1,
    0x15, 0x32, 0x3b, 0xd0, 0x2c, 0xef, 0xea, 0x20,

    /* U+0054 "T" */
    0xcf, 0xff, 0xff, 0xf2, 0x22, 0x2f, 0x72, 0x20,
    0x0, 0xf, 0x50, 0x0, 0x0, 0xf, 0x50, 0x0,
    0x0, 0xf, 0x50, 0x0, 0x0, 0xf, 0x50, 0x0,
    0x0, 0xf, 0x50, 0x0, 0x0, 0xf, 0x50, 0x0,
    0x0, 0xf, 0x50, 0x0, 0x0, 0xf, 0x50, 0x0,

    /* U+0055 "U" */
    0xc7, 0x0, 0x0, 0xa9, 0xc7, 0x0, 0x0, 0xa9,
    0xc7, 0x0, 0x0, 0xa9, 0xc7, 0x0, 0x0, 0xa9,
    0xc7, 0x0, 0x0, 0xa9, 0xc7, 0x0, 0x0, 0xa9,
    0xb8, 0x0, 0x0, 0xa9, 0xaa, 0x0, 0x0, 0xd7,
    0x6f, 0x62, 0x38, 0xf2, 0x6, 0xdf, 0xec, 0x40,

    /* U+0056 "V" */
    0x9b, 0x0, 0x0, 0xf, 0x44, 0xf0, 0x0, 0x5,
    0xe0, 0xe, 0x50, 0x0, 0xa9, 0x0, 0xaa, 0x0,
    0xf, 0x40, 0x4, 0xf0, 0x4, 0xe0, 0x0, 0xf,
    0x40, 0x99, 0x0, 0x0, 0xa9, 0xe, 0x30, 0x0,
    0x4, 0xe4, 0xe0, 0x0, 0x0, 0xf, 0xd9, 0x0,
    0x0, 0x0, 0xaf, 0x30, 0x0,

    /* U+0057 "W" */
    0x8c, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x14, 0xf0,
    0x0, 0x7d, 0x20, 0x6, 0xd0, 0x1f, 0x30, 0xc,
    0xe6, 0x0, 0x99, 0x0, 0xd6, 0x1, 0xf7, 0xb0,
    0xd, 0x60, 0xa, 0xa0, 0x5b, 0x2f, 0x0, 0xf2,
    0x0, 0x6d, 0x9, 0x70, 0xe3, 0x3e, 0x0, 0x2,
    0xf1, 0xe2, 0xa, 0x87, 0xb0, 0x0, 0xe, 0x7e,
    0x0, 0x5c, 0xa7, 0x0, 0x0, 0xbe, 0xa0, 0x1,
    0xfe, 0x30, 0x0, 0x7, 0xf5, 0x0, 0xd, 0xf0,
    0x0,

    /* U+0058 "X" */
    0x2f, 0x50, 0x0, 0x6e, 0x0, 0x8d, 0x0, 0x1e,
    0x50, 0x0, 0xe7, 0x9, 0xb0, 0x0, 0x5, 0xf4,
    0xf2, 0x0, 0x0, 0xb, 0xf7, 0x0, 0x0, 0x0,
    0xde, 0xa0, 0x0, 0x0, 0x7d, 0x2f, 0x40, 0x0,
    0x2f, 0x40, 0x8d, 0x0, 0xb, 0xa0, 0x0, 0xe7,
    0x5, 0xf1, 0x0, 0x5, 0xf2,

    /* U+0059 "Y" */
    0x9d, 0x0, 0x0, 0x7e, 0x1, 0xe6, 0x0, 0x1f,
    0x50, 0x6, 0xe1, 0x9, 0xb0, 0x0, 0xc, 0x82,
    0xf2, 0x0, 0x0, 0x3f, 0xd8, 0x0, 0x0, 0x0,
    0xae, 0x0, 0x0, 0x0, 0x7, 0xc0, 0x0, 0x0,
    0x0, 0x7c, 0x0, 0x0, 0x0, 0x7, 0xc0, 0x0,
    0x0, 0x0, 0x7c, 0x0, 0x0,

    /* U+005A "Z" */
    0xe, 0xff, 0xff, 0xfd, 0x0, 0x23, 0x33, 0x3e,
    0xa0, 0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x5,
    0xf2, 0x0, 0x0, 0x2, 0xf6, 0x0, 0x0, 0x0,
    0xca, 0x0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x0,
    0x5f, 0x20, 0x0, 0x0, 0x1e, 0x93, 0x33, 0x33,
    0x3, 0xff, 0xff, 0xff, 0xf2,

    /* U+005B "[" */
    0xf, 0xfa, 0xf, 0x40, 0xf, 0x30, 0xf, 0x30,
    0xf, 0x30, 0xf, 0x30, 0xf, 0x30, 0xf, 0x30,
    0xf, 0x30, 0xf, 0x30, 0xf, 0x30, 0xf, 0x30,
    0xf, 0x40, 0xf, 0xfa,

    /* U+005C "\\" */
    0x7a, 0x0, 0x0, 0x1, 0xf1, 0x0, 0x0, 0x9,
    0x80, 0x0, 0x0, 0x2e, 0x0, 0x0, 0x0, 0xc6,
    0x0, 0x0, 0x5, 0xc0, 0x0, 0x0, 0xe, 0x30,
    0x0, 0x0, 0x7a, 0x0, 0x0, 0x1, 0xf1, 0x0,
    0x0, 0x9, 0x80, 0x0, 0x0, 0x2e, 0x0, 0x0,
    0x0, 0xc5, 0x0, 0x0, 0x3, 0x50,

    /* U+005D "]" */
    0x8f, 0xf2, 0x2, 0xf2, 0x1, 0xf2, 0x1, 0xf2,
    0x1, 0xf2, 0x1, 0xf2, 0x1, 0xf2, 0x1, 0xf2,
    0x1, 0xf2, 0x1, 0xf2, 0x1, 0xf2, 0x1, 0xf2,
    0x2, 0xf2, 0x8f, 0xf2,

    /* U+005E "^" */
    0x0, 0x7, 0x60, 0x0, 0x5, 0xef, 0x10, 0x0,
    0xb6, 0xb7, 0x0, 0x2f, 0x15, 0xd0, 0x8, 0xa0,
    0xe, 0x40, 0xe4, 0x0, 0x8a,

    /* U+005F "_" */
    0xee, 0xee, 0xee, 0xe1, 0x11, 0x11, 0x11,

    /* U+0060 "`" */
    0xc, 0xa0, 0x1, 0xd3,

    /* U+0061 "a" */
    0x0, 0x6f, 0xfd, 0x50, 0x0, 0x11, 0x5, 0xf1,
    0x0, 0x0, 0x0, 0xf4, 0x3, 0xbd, 0xee, 0xf4,
    0xe, 0x71, 0x11, 0xe4, 0x3f, 0x0, 0x0, 0xf4,
    0x1f, 0x40, 0x1a, 0xf4, 0x6, 0xde, 0xd5, 0xd4,

    /* U+0062 "b" */
    0xb7, 0x0, 0x0, 0x0, 0xb7, 0x0, 0x0, 0x0,
    0xb7, 0x0, 0x0, 0x0, 0xb8, 0xaf, 0xfa, 0x10,
    0xbe, 0x61, 0x2d, 0x90, 0xba, 0x0, 0x6, 0xe0,
    0xb7, 0x0, 0x3, 0xf0, 0xb7, 0x0, 0x3, 0xf0,
    0xba, 0x0, 0x6, 0xe0, 0xbe, 0x61, 0x3d, 0x80,
    0xb6, 0xaf, 0xfa, 0x0,

    /* U+0063 "c" */
    0x1, 0xae, 0xfe, 0x30, 0xad, 0x31, 0x31, 0xf,
    0x40, 0x0, 0x2, 0xf2, 0x0, 0x0, 0x2f, 0x20,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0xb, 0xc3, 0x13,
    0x10, 0x19, 0xef, 0xe4,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x9a, 0x0, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0x0, 0x9a, 0x0, 0xaf, 0xf9, 0x9a,
    0xa, 0xd2, 0x17, 0xea, 0xf, 0x40, 0x0, 0xca,
    0x1f, 0x20, 0x0, 0x9a, 0x1f, 0x20, 0x0, 0x9a,
    0xf, 0x40, 0x0, 0xba, 0xb, 0xb0, 0x5, 0xfa,
    0x1, 0xbf, 0xea, 0x8a,

    /* U+0065 "e" */
    0x1, 0xae, 0xfc, 0x30, 0xa, 0xc2, 0x18, 0xe0,
    0xf, 0x30, 0x0, 0xf3, 0x2f, 0x88, 0xab, 0xf4,
    0x2f, 0x64, 0x44, 0x41, 0xf, 0x40, 0x0, 0x0,
    0xb, 0xd4, 0x13, 0x60, 0x1, 0x9e, 0xfe, 0x90,

    /* U+0066 "f" */
    0x0, 0x9f, 0xe0, 0x4f, 0x20, 0x7, 0xc0, 0xc,
    0xff, 0xf8, 0x8, 0xb0, 0x0, 0x7b, 0x0, 0x7,
    0xb0, 0x0, 0x7b, 0x0, 0x7, 0xb0, 0x0, 0x7b,
    0x0, 0x7, 0xb0, 0x0,

    /* U+0067 "g" */
    0x0, 0xaf, 0xe9, 0x9a, 0xa, 0xc2, 0x17, 0xea,
    0xf, 0x40, 0x0, 0xca, 0x1f, 0x20, 0x0, 0x9a,
    0x1f, 0x20, 0x0, 0x9a, 0xf, 0x40, 0x0, 0xca,
    0xb, 0xc2, 0x17, 0xfa, 0x1, 0xbf, 0xfa, 0xa9,
    0x0, 0x0, 0x0, 0xc7, 0x0, 0x31, 0x28, 0xf1,
    0x0, 0xcf, 0xfc, 0x40,

    /* U+0068 "h" */
    0xb7, 0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xb7,
    0x0, 0x0, 0xb, 0x9a, 0xfe, 0x80, 0xbf, 0x62,
    0x5f, 0x4b, 0x90, 0x0, 0xd6, 0xb7, 0x0, 0xc,
    0x7b, 0x70, 0x0, 0xc7, 0xb7, 0x0, 0xc, 0x7b,
    0x70, 0x0, 0xc7, 0xb7, 0x0, 0xc, 0x70,

    /* U+0069 "i" */
    0xc8, 0x53, 0x0, 0xb7, 0xb7, 0xb7, 0xb7, 0xb7,
    0xb7, 0xb7, 0xb7,

    /* U+006A "j" */
    0x0, 0xc8, 0x0, 0x53, 0x0, 0x0, 0x0, 0xb7,
    0x0, 0xb7, 0x0, 0xb7, 0x0, 0xb7, 0x0, 0xb7,
    0x0, 0xb7, 0x0, 0xb7, 0x0, 0xb7, 0x0, 0xc7,
    0x2, 0xf4, 0xff, 0x90,

    /* U+006B "k" */
    0xb7, 0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0xb7,
    0x0, 0x0, 0xb, 0x70, 0x1d, 0x90, 0xb7, 0xb,
    0xa0, 0xb, 0x7a, 0xb0, 0x0, 0xbf, 0xf1, 0x0,
    0xb, 0x7b, 0x90, 0x0, 0xb7, 0x2f, 0x50, 0xb,
    0x70, 0x6f, 0x20, 0xb7, 0x0, 0xbc, 0x0,

    /* U+006C "l" */
    0xb7, 0xb, 0x70, 0xb7, 0xb, 0x70, 0xb7, 0xb,
    0x70, 0xb7, 0xb, 0x70, 0xb7, 0xb, 0x90, 0x5e,
    0xb0,

    /* U+006D "m" */
    0xb9, 0xbf, 0xd4, 0x7e, 0xfa, 0xb, 0xe5, 0x29,
    0xfb, 0x33, 0xe7, 0xb8, 0x0, 0x3f, 0x10, 0x9,
    0xab, 0x70, 0x2, 0xf1, 0x0, 0x8b, 0xb7, 0x0,
    0x2f, 0x10, 0x8, 0xbb, 0x70, 0x2, 0xf1, 0x0,
    0x8b, 0xb7, 0x0, 0x2f, 0x10, 0x8, 0xbb, 0x70,
    0x2, 0xf1, 0x0, 0x8b,

    /* U+006E "n" */
    0xb9, 0xaf, 0xe8, 0xb, 0xf6, 0x25, 0xf4, 0xb9,
    0x0, 0xd, 0x6b, 0x70, 0x0, 0xc7, 0xb7, 0x0,
    0xc, 0x7b, 0x70, 0x0, 0xc7, 0xb7, 0x0, 0xc,
    0x7b, 0x70, 0x0, 0xc7,

    /* U+006F "o" */
    0x1, 0xae, 0xfd, 0x60, 0xa, 0xc3, 0x15, 0xf4,
    0xf, 0x40, 0x0, 0xaa, 0x2f, 0x20, 0x0, 0x7c,
    0x2f, 0x20, 0x0, 0x7c, 0xf, 0x40, 0x0, 0xaa,
    0xb, 0xc3, 0x15, 0xf5, 0x1, 0x9e, 0xfd, 0x60,

    /* U+0070 "p" */
    0xb8, 0xaf, 0xfa, 0x10, 0xbe, 0x61, 0x2d, 0x90,
    0xba, 0x0, 0x6, 0xe0, 0xb7, 0x0, 0x3, 0xf0,
    0xb7, 0x0, 0x3, 0xf0, 0xba, 0x0, 0x6, 0xe0,
    0xbe, 0x61, 0x3d, 0x80, 0xb8, 0xaf, 0xfa, 0x0,
    0xb7, 0x0, 0x0, 0x0, 0xb7, 0x0, 0x0, 0x0,
    0xb7, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0xaf, 0xf9, 0x9a, 0xa, 0xc2, 0x17, 0xea,
    0xf, 0x40, 0x0, 0xca, 0x1f, 0x20, 0x0, 0x9a,
    0x1f, 0x20, 0x0, 0x9a, 0xf, 0x40, 0x0, 0xca,
    0xb, 0xc2, 0x17, 0xea, 0x1, 0xbf, 0xf9, 0x9a,
    0x0, 0x0, 0x0, 0x9a, 0x0, 0x0, 0x0, 0x9a,
    0x0, 0x0, 0x0, 0x9a,

    /* U+0072 "r" */
    0xb8, 0xaf, 0xb, 0xe5, 0x0, 0xb9, 0x0, 0xb,
    0x70, 0x0, 0xb7, 0x0, 0xb, 0x70, 0x0, 0xb7,
    0x0, 0xb, 0x70, 0x0,

    /* U+0073 "s" */
    0x4, 0xdf, 0xe7, 0xe, 0x60, 0x12, 0xf, 0x20,
    0x0, 0x9, 0xea, 0x40, 0x0, 0x27, 0xe8, 0x0,
    0x0, 0x4d, 0x2, 0x1, 0x8b, 0x2e, 0xff, 0xb2,

    /* U+0074 "t" */
    0xa, 0x80, 0x0, 0xa8, 0x0, 0xcf, 0xff, 0x60,
    0xa8, 0x0, 0xa, 0x80, 0x0, 0xa8, 0x0, 0xa,
    0x80, 0x0, 0xa8, 0x0, 0x9, 0xb1, 0x0, 0x2d,
    0xf6,

    /* U+0075 "u" */
    0xc6, 0x0, 0xe, 0x5c, 0x60, 0x0, 0xe5, 0xc6,
    0x0, 0xe, 0x5c, 0x60, 0x0, 0xe5, 0xc6, 0x0,
    0xe, 0x5c, 0x70, 0x0, 0xf5, 0x9b, 0x0, 0x8f,
    0x51, 0xbf, 0xe8, 0xc5,

    /* U+0076 "v" */
    0xa8, 0x0, 0x3, 0xf1, 0x5d, 0x0, 0x8, 0xa0,
    0xf, 0x30, 0xd, 0x50, 0xa, 0x80, 0x3f, 0x0,
    0x4, 0xe0, 0x8a, 0x0, 0x0, 0xe3, 0xd4, 0x0,
    0x0, 0x9c, 0xe0, 0x0, 0x0, 0x3f, 0x90, 0x0,

    /* U+0077 "w" */
    0x9a, 0x0, 0x1f, 0xa0, 0x0, 0xf2, 0x4d, 0x0,
    0x5d, 0xe0, 0x3, 0xe0, 0xf, 0x10, 0xa6, 0xd3,
    0x7, 0xa0, 0xc, 0x50, 0xe2, 0x97, 0xb, 0x60,
    0x8, 0x93, 0xd0, 0x4c, 0xf, 0x10, 0x4, 0xd7,
    0x90, 0xf, 0x4d, 0x0, 0x0, 0xfd, 0x40, 0xb,
    0xc9, 0x0, 0x0, 0xcf, 0x0, 0x7, 0xf5, 0x0,

    /* U+0078 "x" */
    0x5e, 0x10, 0xb, 0x90, 0xb, 0x90, 0x5e, 0x0,
    0x2, 0xf3, 0xe5, 0x0, 0x0, 0x7f, 0xb0, 0x0,
    0x0, 0x9e, 0xd0, 0x0, 0x3, 0xf2, 0xd7, 0x0,
    0xd, 0x70, 0x4f, 0x20, 0x7d, 0x0, 0xa, 0xb0,

    /* U+0079 "y" */
    0xa9, 0x0, 0x2, 0xf1, 0x4e, 0x0, 0x7, 0xb0,
    0xe, 0x40, 0xc, 0x50, 0x8, 0xa0, 0x2f, 0x0,
    0x2, 0xf0, 0x7a, 0x0, 0x0, 0xc5, 0xc4, 0x0,
    0x0, 0x6c, 0xe0, 0x0, 0x0, 0x1f, 0x90, 0x0,
    0x0, 0xf, 0x30, 0x0, 0x2, 0xab, 0x0, 0x0,
    0xf, 0xa1, 0x0, 0x0,

    /* U+007A "z" */
    0xf, 0xff, 0xff, 0x50, 0x11, 0x17, 0xe0, 0x0,
    0x3, 0xf3, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x9c,
    0x0, 0x0, 0x5e, 0x10, 0x0, 0x1e, 0x72, 0x22,
    0x16, 0xff, 0xff, 0xf9,

    /* U+007B "{" */
    0x0, 0xba, 0x5, 0xe2, 0x6, 0xc0, 0x6, 0xc0,
    0x6, 0xc0, 0x8, 0xb0, 0x4f, 0x50, 0xa, 0xa0,
    0x6, 0xc0, 0x6, 0xc0, 0x6, 0xc0, 0x6, 0xc0,
    0x4, 0xe2, 0x0, 0xaa,

    /* U+007C "|" */
    0x4d, 0x4d, 0x4d, 0x4d, 0x4d, 0x4d, 0x4d, 0x4d,
    0x4d, 0x4d, 0x4d, 0x4d, 0x4d, 0x4d,

    /* U+007D "}" */
    0x8c, 0x10, 0x1c, 0x80, 0x9, 0x90, 0x9, 0x90,
    0x9, 0x90, 0x8, 0xb0, 0x2, 0xf7, 0x7, 0xc1,
    0x9, 0x90, 0x9, 0x90, 0x9, 0x90, 0x9, 0x90,
    0x1c, 0x70, 0x8b, 0x10,

    /* U+007E "~" */
    0x1b, 0xeb, 0x30, 0xc3, 0x7a, 0x17, 0xff, 0xc0,
    0x0, 0x0, 0x2, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 58, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 81, .box_w = 3, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15, .adv_w = 91, .box_w = 4, .box_h = 5, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 25, .adv_w = 134, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 70, .adv_w = 129, .box_w = 7, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 119, .adv_w = 184, .box_w = 11, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 174, .adv_w = 143, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 219, .adv_w = 57, .box_w = 2, .box_h = 5, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 224, .adv_w = 80, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 259, .adv_w = 80, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 294, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 312, .adv_w = 124, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 340, .adv_w = 68, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 348, .adv_w = 99, .box_w = 5, .box_h = 2, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 353, .adv_w = 68, .box_w = 3, .box_h = 2, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 356, .adv_w = 112, .box_w = 5, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 384, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 424, .adv_w = 129, .box_w = 5, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 449, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 489, .adv_w = 129, .box_w = 7, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 524, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 564, .adv_w = 129, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 599, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 639, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 679, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 719, .adv_w = 129, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 759, .adv_w = 85, .box_w = 3, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 771, .adv_w = 85, .box_w = 4, .box_h = 10, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 791, .adv_w = 124, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 827, .adv_w = 124, .box_w = 8, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 851, .adv_w = 124, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 887, .adv_w = 97, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 917, .adv_w = 193, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 995, .adv_w = 149, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1045, .adv_w = 139, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1080, .adv_w = 134, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1115, .adv_w = 157, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1155, .adv_w = 125, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1190, .adv_w = 118, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1220, .adv_w = 153, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1260, .adv_w = 159, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1300, .adv_w = 61, .box_w = 2, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1310, .adv_w = 61, .box_w = 5, .box_h = 13, .ofs_x = -2, .ofs_y = -3},
    {.bitmap_index = 1343, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1383, .adv_w = 114, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1413, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1463, .adv_w = 165, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1508, .adv_w = 166, .box_w = 9, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1553, .adv_w = 134, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1588, .adv_w = 166, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1642, .adv_w = 138, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1682, .adv_w = 127, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1722, .adv_w = 118, .box_w = 8, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1762, .adv_w = 157, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1802, .adv_w = 138, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1847, .adv_w = 201, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1912, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1957, .adv_w = 133, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2002, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2047, .adv_w = 66, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2075, .adv_w = 114, .box_w = 7, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2121, .adv_w = 66, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2149, .adv_w = 124, .box_w = 7, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 2170, .adv_w = 112, .box_w = 7, .box_h = 2, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2177, .adv_w = 78, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 2181, .adv_w = 137, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2213, .adv_w = 142, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2257, .adv_w = 111, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2285, .adv_w = 142, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2329, .adv_w = 129, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2361, .adv_w = 78, .box_w = 5, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2389, .adv_w = 142, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2433, .adv_w = 138, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2472, .adv_w = 60, .box_w = 2, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2483, .adv_w = 60, .box_w = 4, .box_h = 14, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2511, .adv_w = 116, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2550, .adv_w = 64, .box_w = 3, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2567, .adv_w = 206, .box_w = 11, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2611, .adv_w = 138, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2639, .adv_w = 138, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2671, .adv_w = 142, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 2715, .adv_w = 142, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2759, .adv_w = 85, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2779, .adv_w = 107, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2803, .adv_w = 75, .box_w = 5, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2828, .adv_w = 137, .box_w = 7, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2856, .adv_w = 118, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2888, .adv_w = 185, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2936, .adv_w = 116, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2968, .adv_w = 118, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3012, .adv_w = 113, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3040, .adv_w = 67, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3068, .adv_w = 41, .box_w = 2, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3082, .adv_w = 67, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3110, .adv_w = 124, .box_w = 8, .box_h = 3, .ofs_x = 0, .ofs_y = 2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Pair left and right glyphs for kerning*/
static const uint8_t kern_pair_glyph_ids[] =
{
    3, 3,
    3, 8,
    3, 13,
    3, 15,
    8, 3,
    8, 8,
    8, 13,
    8, 15,
    9, 75,
    13, 3,
    13, 8,
    13, 18,
    13, 24,
    13, 26,
    15, 3,
    15, 8,
    15, 18,
    15, 24,
    15, 26,
    16, 16,
    18, 13,
    18, 15,
    18, 27,
    18, 28,
    24, 13,
    24, 15,
    24, 27,
    24, 28,
    27, 18,
    27, 24,
    28, 18,
    28, 24,
    34, 3,
    34, 8,
    34, 36,
    34, 40,
    34, 48,
    34, 50,
    34, 53,
    34, 54,
    34, 55,
    34, 56,
    34, 58,
    34, 71,
    34, 77,
    34, 85,
    34, 87,
    34, 90,
    35, 36,
    35, 40,
    35, 48,
    35, 50,
    35, 53,
    35, 55,
    35, 57,
    35, 58,
    37, 13,
    37, 15,
    37, 34,
    37, 53,
    37, 55,
    37, 56,
    37, 57,
    37, 58,
    37, 59,
    37, 66,
    38, 75,
    39, 13,
    39, 15,
    39, 34,
    39, 66,
    39, 73,
    39, 76,
    40, 55,
    40, 58,
    43, 43,
    44, 34,
    44, 36,
    44, 40,
    44, 48,
    44, 50,
    44, 53,
    44, 54,
    44, 55,
    44, 56,
    44, 58,
    44, 68,
    44, 69,
    44, 70,
    44, 72,
    44, 77,
    44, 80,
    44, 82,
    44, 84,
    44, 85,
    44, 86,
    44, 87,
    44, 88,
    44, 90,
    45, 3,
    45, 8,
    45, 34,
    45, 36,
    45, 40,
    45, 48,
    45, 50,
    45, 53,
    45, 54,
    45, 55,
    45, 56,
    45, 58,
    45, 87,
    45, 88,
    45, 90,
    48, 13,
    48, 15,
    48, 34,
    48, 53,
    48, 55,
    48, 56,
    48, 57,
    48, 58,
    48, 59,
    48, 66,
    49, 13,
    49, 15,
    49, 34,
    49, 53,
    49, 55,
    49, 56,
    49, 57,
    49, 58,
    49, 59,
    49, 66,
    50, 13,
    50, 15,
    50, 34,
    50, 43,
    50, 53,
    50, 55,
    50, 56,
    50, 57,
    50, 58,
    50, 59,
    50, 66,
    51, 36,
    51, 40,
    51, 48,
    51, 50,
    51, 53,
    51, 54,
    51, 55,
    51, 56,
    51, 57,
    51, 58,
    51, 68,
    51, 69,
    51, 70,
    51, 72,
    51, 80,
    51, 82,
    51, 85,
    51, 86,
    51, 87,
    51, 89,
    51, 90,
    52, 13,
    52, 15,
    52, 52,
    52, 53,
    52, 55,
    52, 56,
    52, 58,
    52, 85,
    52, 87,
    52, 88,
    52, 89,
    52, 90,
    53, 3,
    53, 8,
    53, 13,
    53, 15,
    53, 34,
    53, 36,
    53, 40,
    53, 48,
    53, 50,
    53, 52,
    53, 66,
    53, 68,
    53, 69,
    53, 70,
    53, 72,
    53, 74,
    53, 75,
    53, 78,
    53, 79,
    53, 80,
    53, 81,
    53, 82,
    53, 83,
    53, 84,
    53, 85,
    53, 86,
    53, 87,
    53, 88,
    53, 89,
    53, 90,
    53, 91,
    54, 34,
    54, 57,
    54, 89,
    55, 13,
    55, 15,
    55, 27,
    55, 28,
    55, 34,
    55, 36,
    55, 40,
    55, 48,
    55, 50,
    55, 52,
    55, 66,
    55, 68,
    55, 69,
    55, 70,
    55, 72,
    55, 73,
    55, 76,
    55, 80,
    55, 82,
    55, 84,
    55, 87,
    55, 90,
    56, 13,
    56, 15,
    56, 27,
    56, 28,
    56, 34,
    56, 36,
    56, 40,
    56, 48,
    56, 50,
    56, 66,
    56, 68,
    56, 69,
    56, 70,
    56, 72,
    56, 80,
    56, 82,
    56, 84,
    57, 34,
    57, 36,
    57, 40,
    57, 48,
    57, 50,
    57, 53,
    57, 54,
    57, 55,
    57, 56,
    57, 58,
    57, 68,
    57, 69,
    57, 70,
    57, 72,
    57, 77,
    57, 80,
    57, 82,
    57, 84,
    57, 85,
    57, 86,
    57, 87,
    57, 88,
    57, 90,
    58, 3,
    58, 8,
    58, 13,
    58, 15,
    58, 27,
    58, 28,
    58, 34,
    58, 36,
    58, 40,
    58, 48,
    58, 50,
    58, 52,
    58, 66,
    58, 68,
    58, 69,
    58, 70,
    58, 71,
    58, 72,
    58, 73,
    58, 74,
    58, 76,
    58, 78,
    58, 79,
    58, 80,
    58, 81,
    58, 82,
    58, 83,
    58, 84,
    58, 85,
    58, 86,
    58, 87,
    58, 89,
    58, 90,
    58, 91,
    59, 36,
    59, 40,
    59, 48,
    59, 50,
    60, 75,
    66, 53,
    66, 55,
    66, 58,
    66, 87,
    66, 88,
    66, 90,
    67, 13,
    67, 15,
    67, 53,
    67, 55,
    67, 56,
    67, 57,
    67, 58,
    67, 87,
    67, 89,
    67, 90,
    70, 13,
    70, 15,
    70, 53,
    70, 58,
    70, 75,
    71, 3,
    71, 8,
    71, 10,
    71, 11,
    71, 13,
    71, 15,
    71, 32,
    71, 53,
    71, 55,
    71, 56,
    71, 57,
    71, 58,
    71, 62,
    71, 66,
    71, 68,
    71, 69,
    71, 70,
    71, 71,
    71, 72,
    71, 73,
    71, 74,
    71, 75,
    71, 76,
    71, 77,
    71, 80,
    71, 82,
    71, 84,
    71, 85,
    71, 94,
    73, 53,
    73, 58,
    73, 87,
    73, 90,
    74, 3,
    74, 8,
    74, 10,
    74, 32,
    74, 53,
    74, 62,
    74, 94,
    75, 53,
    76, 53,
    76, 54,
    76, 56,
    76, 66,
    76, 68,
    76, 69,
    76, 70,
    76, 72,
    76, 77,
    76, 80,
    76, 82,
    76, 86,
    77, 71,
    77, 87,
    77, 88,
    77, 90,
    78, 53,
    78, 58,
    78, 87,
    78, 90,
    79, 53,
    79, 58,
    79, 87,
    79, 90,
    80, 13,
    80, 15,
    80, 53,
    80, 55,
    80, 56,
    80, 57,
    80, 58,
    80, 87,
    80, 89,
    80, 90,
    81, 13,
    81, 15,
    81, 53,
    81, 55,
    81, 56,
    81, 57,
    81, 58,
    81, 87,
    81, 89,
    81, 90,
    82, 53,
    82, 58,
    82, 75,
    83, 3,
    83, 8,
    83, 13,
    83, 15,
    83, 66,
    83, 68,
    83, 69,
    83, 70,
    83, 71,
    83, 80,
    83, 82,
    83, 85,
    84, 53,
    84, 55,
    84, 56,
    84, 57,
    84, 58,
    84, 87,
    84, 89,
    84, 90,
    85, 71,
    86, 53,
    86, 58,
    87, 13,
    87, 15,
    87, 53,
    87, 57,
    87, 66,
    87, 68,
    87, 69,
    87, 70,
    87, 80,
    87, 82,
    88, 13,
    88, 15,
    88, 53,
    88, 57,
    88, 66,
    89, 53,
    89, 54,
    89, 56,
    89, 66,
    89, 68,
    89, 69,
    89, 70,
    89, 72,
    89, 77,
    89, 80,
    89, 82,
    89, 86,
    90, 13,
    90, 15,
    90, 53,
    90, 57,
    90, 66,
    90, 68,
    90, 69,
    90, 70,
    90, 80,
    90, 82,
    91, 53,
    92, 73,
    92, 75,
    92, 76
};

/* Kerning between the respective left and right glyphs
 * 4.4 format which needs to scaled with `kern_scale`*/
static const int8_t kern_pair_values[] =
{
    -13, -13, -40, -40, -13, -13, -40, -40,
    13, -36, -36, -31, -9, -9, -36, -36,
    -31, -9, -9, -27, -22, -22, -22, -22,
    -36, -36, -22, -22, -18, -13, -18, -13,
    -22, -22, -4, -4, -4, -4, -18, -2,
    -16, -9, -18, -4, -2, -4, -4, -4,
    -2, -2, -2, -2, -9, -4, -4, -5,
    -13, -13, -4, -8, -7, -4, -4, -9,
    -4, -9, 4, -36, -36, -9, -13, 0,
    0, -2, -5, 2, -4, -4, -4, -4,
    -4, -4, -9, -9, -9, -9, -9, -9,
    -9, -9, -9, -9, -9, -4, -2, -9,
    -13, -9, -13, -22, -22, 4, -13, -13,
    -13, -13, -18, -4, -18, -9, -22, -9,
    -5, -9, -13, -13, -4, -8, -7, -4,
    -4, -9, -4, -9, -45, -45, -13, -6,
    -2, -2, -11, -4, -9, -13, -13, -13,
    -4, 2, -8, -7, -4, -4, -9, -4,
    -9, -3, -3, -3, -3, -9, -2, -5,
    -2, -6, -9, -4, -4, -4, -4, -4,
    -4, -4, -2, -2, 4, -2, -4, -4,
    0, 0, -5, -2, -5, 0, -4, -2,
    0, -4, 0, 0, -34, -34, -18, -4,
    -4, -4, -4, 0, -18, -13, -13, -13,
    -13, -4, -4, -13, -13, -13, -13, -13,
    -13, -13, 4, -18, -9, -4, -9, -9,
    -13, -2, -4, 0, -31, -31, -9, -9,
    -16, -7, -7, -7, -7, -2, -9, -5,
    -5, -5, -5, 0, 0, -5, -5, -5,
    0, 0, -18, -18, -9, -9, -9, -4,
    -4, -4, -4, -9, -4, -4, -4, -4,
    -4, -4, -4, -4, -4, -4, -4, -4,
    -4, -9, -9, -9, -9, -9, -9, -9,
    -9, -9, -9, -9, -4, -2, -9, -13,
    -9, -13, 0, 0, -32, -32, -13, -13,
    -18, -9, -9, -9, -9, 0, -23, -13,
    -13, -13, 0, -13, 0, -4, 0, -9,
    -9, -13, -9, -13, -9, -14, 0, -9,
    -9, -9, -9, -9, -4, -4, -4, -4,
    18, -13, -5, -14, -2, -2, -2, -9,
    -9, -13, -5, -4, -9, -13, -2, -4,
    -2, -4, -4, -13, -9, 4, 1, 1,
    0, 14, 0, 0, 9, 9, 5, 5,
    5, 0, 5, -6, -4, -4, -4, 0,
    -9, -4, -4, -4, -4, -4, -4, -4,
    -4, 0, 1, -13, -9, -2, -2, 0,
    0, 0, 1, -4, 9, 5, -4, -9,
    0, -4, -4, -4, -4, -4, -4, -4,
    -4, -4, -2, -4, -4, -2, -4, -13,
    -9, -2, -2, -13, -9, -2, -2, -9,
    -9, -13, -5, -4, -9, -13, -2, -4,
    -2, -9, -9, -13, -5, -4, -9, -13,
    -2, -4, -2, -4, -4, 4, 0, 0,
    -20, -20, -11, -4, -4, -4, 0, -4,
    -4, 4, -13, -5, -4, -4, -13, -4,
    -2, -4, 0, -9, -4, -20, -20, -9,
    -9, -8, -2, -2, -2, -2, -2, -11,
    -11, -4, -9, -7, -9, 0, -4, -4,
    -4, -4, -4, -4, -4, -4, -4, -2,
    -20, -20, -9, -9, -8, -2, -2, -2,
    -2, -2, -13, 0, 18, 0
};

/*Collect the kern pair's data in one place*/
static const lv_font_fmt_txt_kern_pair_t kern_pairs =
{
    .glyph_ids = kern_pair_glyph_ids,
    .values = kern_pair_values,
    .pair_cnt = 486,
    .glyph_ids_size = 0
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_pairs,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_AlibabaPuHui14 = {
#else
lv_font_t ui_font_AlibabaPuHui14 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 16,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_ALIBABAPUHUI14*/

