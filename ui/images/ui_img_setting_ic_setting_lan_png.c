//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

// IMAGE DATA: assets/setting/ic_setting_lan.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t ui_img_setting_ic_setting_lan_png_data[] = {
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x00,0x02,0xFF,0xC6,0x00,0x31,0xFF,0xC6,0x00,0x67,0xFF,0xC6,0x02,0x87,0xFF,0xC8,0x02,0x91,0xFF,0xC8,0x02,0x87,0xFF,0xC6,0x00,0x67,0xFF,0xC6,0x00,0x31,0xFF,0xFF,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBF,0x00,0x10,0xFF,0xC6,0x04,0x7D,0xFF,0xC6,0x05,0xE1,0xFF,0xC6,0x04,0xFF,0xFF,0xC6,0x03,0xFF,0xFF,0xC7,0x04,0xFF,0xFF,0xC6,0x04,0xFF,0xFF,0xC6,0x04,0xFF,0xFF,0xC6,0x04,0xFF,0xFF,0xC6,0x04,0xFF,0xFF,0xC6,0x05,0xE1,0xFF,0xC6,0x04,0x7D,0xFF,0xBF,0x00,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC3,0x09,0x55,0xFF,0xC5,0x07,0xE9,0xFF,0xC5,0x07,0xFF,0xFF,0xC5,0x07,0xFF,0xFF,0xC5,0x07,0xFF,
    0xFF,0xC5,0x07,0xFF,0xFF,0xC5,0x08,0xCB,0xFF,0xC5,0x06,0x77,0xFF,0xC5,0x08,0xCB,0xFF,0xC5,0x07,0xFF,0xFF,0xC5,0x07,0xFF,0xFF,0xC5,0x07,0xFF,0xFF,0xC5,0x07,0xFF,0xFF,0xC5,0x07,0xE9,0xFF,0xC3,0x09,0x55,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0x00,0x01,0xFF,0xC3,0x0B,0x89,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC2,0x0A,0xC6,0xFF,0xC3,0x0A,0xCF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xC8,0xFF,0xB6,0x00,0x07,0x00,0x00,0x00,0x00,0xFF,0xBF,0x00,0x08,0xFF,0xC3,0x0A,0xC8,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xD0,0xFF,0xC2,0x0A,0xC6,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC2,0x0B,0x89,0xFF,0xFF,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC2,0x0F,0x89,0xFF,0xC2,0x0E,0xFF,0xFF,0xC2,0x0E,0xFB,0xFF,0xC2,0x0E,0x70,0xFF,0xBD,0x09,0x1B,0xFF,0xC1,0x0D,0xFA,0xFF,0xC1,0x0D,0xFC,0xFF,0xBF,0x0C,0x2C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBF,0x0C,0x2C,0xFF,0xC1,0x0D,0xFC,
    0xFF,0xC2,0x0D,0xFA,0xFF,0xBD,0x09,0x1B,0xFF,0xC2,0x0E,0x70,0xFF,0xC2,0x0D,0xFB,0xFF,0xC1,0x0E,0xFF,0xFF,0xC2,0x0F,0x89,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC0,0x12,0x55,0xFF,0xBF,0x10,0xFF,0xFF,0xC0,0x11,0xFB,0xFF,0xBE,0x10,0x4F,0x00,0x00,0x00,0x00,0xFF,0xC0,0x12,0x72,0xFF,0xC0,0x11,0xFF,0xFF,0xC0,0x11,0xB1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC0,0x11,0xB1,0xFF,0xC0,0x11,0xFF,0xFF,0xC1,0x12,0x73,0x00,0x00,0x00,0x00,0xFF,0xBE,0x10,0x4F,0xFF,0xC0,0x11,0xFB,0xFF,0xC0,0x11,0xFF,0xFF,0xC0,0x12,0x55,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBF,0x10,0x10,0xFF,0xBE,0x14,0xE9,0xFF,0xBE,0x14,0xFF,0xFF,0xBD,0x14,0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBD,0x14,0xC2,0xFF,0xBE,0x14,0xFF,0xFF,0xBD,0x15,0x55,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBD,0x15,0x55,0xFF,0xBE,0x14,0xFF,0xFF,0xBF,0x14,0xC2,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBD,0x14,0x70,0xFF,0xBE,0x14,0xFF,
    0xFF,0xBE,0x14,0xE9,0xFF,0xBF,0x10,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBC,0x18,0x7D,0xFF,0xBC,0x17,0xFF,0xFF,0xBD,0x17,0xC5,0xFF,0x80,0x00,0x02,0x00,0x00,0x00,0x00,0xFF,0xB6,0x24,0x07,0xFF,0xBD,0x18,0xFA,0xFF,0xBD,0x17,0xFC,0xFF,0xB8,0x1C,0x12,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC3,0x1E,0x11,0xFF,0xBC,0x17,0xFC,0xFF,0xBC,0x17,0xFA,0xFF,0xB6,0x24,0x07,0x00,0x00,0x00,0x00,0xFF,0x80,0x00,0x02,0xFF,0xBD,0x17,0xC6,0xFF,0xBD,0x18,0xFF,0xFF,0xBC,0x18,0x7D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x00,0x02,0xFF,0xBB,0x1B,0xE1,0xFF,0xBB,0x1A,0xFF,0xFF,0xBB,0x19,0x47,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBD,0x19,0x32,0xFF,0xBB,0x1A,0xFF,0xFF,0xBB,0x1B,0xD8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBB,0x1A,0xD8,0xFF,0xBB,0x1A,0xFF,0xFF,0xBD,0x19,0x32,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBB,0x19,0x47,0xFF,0xBB,0x1B,0xFF,0xFF,0xBB,0x1B,0xE1,0xFF,0x80,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0xFF,0xBB,0x1F,0x31,0xFF,0xB9,0x1E,0xFF,0xFF,0xBA,0x1E,0xE6,0xFF,0x80,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB9,0x1D,0x58,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1D,0xAE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB9,0x1D,0xAE,0xFF,0xB9,0x1E,0xFF,0xFF,0xBA,0x1D,0x59,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x00,0x02,0xFF,0xB9,0x1E,0xE6,0xFF,0xBA,0x1E,0xFF,0xFF,0xBB,0x1F,0x31,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB7,0x20,0x67,0xFF,0xB8,0x21,0xFF,0xFF,0xB7,0x20,0xA6,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB8,0x20,0x6F,0xFF,0xB8,0x21,0xFF,0xFF,0xB8,0x21,0x93,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB8,0x21,0x94,0xFF,0xB8,0x21,0xFF,0xFF,0xB8,0x20,0x6F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB8,0x20,0xA6,0xFF,0xB8,0x21,0xFF,0xFF,0xB7,0x20,0x67,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB5,0x26,0x87,0xFF,0xB6,0x24,0xFF,0xFF,0xB6,0x25,0xC3,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x26,0x81,
    0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x25,0xC0,0xFF,0xB6,0x24,0xFF,0xFF,0xB5,0x25,0xC2,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x26,0x81,0xFF,0xB5,0x25,0xC2,0xFF,0xB6,0x25,0xFF,0xFF,0xB6,0x25,0xC0,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x26,0x81,0xFF,0xB6,0x25,0xC3,0xFF,0xB6,0x24,0xFF,0xFF,0xB5,0x24,0x87,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB4,0x28,0x92,0xFF,0xB4,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB5,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB5,0x28,0xFF,0xFF,0xB5,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB5,0x28,0xFF,0xFF,0xB5,0x28,0xFF,0xFF,0xB5,0x27,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB5,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB4,0x27,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB5,0x28,0xFF,0xFF,0xB5,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB6,0x28,0x92,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB3,0x2B,0x87,0xFF,0xB3,0x2B,0xFF,0xFF,0xB3,0x2B,0xC3,0xFF,0xB4,0x2A,0x81,0xFF,0xB4,0x2A,0x81,0xFF,0xB4,0x2A,0x81,0xFF,0xB3,0x2A,0xC0,0xFF,0xB3,0x2B,0xFF,0xFF,0xB3,0x2B,0xC2,0xFF,0xB4,0x2A,0x81,0xFF,0xB4,0x2A,0x81,
    0xFF,0xB4,0x2A,0x81,0xFF,0xB4,0x2A,0x81,0xFF,0xB4,0x2A,0x81,0xFF,0xB4,0x2A,0x81,0xFF,0xB4,0x2A,0x81,0xFF,0xB3,0x2B,0xC2,0xFF,0xB3,0x2B,0xFF,0xFF,0xB3,0x2A,0xC0,0xFF,0xB4,0x2A,0x81,0xFF,0xB4,0x2A,0x81,0xFF,0xB4,0x2A,0x81,0xFF,0xB3,0x2B,0xC3,0xFF,0xB3,0x2B,0xFF,0xFF,0xB3,0x2B,0x87,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB0,0x2F,0x67,0xFF,0xB1,0x2E,0xFF,0xFF,0xB1,0x2E,0xA6,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB1,0x2E,0x6F,0xFF,0xB1,0x2E,0xFF,0xFF,0xB1,0x2F,0x94,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB1,0x2F,0x94,0xFF,0xB1,0x2E,0xFF,0xFF,0xB1,0x2E,0x6F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB1,0x2E,0xA6,0xFF,0xB1,0x2E,0xFF,0xFF,0xB0,0x2F,0x67,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB1,0x2F,0x31,0xFF,0xB0,0x32,0xFF,0xFF,0xAF,0x31,0xE6,0xFF,0x80,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAF,0x31,0x59,0xFF,0xB0,0x32,0xFF,0xFF,0xAE,0x32,0xAE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAE,0x32,0xAE,
    0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x31,0x59,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x00,0x02,0xFF,0xB0,0x32,0xE6,0xFF,0xB0,0x31,0xFF,0xFF,0xB1,0x2F,0x31,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x00,0x02,0xFF,0xAF,0x35,0xE1,0xFF,0xAE,0x35,0xFF,0xFF,0xAC,0x36,0x47,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAD,0x33,0x32,0xFF,0xAE,0x35,0xFF,0xFF,0xAE,0x35,0xD8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAE,0x35,0xD8,0xFF,0xAE,0x35,0xFF,0xFF,0xAD,0x33,0x32,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAC,0x36,0x47,0xFF,0xAE,0x35,0xFF,0xFF,0xAF,0x35,0xE1,0xFF,0x80,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAB,0x37,0x7D,0xFF,0xAD,0x38,0xFF,0xFF,0xAC,0x39,0xC5,0xFF,0x80,0x00,0x02,0x00,0x00,0x00,0x00,0xFF,0xB6,0x49,0x07,0xFF,0xAC,0x38,0xFA,0xFF,0xAD,0x38,0xFC,0xFF,0xB4,0x3C,0x11,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB4,0x3C,0x11,0xFF,0xAC,0x38,0xFC,0xFF,0xAC,0x38,0xFA,0xFF,0xB6,0x49,0x07,0x00,0x00,0x00,0x00,0xFF,0x80,0x00,0x02,0xFF,0xAD,0x37,0xC6,0xFF,0xAC,0x38,0xFF,
    0xFF,0xAB,0x37,0x7D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAF,0x40,0x10,0xFF,0xAB,0x3B,0xE9,0xFF,0xAB,0x3B,0xFF,0xFF,0xAB,0x3B,0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAB,0x3B,0xC3,0xFF,0xAB,0x3C,0xFF,0xFF,0xAB,0x3C,0x55,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAB,0x3C,0x55,0xFF,0xAB,0x3B,0xFF,0xFF,0xAB,0x3B,0xC3,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAB,0x3B,0x70,0xFF,0xAB,0x3B,0xFF,0xFF,0xAB,0x3C,0xE9,0xFF,0xAF,0x40,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAB,0x3F,0x55,0xFF,0xA9,0x3F,0xFF,0xFF,0xA9,0x3F,0xFB,0xFF,0xA8,0x41,0x4F,0x00,0x00,0x00,0x00,0xFF,0xAA,0x3F,0x72,0xFF,0xA9,0x3F,0xFF,0xFF,0xA9,0x3F,0xB1,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xA9,0x3F,0xB1,0xFF,0xA9,0x3F,0xFF,0xFF,0xAA,0x3F,0x72,0x00,0x00,0x00,0x00,0xFF,0xA8,0x41,0x4F,0xFF,0xA9,0x3F,0xFB,0xFF,0xA9,0x3F,0xFF,0xFF,0xAB,0x3F,0x55,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xA8,0x41,0x89,0xFF,0xA8,0x42,0xFF,0xFF,0xA8,0x42,0xFB,0xFF,0xA6,0x42,0x70,0xFF,0xAA,0x42,0x1B,0xFF,0xA8,0x42,0xFA,0xFF,0xA8,0x42,0xFC,0xFF,0xA8,0x46,0x2C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xA8,0x46,0x2C,0xFF,0xA7,0x42,0xFC,0xFF,0xA8,0x42,0xFA,0xFF,0xAA,0x42,0x1B,0xFF,0xA8,0x42,0x70,0xFF,0xA8,0x42,0xFB,0xFF,0xA8,0x42,0xFF,0xFF,0xA8,0x41,0x89,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0x00,0x01,0xFF,0xA6,0x45,0x89,0xFF,0xA6,0x45,0xFF,0xFF,0xA6,0x45,0xFF,0xFF,0xA6,0x46,0xC5,0xFF,0xA6,0x46,0xD0,0xFF,0xA6,0x45,0xFF,0xFF,0xA6,0x45,0xC8,0xFF,0x9F,0x40,0x08,0x00,0x00,0x00,0x00,0xFF,0x9F,0x40,0x08,0xFF,0xA6,0x46,0xC8,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xD0,0xFF,0xA6,0x46,0xC5,0xFF,0xA6,0x45,0xFF,0xFF,0xA6,0x45,0xFF,0xFF,0xA6,0x45,0x89,0xFF,0xFF,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xA5,0x48,0x55,
    0xFF,0xA4,0x48,0xE9,0xFF,0xA4,0x49,0xFF,0xFF,0xA4,0x49,0xFF,0xFF,0xA4,0x49,0xFF,0xFF,0xA4,0x48,0xFF,0xFF,0xA5,0x49,0xCB,0xFF,0xA5,0x49,0x77,0xFF,0xA5,0x49,0xCB,0xFF,0xA4,0x49,0xFF,0xFF,0xA4,0x48,0xFF,0xFF,0xA5,0x49,0xFF,0xFF,0xA5,0x49,0xFF,0xFF,0xA5,0x48,0xE9,0xFF,0xA5,0x48,0x55,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x9F,0x50,0x10,0xFF,0xA3,0x4B,0x7D,0xFF,0xA3,0x4C,0xE1,0xFF,0xA2,0x4C,0xFF,0xFF,0xA3,0x4C,0xFF,0xFF,0xA3,0x4C,0xFF,0xFF,0xA3,0x4C,0xFF,0xFF,0xA3,0x4C,0xFF,0xFF,0xA3,0x4C,0xFF,0xFF,0xA3,0x4C,0xFF,0xFF,0xA3,0x4C,0xE1,0xFF,0xA3,0x4B,0x7D,0xFF,0x9F,0x50,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x80,0x02,0xFF,0xA1,0x4E,0x31,0xFF,0xA1,0x4F,0x67,0xFF,0xA1,0x4D,0x87,
    0xFF,0xA1,0x4F,0x92,0xFF,0xA1,0x4D,0x87,0xFF,0xA1,0x4F,0x67,0xFF,0xA1,0x4E,0x31,0xFF,0x80,0x80,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const lv_img_dsc_t ui_img_setting_ic_setting_lan_png = {
    .header.always_zero = 0,
    .header.w = 29,
    .header.h = 29,
    .data_size = sizeof(ui_img_setting_ic_setting_lan_png_data),
    .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
    .data = ui_img_setting_ic_setting_lan_png_data
};

