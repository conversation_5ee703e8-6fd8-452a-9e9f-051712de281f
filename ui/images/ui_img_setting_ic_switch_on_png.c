//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

// IMAGE DATA: assets/setting/ic_switch_on.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t ui_img_setting_ic_switch_on_png_data[] = {
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDF,0x8A,0x1B,0x30,0xF1,0xA7,0x27,0x96,0xF2,0xAB,0x27,0xE0,0xFA,0xB5,0x28,0xFE,0xFD,0xBE,0x29,0xFF,0xFD,0xC0,0x28,0xFF,0xFD,0xC2,0x27,0xFF,0xFD,0xC4,0x26,0xFF,0xFC,0xC6,0x25,0xFF,0xFC,0xC9,0x25,0xFF,0xFC,0xCB,0x24,0xFF,0xFC,0xCE,0x24,0xFF,0xFC,0xD0,0x24,0xFF,0xFC,0xD3,0x24,0xFF,0xFB,0xD6,0x24,0xFF,0xFB,0xD9,0x25,0xFF,0xFB,0xDC,0x25,0xFF,0xFB,0xDE,0x26,0xFF,0xFB,0xE1,0x27,0xFF,0xFB,0xE4,0x28,0xFF,0xFB,0xE6,0x29,0xFF,0xF7,0xE0,0x28,0xFE,0xF0,0xD8,0x27,0xE0,0xEE,0xD6,0x27,0x96,0xDF,0xB5,0x1B,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC6,0x63,0x0E,0x12,0xF2,0xA1,0x27,0xAA,0xFD,0xB5,0x2B,0xFF,0xFE,0xB7,0x2A,0xFF,0xFE,0xB8,0x27,0xFF,0xFD,0xB9,0x26,0xFF,0xFD,0xBA,0x24,0xFF,0xFD,0xBC,0x23,0xFF,0xFD,0xBE,0x21,0xFF,0xFC,0xC0,0x20,0xFF,0xFC,0xC3,0x20,0xFF,0xFC,0xC5,0x20,0xFF,0xFB,0xC7,0x1F,0xFF,0xFB,0xCA,0x1F,0xFF,0xFB,0xCD,0x1F,0xFF,0xFB,0xD0,0x1F,0xFF,0xFB,0xD3,0x1F,0xFF,0xFA,0xD6,0x20,0xFF,0xFA,0xD9,0x20,0xFF,0xF9,0xDB,0x20,0xFF,0xF9,0xDE,0x21,0xFF,0xF9,0xE1,0x23,0xFF,0xF9,0xE4,0x24,0xFF,0xF9,0xE8,0x26,0xFF,0xFA,0xEB,0x27,0xFF,
    0xFA,0xEE,0x2A,0xFF,0xFA,0xEE,0x2B,0xFF,0xED,0xDA,0x27,0xAA,0xC6,0x8E,0x0E,0x12,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE6,0x8C,0x21,0x1F,0xF7,0xA7,0x29,0xDC,0xFF,0xB4,0x2B,0xFF,0xFE,0xB3,0x28,0xFF,0xFE,0xB3,0x25,0xFF,0xFD,0xB4,0x23,0xFF,0xFD,0xB4,0x21,0xFF,0xFC,0xB6,0x1F,0xFF,0xFC,0xB8,0x1E,0xFF,0xFC,0xB9,0x1C,0xFF,0xFB,0xBC,0x1B,0xFF,0xFB,0xBE,0x1B,0xFF,0xFB,0xC1,0x1A,0xFF,0xFB,0xC4,0x1A,0xFF,0xFA,0xC7,0x1A,0xFF,0xFA,0xCA,0x1A,0xFF,0xF9,0xCD,0x1A,0xFF,0xF8,0xD0,0x1A,0xFF,0xF8,0xD2,0x1A,0xFF,0xF7,0xD4,0x1B,0xFF,0xF7,0xDA,0x31,0xFF,0xF9,0xE9,0x7B,0xFF,0xFB,0xF1,0xA8,0xFF,0xFC,0xF5,0xB6,0xFF,0xFB,0xF4,0xA4,0xFF,0xF9,0xEF,0x71,0xFF,0xF7,0xEB,0x30,0xFF,0xF8,0xEF,0x28,0xFF,0xFA,0xF3,0x2B,0xFF,0xF3,0xE6,0x29,0xDC,0xE6,0xC5,0x21,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC6,0x63,0x0E,0x12,0xF7,0xA5,0x29,0xDC,0xFF,0xB1,0x2B,0xFF,0xFE,0xB0,0x27,0xFF,0xFE,0xAF,0x24,0xFF,0xFE,0xAF,0x21,0xFF,0xFD,0xAF,0x1F,0xFF,0xFD,0xB0,0x1C,0xFF,0xFC,0xB1,0x1A,0xFF,0xFB,0xB3,0x19,0xFF,0xFB,0xB5,0x18,0xFF,0xFA,0xB8,0x16,0xFF,0xFA,0xBB,0x16,0xFF,0xF9,0xBE,0x15,0xFF,0xF9,0xC1,0x15,0xFF,0xF9,0xC4,0x15,0xFF,0xF9,0xC7,0x15,0xFF,0xF7,0xC9,0x15,0xFF,0xF6,0xCC,0x15,0xFF,
    0xF4,0xCE,0x19,0xFF,0xF9,0xE8,0x8F,0xFF,0xFF,0xFD,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xFD,0xED,0xFF,0xF8,0xF2,0x78,0xFF,0xF7,0xEF,0x27,0xFF,0xF9,0xF4,0x2B,0xFF,0xF2,0xE7,0x29,0xDC,0xB8,0x8E,0x0E,0x12,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF2,0x9B,0x29,0xAA,0xFF,0xAF,0x2C,0xFF,0xFE,0xAD,0x28,0xFF,0xFE,0xAC,0x24,0xFF,0xFE,0xAB,0x21,0xFF,0xFE,0xAB,0x1D,0xFF,0xFD,0xAB,0x1B,0xFF,0xFC,0xAC,0x18,0xFF,0xFC,0xAD,0x16,0xFF,0xFB,0xAF,0x14,0xFF,0xFB,0xB2,0x13,0xFF,0xFA,0xB4,0x12,0xFF,0xF9,0xB7,0x11,0xFF,0xF9,0xBA,0x11,0xFF,0xF8,0xBE,0x11,0xFF,0xF8,0xC1,0x11,0xFF,0xF7,0xC3,0x11,0xFF,0xF5,0xC6,0x11,0xFF,0xF2,0xC7,0x16,0xFF,0xFB,0xEF,0xBB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF9,0xF5,0x99,0xFF,0xF5,0xF0,0x27,0xFF,0xF9,0xF7,0x2B,0xFF,0xEC,0xDE,0x29,0xAA,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE4,0x80,0x20,0x30,0xFE,0xAD,0x2C,0xFF,0xFE,0xAC,0x29,0xFF,0xFE,0xAA,0x25,0xFF,0xFE,0xA8,0x21,0xFF,0xFE,0xA8,0x1D,0xFF,0xFD,0xA7,0x1A,0xFF,0xFC,0xA8,0x17,0xFF,0xFC,0xA8,0x14,0xFF,0xFB,0xAA,0x12,0xFF,0xFB,0xAC,0x11,0xFF,0xFA,0xAE,0x0F,0xFF,
    0xF9,0xB1,0x0E,0xFF,0xF9,0xB4,0x0E,0xFF,0xF9,0xB7,0x0D,0xFF,0xF8,0xBB,0x0D,0xFF,0xF7,0xBE,0x0D,0xFF,0xF5,0xC0,0x0D,0xFF,0xF1,0xC0,0x0E,0xFF,0xF7,0xE3,0x93,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF6,0xF1,0x72,0xFF,0xF6,0xF3,0x29,0xFF,0xF9,0xF7,0x2C,0xFF,0xDA,0xBF,0x20,0x30,0x00,0x00,0x00,0x00,0xF1,0x9A,0x29,0x97,0xFF,0xAC,0x2B,0xFF,0xFE,0xAA,0x27,0xFF,0xFE,0xA7,0x23,0xFF,0xFE,0xA6,0x1E,0xFF,0xFD,0xA4,0x1B,0xFF,0xFD,0xA4,0x17,0xFF,0xFC,0xA4,0x14,0xFF,0xFB,0xA6,0x11,0xFF,0xFA,0xA7,0x0F,0xFF,0xF9,0xA9,0x0D,0xFF,0xF9,0xAC,0x0C,0xFF,0xF8,0xAE,0x0B,0xFF,0xF8,0xB2,0x0A,0xFF,0xF7,0xB5,0x0A,0xFF,0xF6,0xB9,0x0A,0xFF,0xF5,0xBB,0x0A,0xFF,0xF2,0xBC,0x0B,0xFF,0xEF,0xC3,0x2C,0xFF,0xFF,0xFE,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0xFC,0xE7,0xFF,0xF1,0xED,0x2C,0xFF,0xF7,0xF7,0x2B,0xFF,0xEC,0xE1,0x29,0x97,0x00,0x00,0x00,0x00,0xF4,0x9E,0x29,0xE0,0xFF,0xAB,0x2A,0xFF,0xFE,0xA8,0x25,0xFF,0xFE,0xA5,0x21,0xFF,
    0xFD,0xA4,0x1D,0xFF,0xFD,0xA3,0x19,0xFF,0xFC,0xA2,0x15,0xFF,0xFB,0xA2,0x12,0xFF,0xFB,0xA3,0x0F,0xFF,0xFA,0xA5,0x0D,0xFF,0xF9,0xA7,0x0B,0xFF,0xF9,0xAA,0x0A,0xFF,0xF8,0xAD,0x09,0xFF,0xF7,0xB0,0x08,0xFF,0xF7,0xB4,0x08,0xFF,0xF6,0xB7,0x08,0xFF,0xF4,0xB9,0x08,0xFF,0xEE,0xB7,0x09,0xFF,0xF2,0xD7,0x80,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF1,0xED,0x63,0xFF,0xF4,0xF3,0x2A,0xFF,0xEE,0xE5,0x29,0xE0,0x00,0x00,0x00,0x00,0xFB,0xA5,0x2B,0xFE,0xFF,0xAA,0x29,0xFF,0xFE,0xA7,0x24,0xFF,0xFE,0xA4,0x20,0xFF,0xFD,0xA2,0x1C,0xFF,0xFD,0xA1,0x18,0xFF,0xFC,0xA1,0x14,0xFF,0xFB,0xA1,0x11,0xFF,0xFB,0xA2,0x0E,0xFF,0xFA,0xA4,0x0C,0xFF,0xF9,0xA6,0x0A,0xFF,0xF9,0xA8,0x09,0xFF,0xF8,0xAB,0x08,0xFF,0xF7,0xAF,0x07,0xFF,0xF7,0xB2,0x06,0xFF,0xF6,0xB5,0x06,0xFF,0xF2,0xB6,0x07,0xFF,0xEA,0xB2,0x08,0xFF,0xF6,0xE6,0xB5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
    0xF3,0xEF,0x91,0xFF,0xF0,0xEE,0x28,0xFF,0xF4,0xF0,0x2B,0xFE,0x00,0x00,0x00,0x00,0xFF,0xAB,0x2D,0xFF,0xFF,0xA9,0x29,0xFF,0xFE,0xA6,0x24,0xFF,0xFE,0xA4,0x20,0xFF,0xFD,0xA2,0x1B,0xFF,0xFD,0xA1,0x17,0xFF,0xFC,0xA0,0x14,0xFF,0xFB,0xA0,0x11,0xFF,0xFB,0xA2,0x0E,0xFF,0xFA,0xA3,0x0C,0xFF,0xF9,0xA5,0x0A,0xFF,0xF9,0xA8,0x08,0xFF,0xF8,0xAB,0x07,0xFF,0xF7,0xAE,0x06,0xFF,0xF7,0xB2,0x06,0xFF,0xF5,0xB5,0x06,0xFF,0xF0,0xB5,0x07,0xFF,0xE6,0xAE,0x08,0xFF,0xF7,0xEB,0xC8,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xF2,0xEE,0xA2,0xFF,0xED,0xE9,0x28,0xFF,0xF7,0xF6,0x2D,0xFF,0xFF,0x00,0x00,0x01,0xFB,0xA5,0x2B,0xFE,0xFF,0xAA,0x29,0xFF,0xFE,0xA7,0x24,0xFF,0xFE,0xA4,0x20,0xFF,0xFD,0xA2,0x1C,0xFF,0xFD,0xA1,0x18,0xFF,0xFC,0xA1,0x14,0xFF,0xFB,0xA1,0x11,0xFF,0xFB,0xA1,0x0E,0xFF,0xFA,0xA4,0x0C,0xFF,0xF9,0xA6,0x0A,0xFF,0xF9,0xA8,0x09,0xFF,0xF8,0xAB,0x08,0xFF,0xF7,0xAF,0x07,0xFF,0xF7,0xB2,0x06,0xFF,0xF5,0xB5,0x06,0xFF,0xEF,0xB4,0x08,0xFF,0xE4,0xAC,0x09,0xFF,0xF4,0xE3,0xB5,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,
    0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xEE,0xE8,0x91,0xFF,0xEB,0xE5,0x28,0xFF,0xF2,0xED,0x2A,0xFE,0x00,0x00,0x00,0x01,0xF4,0x9D,0x29,0xE0,0xFF,0xAB,0x2A,0xFF,0xFE,0xA8,0x25,0xFF,0xFE,0xA5,0x21,0xFF,0xFE,0xA4,0x1D,0xFF,0xFD,0xA2,0x19,0xFF,0xFC,0xA2,0x15,0xFF,0xFB,0xA2,0x12,0xFF,0xFA,0xA3,0x0F,0xFF,0xFA,0xA5,0x0D,0xFF,0xF9,0xA7,0x0B,0xFF,0xF9,0xAA,0x0A,0xFF,0xF8,0xAD,0x09,0xFF,0xF7,0xB0,0x08,0xFF,0xF7,0xB4,0x08,0xFF,0xF5,0xB6,0x08,0xFF,0xEF,0xB4,0x08,0xFF,0xE3,0xAB,0x0A,0xFF,0xE9,0xCE,0x81,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE5,0xDB,0x62,0xFF,0xEA,0xE4,0x28,0xFF,0xE9,0xDF,0x28,0xE1,0x00,0x00,0x00,0x01,0xF1,0x9A,0x29,0x97,0xFF,0xAC,0x2B,0xFF,0xFE,0xAA,0x27,0xFF,0xFE,0xA7,0x23,0xFF,0xFE,0xA6,0x1E,0xFF,0xFD,0xA5,0x1B,0xFF,0xFD,0xA4,0x17,0xFF,0xFC,0xA4,0x14,0xFF,0xFB,0xA6,0x11,0xFF,0xFA,0xA7,0x0F,0xFF,0xF9,0xA9,0x0D,0xFF,0xF9,0xAB,0x0C,0xFF,0xF8,0xAF,0x0B,0xFF,0xF8,0xB2,0x0A,0xFF,0xF7,0xB5,0x0A,0xFF,0xF5,0xB7,0x0A,0xFF,
    0xF0,0xB6,0x0B,0xFF,0xE5,0xAE,0x0D,0xFF,0xDB,0xAD,0x2F,0xFF,0xFE,0xFD,0xFA,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0xF8,0xE7,0xFF,0xDB,0xCD,0x2A,0xFF,0xEB,0xE6,0x2A,0xFF,0xE6,0xD9,0x26,0x99,0x00,0x00,0x00,0x01,0xE4,0x80,0x20,0x30,0xFE,0xAD,0x2C,0xFF,0xFE,0xAC,0x29,0xFF,0xFE,0xAA,0x25,0xFF,0xFE,0xA8,0x21,0xFF,0xFE,0xA8,0x1D,0xFF,0xFD,0xA7,0x1A,0xFF,0xFC,0xA8,0x17,0xFF,0xFC,0xA8,0x14,0xFF,0xFB,0xAA,0x12,0xFF,0xFB,0xAC,0x11,0xFF,0xFA,0xAE,0x0F,0xFF,0xF9,0xB2,0x0E,0xFF,0xF9,0xB4,0x0E,0xFF,0xF9,0xB8,0x0D,0xFF,0xF7,0xBA,0x0D,0xFF,0xF2,0xB9,0x0E,0xFF,0xE8,0xB2,0x0F,0xFF,0xD9,0xA4,0x11,0xFF,0xE8,0xD1,0x95,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE0,0xD2,0x70,0xFF,0xDF,0xD1,0x27,0xFF,0xED,0xE6,0x2B,0xFF,0xCF,0xAD,0x1D,0x35,0xFF,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0xF2,0x9B,0x29,0xAA,0xFF,0xAF,0x2C,0xFF,0xFE,0xAD,0x28,0xFF,0xFE,0xAC,0x24,0xFF,0xFE,0xAB,0x21,0xFF,0xFD,0xAB,0x1D,0xFF,0xFD,0xAB,0x1B,0xFF,
    0xFC,0xAC,0x18,0xFF,0xFC,0xAD,0x16,0xFF,0xFB,0xAF,0x14,0xFF,0xFB,0xB2,0x13,0xFF,0xFA,0xB4,0x12,0xFF,0xFA,0xB7,0x11,0xFF,0xF9,0xBA,0x11,0xFF,0xF8,0xBD,0x11,0xFF,0xF4,0xBE,0x11,0xFF,0xEC,0xB8,0x12,0xFF,0xDF,0xAD,0x13,0xFF,0xD0,0xA0,0x19,0xFF,0xEF,0xE1,0xBD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xE6,0xD9,0x98,0xFF,0xD4,0xC0,0x24,0xFF,0xE5,0xDA,0x29,0xFF,0xE0,0xCD,0x26,0xAE,0x66,0x33,0x00,0x05,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC6,0x63,0x0E,0x12,0xF7,0xA5,0x29,0xDC,0xFF,0xB1,0x2B,0xFF,0xFE,0xB0,0x27,0xFF,0xFE,0xAF,0x24,0xFF,0xFE,0xAF,0x21,0xFF,0xFD,0xAF,0x1F,0xFF,0xFD,0xB0,0x1C,0xFF,0xFC,0xB1,0x1A,0xFF,0xFB,0xB3,0x19,0xFF,0xFB,0xB5,0x18,0xFF,0xFA,0xB8,0x16,0xFF,0xFA,0xBB,0x16,0xFF,0xF9,0xBD,0x15,0xFF,0xF9,0xC0,0x15,0xFF,0xF7,0xC2,0x15,0xFF,0xF1,0xBF,0x16,0xFF,0xE7,0xB7,0x16,0xFF,0xD9,0xAA,0x17,0xFF,0xCC,0x9E,0x1B,0xFF,0xE2,0xCC,0x90,0xFF,0xFD,0xFB,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFA,0xF7,0xED,0xFF,0xDB,0xC9,0x76,0xFF,0xCF,0xB7,0x24,0xFF,0xDE,0xCE,0x28,0xFF,0xE3,0xD1,0x28,0xDE,0xA1,0x71,0x1C,0x1B,0x80,0x00,0x00,0x02,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE6,0x8C,0x21,0x1F,0xF7,0xA8,0x29,0xDC,0xFF,0xB4,0x2B,0xFF,0xFE,0xB3,0x28,0xFF,0xFE,0xB3,0x25,0xFF,0xFD,0xB4,0x23,0xFF,0xFD,0xB4,0x21,0xFF,0xFC,0xB6,0x1F,0xFF,0xFC,0xB7,0x1E,0xFF,0xFC,0xB9,0x1C,0xFF,0xFB,0xBC,0x1B,0xFF,0xFB,0xBE,0x1B,0xFF,0xFB,0xC1,0x1A,0xFF,0xFA,0xC4,0x1A,0xFF,0xF9,0xC6,0x1A,0xFF,0xF6,0xC6,0x1A,0xFF,0xEF,0xC2,0x1A,0xFF,0xE4,0xB8,0x1A,0xFF,0xD8,0xAC,0x1B,0xFF,0xCC,0xA0,0x1B,0xFF,0xCA,0xA3,0x32,0xFF,0xDA,0xC2,0x7B,0xFF,0xE6,0xD6,0xA7,0xFF,0xEA,0xDE,0xB6,0xFF,0xE5,0xD5,0xA3,0xFF,0xD7,0xC1,0x6F,0xFF,0xC9,0xAB,0x2D,0xFF,0xCF,0xB5,0x24,0xFF,0xDB,0xC8,0x28,0xFF,0xDF,0xC9,0x27,0xDF,0xB9,0x91,0x1D,0x2C,0x66,0x33,0x00,0x05,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC6,0x63,0x0E,0x12,0xF2,0xA1,0x27,0xAA,0xFE,0xB5,0x2B,0xFF,0xFE,0xB7,0x2A,0xFF,0xFE,0xB8,0x27,0xFF,0xFD,0xB9,0x26,0xFF,0xFD,0xBA,0x24,0xFF,0xFD,0xBC,0x23,0xFF,0xFD,0xBE,0x21,0xFF,0xFC,0xC0,0x20,0xFF,0xFC,0xC2,0x20,0xFF,0xFC,0xC5,0x20,0xFF,0xFB,0xC7,0x1F,0xFF,0xFB,0xCA,0x1F,0xFF,0xF9,0xCC,0x1F,0xFF,0xF6,0xCB,0x1F,0xFF,0xEF,0xC6,0x1F,0xFF,0xE6,0xBD,0x1F,0xFF,0xDB,0xB4,0x1F,0xFF,0xD2,0xAB,0x1F,0xFF,0xCC,0xA5,0x1F,0xFF,0xC8,0xA3,0x20,0xFF,0xC7,0xA3,0x21,0xFF,0xC9,0xA7,0x22,0xFF,0xCD,0xAF,0x24,0xFF,
    0xD4,0xBA,0x26,0xFF,0xDD,0xC7,0x28,0xFF,0xD6,0xB8,0x25,0xB3,0x96,0x62,0x17,0x22,0x6D,0x49,0x24,0x07,0x80,0x00,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDF,0x8A,0x1B,0x30,0xF1,0xA7,0x27,0x96,0xF2,0xAB,0x27,0xE0,0xFA,0xB5,0x28,0xFE,0xFD,0xBE,0x29,0xFF,0xFD,0xC0,0x28,0xFF,0xFD,0xC2,0x27,0xFF,0xFD,0xC4,0x26,0xFF,0xFC,0xC6,0x25,0xFF,0xFC,0xC9,0x25,0xFF,0xFC,0xCB,0x24,0xFF,0xFC,0xCE,0x24,0xFF,0xFB,0xD0,0x24,0xFF,0xFA,0xD1,0x24,0xFF,0xF7,0xD0,0x24,0xFF,0xF1,0xCC,0x24,0xFF,0xEA,0xC7,0x24,0xFF,0xE3,0xC0,0x24,0xFF,0xDE,0xBB,0x24,0xFF,0xDA,0xB9,0x25,0xFF,0xD8,0xB9,0x25,0xFF,0xD7,0xB7,0x25,0xFE,0xD2,0xB1,0x24,0xE5,0xCF,0xAC,0x24,0xA5,0xB3,0x7F,0x1D,0x46,0x78,0x3C,0x1E,0x11,0x6D,0x49,0x24,0x07,0x80,0x00,0x00,0x02,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x55,0x55,0x00,0x03,
    0x6D,0x49,0x24,0x07,0x76,0x3B,0x14,0x0D,0x73,0x33,0x1A,0x14,0x71,0x39,0x1C,0x1B,0x78,0x38,0x20,0x20,0x74,0x36,0x1F,0x21,0x73,0x3A,0x19,0x1F,0x76,0x3B,0x1D,0x1A,0x79,0x36,0x1B,0x13,0x74,0x2E,0x17,0x0B,0x66,0x33,0x33,0x05,0x80,0x00,0x00,0x02,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x55,0x55,0x00,0x03,0x66,0x33,0x33,0x05,0x71,0x39,0x1C,0x09,0x74,0x2E,0x17,0x0B,0x74,0x2E,0x17,0x0B,0x80,0x33,0x1A,0x0A,0x80,0x40,0x20,0x08,0x66,0x33,0x00,0x05,0x55,0x55,0x00,0x03,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const lv_img_dsc_t ui_img_setting_ic_switch_on_png = {
    .header.always_zero = 0,
    .header.w = 36,
    .header.h = 22,
    .data_size = sizeof(ui_img_setting_ic_switch_on_png_data),
    .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
    .data = ui_img_setting_ic_switch_on_png_data
};

