//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

// IMAGE DATA: assets/dark/ic_line_light.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t ui_img_dark_ic_line_light_png_data[] = {
    0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0x04,0xFF,0xFF,0xFF,0x06,0xFF,0xFF,0xFF,0x08,0xFF,0xFF,0xFF,0x0B,0xFF,0xFF,0xFF,0x0D,0xFF,0xFF,0xFF,0x10,0xFF,0xFF,0xFF,0x12,0xFF,0xFF,0xFF,0x14,0xFF,0xFF,0xFF,0x17,0xFF,0xFF,0xFF,0x19,0xFF,0xFF,0xFF,0x1B,0xFF,0xFF,0xFF,0x1E,0xFF,0xFF,0xFF,0x20,0xFF,0xFF,0xFF,0x23,0xFF,0xFF,0xFF,0x25,0xFF,0xFF,0xFF,0x27,0xFF,0xFF,0xFF,0x2A,0xFF,0xFF,0xFF,0x2C,0xFF,0xFF,0xFF,0x2F,0xFF,0xFF,0xFF,0x31,0xFF,0xFF,0xFF,0x33,0xFF,0xFF,0xFF,0x36,0xFF,0xFF,0xFF,0x38,0xFF,0xFF,0xFF,0x3B,0xFF,0xFF,0xFF,0x3D,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xFF,0x42,0xFF,0xFF,0xFF,0x44,0xFF,0xFF,0xFF,0x46,0xFF,0xFF,0xFF,0x49,0xFF,0xFF,0xFF,0x4B,0xFF,0xFF,0xFF,0x4E,0xFF,0xFF,0xFF,0x50,0xFF,0xFF,0xFF,0x52,0xFF,0xFF,0xFF,0x55,0xFF,0xFF,0xFF,0x57,0xFF,0xFF,0xFF,0x5A,0xFF,0xFF,0xFF,0x5C,0xFF,0xFF,0xFF,0x5E,0xFF,0xFF,0xFF,0x61,0xFF,0xFF,0xFF,0x63,0xFF,0xFF,0xFF,0x66,0xFF,0xFF,0xFF,0x68,0xFF,0xFF,0xFF,0x6A,0xFF,0xFF,0xFF,0x6D,0xFF,0xFF,0xFF,0x6F,0xFF,0xFF,0xFF,0x71,0xFF,0xFF,0xFF,0x74,0xFF,0xFF,0xFF,0x76,0xFF,0xFF,0xFF,0x79,0xFF,0xFF,0xFF,0x7B,0xFF,0xFF,0xFF,0x7D,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0x82,0xFF,0xFF,0xFF,0x85,0xFF,0xFF,0xFF,0x87,0xFF,0xFF,0xFF,0x89,0xFF,0xFF,0xFF,0x8C,0xFF,0xFF,0xFF,0x8E,0xFF,0xFF,0xFF,0x91,0xFF,0xFF,0xFF,0x93,0xFF,0xFF,0xFF,0x95,0xFF,0xFF,0xFF,0x98,
    0xFF,0xFF,0xFF,0x9A,0xFF,0xFF,0xFF,0x9C,0xFF,0xFF,0xFF,0x9F,0xFF,0xFF,0xFF,0xA1,0xFF,0xFF,0xFF,0xA4,0xFF,0xFF,0xFF,0xA6,0xFF,0xFF,0xFF,0xA8,0xFF,0xFF,0xFF,0xAB,0xFF,0xFF,0xFF,0xAD,0xFF,0xFF,0xFF,0xB0,0xFF,0xFF,0xFF,0xB2,0xFF,0xFF,0xFF,0xB4,0xFF,0xFF,0xFF,0xB7,0xFF,0xFF,0xFF,0xB9,0xFF,0xFF,0xFF,0xBC,0xFF,0xFF,0xFF,0xBE,0xFF,0xFF,0xFF,0xC0,0xFF,0xFF,0xFF,0xC3,0xFF,0xFF,0xFF,0xC5,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xCA,0xFF,0xFF,0xFF,0xCC,0xFF,0xFF,0xFF,0xCF,0xFF,0xFF,0xFF,0xD1,0xFF,0xFF,0xFF,0xD3,0xFF,0xFF,0xFF,0xD6,0xFF,0xFF,0xFF,0xD8,0xFF,0xFF,0xFF,0xDB,0xFF,0xFF,0xFF,0xDD,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xE2,0xFF,0xFF,0xFF,0xE4,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xE9,0xFF,0xFF,0xFF,0xEB,0xFF,0xFF,0xFF,0xEE,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xF2,0xFF,0xFF,0xFF,0xF5,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFA,0xFF,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xF6,0xFF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFF,0xF1,0xFF,0xFF,0xFF,0xEE,0xFF,0xFF,0xFF,0xEC,0xFF,0xFF,0xFF,0xE9,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xE4,0xFF,0xFF,0xFF,0xE2,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xDD,0xFF,0xFF,0xFF,0xDB,0xFF,0xFF,0xFF,0xD8,0xFF,0xFF,0xFF,0xD6,0xFF,0xFF,0xFF,0xD3,0xFF,0xFF,0xFF,0xD1,0xFF,0xFF,0xFF,0xCE,0xFF,0xFF,0xFF,0xCC,
    0xFF,0xFF,0xFF,0xC9,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xC4,0xFF,0xFF,0xFF,0xC2,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xBD,0xFF,0xFF,0xFF,0xBA,0xFF,0xFF,0xFF,0xB8,0xFF,0xFF,0xFF,0xB6,0xFF,0xFF,0xFF,0xB3,0xFF,0xFF,0xFF,0xB1,0xFF,0xFF,0xFF,0xAE,0xFF,0xFF,0xFF,0xAC,0xFF,0xFF,0xFF,0xA9,0xFF,0xFF,0xFF,0xA7,0xFF,0xFF,0xFF,0xA4,0xFF,0xFF,0xFF,0xA2,0xFF,0xFF,0xFF,0x9F,0xFF,0xFF,0xFF,0x9D,0xFF,0xFF,0xFF,0x9A,0xFF,0xFF,0xFF,0x98,0xFF,0xFF,0xFF,0x95,0xFF,0xFF,0xFF,0x93,0xFF,0xFF,0xFF,0x90,0xFF,0xFF,0xFF,0x8E,0xFF,0xFF,0xFF,0x8C,0xFF,0xFF,0xFF,0x89,0xFF,0xFF,0xFF,0x87,0xFF,0xFF,0xFF,0x84,0xFF,0xFF,0xFF,0x82,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0x7D,0xFF,0xFF,0xFF,0x7A,0xFF,0xFF,0xFF,0x78,0xFF,0xFF,0xFF,0x75,0xFF,0xFF,0xFF,0x73,0xFF,0xFF,0xFF,0x70,0xFF,0xFF,0xFF,0x6E,0xFF,0xFF,0xFF,0x6B,0xFF,0xFF,0xFF,0x69,0xFF,0xFF,0xFF,0x66,0xFF,0xFF,0xFF,0x64,0xFF,0xFF,0xFF,0x62,0xFF,0xFF,0xFF,0x5F,0xFF,0xFF,0xFF,0x5D,0xFF,0xFF,0xFF,0x5A,0xFF,0xFF,0xFF,0x58,0xFF,0xFF,0xFF,0x55,0xFF,0xFF,0xFF,0x53,0xFF,0xFF,0xFF,0x50,0xFF,0xFF,0xFF,0x4E,0xFF,0xFF,0xFF,0x4B,0xFF,0xFF,0xFF,0x49,0xFF,0xFF,0xFF,0x46,0xFF,0xFF,0xFF,0x44,0xFF,0xFF,0xFF,0x41,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xFF,0x3D,0xFF,0xFF,0xFF,0x3A,0xFF,0xFF,0xFF,0x38,0xFF,0xFF,0xFF,0x35,0xFF,0xFF,0xFF,0x33,0xFF,0xFF,0xFF,0x30,0xFF,0xFF,0xFF,0x2E,
    0xFF,0xFF,0xFF,0x2B,0xFF,0xFF,0xFF,0x29,0xFF,0xFF,0xFF,0x26,0xFF,0xFF,0xFF,0x24,0xFF,0xFF,0xFF,0x21,0xFF,0xFF,0xFF,0x1F,0xFF,0xFF,0xFF,0x1C,0xFF,0xFF,0xFF,0x1A,0xFF,0xFF,0xFF,0x17,0xFF,0xFF,0xFF,0x15,0xFF,0xFF,0xFF,0x13,0xFF,0xFF,0xFF,0x10,0xFF,0xFF,0xFF,0x0E,0xFF,0xFF,0xFF,0x0B,0xFF,0xFF,0xFF,0x09,0xFF,0xFF,0xFF,0x06,0xFF,0xFF,0xFF,0x04,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0x04,0xFF,0xFF,0xFF,0x06,0xFF,0xFF,0xFF,0x08,0xFF,0xFF,0xFF,0x0B,0xFF,0xFF,0xFF,0x0D,0xFF,0xFF,0xFF,0x10,0xFF,0xFF,0xFF,0x12,0xFF,0xFF,0xFF,0x14,0xFF,0xFF,0xFF,0x17,0xFF,0xFF,0xFF,0x19,0xFF,0xFF,0xFF,0x1B,0xFF,0xFF,0xFF,0x1E,0xFF,0xFF,0xFF,0x20,0xFF,0xFF,0xFF,0x23,0xFF,0xFF,0xFF,0x25,0xFF,0xFF,0xFF,0x27,0xFF,0xFF,0xFF,0x2A,0xFF,0xFF,0xFF,0x2C,0xFF,0xFF,0xFF,0x2F,0xFF,0xFF,0xFF,0x31,0xFF,0xFF,0xFF,0x33,0xFF,0xFF,0xFF,0x36,0xFF,0xFF,0xFF,0x38,0xFF,0xFF,0xFF,0x3B,0xFF,0xFF,0xFF,0x3D,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xFF,0x42,0xFF,0xFF,0xFF,0x44,0xFF,0xFF,0xFF,0x46,0xFF,0xFF,0xFF,0x49,0xFF,0xFF,0xFF,0x4B,0xFF,0xFF,0xFF,0x4E,0xFF,0xFF,0xFF,0x50,0xFF,0xFF,0xFF,0x52,0xFF,0xFF,0xFF,0x55,0xFF,0xFF,0xFF,0x57,0xFF,0xFF,0xFF,0x5A,0xFF,0xFF,0xFF,0x5C,0xFF,0xFF,0xFF,0x5E,0xFF,0xFF,0xFF,0x61,0xFF,0xFF,0xFF,0x63,0xFF,0xFF,0xFF,0x66,0xFF,0xFF,0xFF,0x68,0xFF,0xFF,0xFF,0x6A,0xFF,0xFF,0xFF,0x6D,
    0xFF,0xFF,0xFF,0x6F,0xFF,0xFF,0xFF,0x71,0xFF,0xFF,0xFF,0x74,0xFF,0xFF,0xFF,0x76,0xFF,0xFF,0xFF,0x79,0xFF,0xFF,0xFF,0x7B,0xFF,0xFF,0xFF,0x7D,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0x82,0xFF,0xFF,0xFF,0x85,0xFF,0xFF,0xFF,0x87,0xFF,0xFF,0xFF,0x89,0xFF,0xFF,0xFF,0x8C,0xFF,0xFF,0xFF,0x8E,0xFF,0xFF,0xFF,0x91,0xFF,0xFF,0xFF,0x93,0xFF,0xFF,0xFF,0x95,0xFF,0xFF,0xFF,0x98,0xFF,0xFF,0xFF,0x9A,0xFF,0xFF,0xFF,0x9C,0xFF,0xFF,0xFF,0x9F,0xFF,0xFF,0xFF,0xA1,0xFF,0xFF,0xFF,0xA4,0xFF,0xFF,0xFF,0xA6,0xFF,0xFF,0xFF,0xA8,0xFF,0xFF,0xFF,0xAB,0xFF,0xFF,0xFF,0xAD,0xFF,0xFF,0xFF,0xB0,0xFF,0xFF,0xFF,0xB2,0xFF,0xFF,0xFF,0xB4,0xFF,0xFF,0xFF,0xB7,0xFF,0xFF,0xFF,0xB9,0xFF,0xFF,0xFF,0xBC,0xFF,0xFF,0xFF,0xBE,0xFF,0xFF,0xFF,0xC0,0xFF,0xFF,0xFF,0xC3,0xFF,0xFF,0xFF,0xC5,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xCA,0xFF,0xFF,0xFF,0xCC,0xFF,0xFF,0xFF,0xCF,0xFF,0xFF,0xFF,0xD1,0xFF,0xFF,0xFF,0xD3,0xFF,0xFF,0xFF,0xD6,0xFF,0xFF,0xFF,0xD8,0xFF,0xFF,0xFF,0xDB,0xFF,0xFF,0xFF,0xDD,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xE2,0xFF,0xFF,0xFF,0xE4,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xE9,0xFF,0xFF,0xFF,0xEB,0xFF,0xFF,0xFF,0xEE,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xF2,0xFF,0xFF,0xFF,0xF5,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFA,0xFF,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xF8,
    0xFF,0xFF,0xFF,0xF6,0xFF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFF,0xF1,0xFF,0xFF,0xFF,0xEE,0xFF,0xFF,0xFF,0xEC,0xFF,0xFF,0xFF,0xE9,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xE4,0xFF,0xFF,0xFF,0xE2,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xDD,0xFF,0xFF,0xFF,0xDB,0xFF,0xFF,0xFF,0xD8,0xFF,0xFF,0xFF,0xD6,0xFF,0xFF,0xFF,0xD3,0xFF,0xFF,0xFF,0xD1,0xFF,0xFF,0xFF,0xCE,0xFF,0xFF,0xFF,0xCC,0xFF,0xFF,0xFF,0xC9,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xC4,0xFF,0xFF,0xFF,0xC2,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xBD,0xFF,0xFF,0xFF,0xBA,0xFF,0xFF,0xFF,0xB8,0xFF,0xFF,0xFF,0xB6,0xFF,0xFF,0xFF,0xB3,0xFF,0xFF,0xFF,0xB1,0xFF,0xFF,0xFF,0xAE,0xFF,0xFF,0xFF,0xAC,0xFF,0xFF,0xFF,0xA9,0xFF,0xFF,0xFF,0xA7,0xFF,0xFF,0xFF,0xA4,0xFF,0xFF,0xFF,0xA2,0xFF,0xFF,0xFF,0x9F,0xFF,0xFF,0xFF,0x9D,0xFF,0xFF,0xFF,0x9A,0xFF,0xFF,0xFF,0x98,0xFF,0xFF,0xFF,0x95,0xFF,0xFF,0xFF,0x93,0xFF,0xFF,0xFF,0x90,0xFF,0xFF,0xFF,0x8E,0xFF,0xFF,0xFF,0x8C,0xFF,0xFF,0xFF,0x89,0xFF,0xFF,0xFF,0x87,0xFF,0xFF,0xFF,0x84,0xFF,0xFF,0xFF,0x82,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0x7D,0xFF,0xFF,0xFF,0x7A,0xFF,0xFF,0xFF,0x78,0xFF,0xFF,0xFF,0x75,0xFF,0xFF,0xFF,0x73,0xFF,0xFF,0xFF,0x70,0xFF,0xFF,0xFF,0x6E,0xFF,0xFF,0xFF,0x6B,0xFF,0xFF,0xFF,0x69,0xFF,0xFF,0xFF,0x66,0xFF,0xFF,0xFF,0x64,0xFF,0xFF,0xFF,0x62,0xFF,0xFF,0xFF,0x5F,0xFF,0xFF,0xFF,0x5D,0xFF,0xFF,0xFF,0x5A,
    0xFF,0xFF,0xFF,0x58,0xFF,0xFF,0xFF,0x55,0xFF,0xFF,0xFF,0x53,0xFF,0xFF,0xFF,0x50,0xFF,0xFF,0xFF,0x4E,0xFF,0xFF,0xFF,0x4B,0xFF,0xFF,0xFF,0x49,0xFF,0xFF,0xFF,0x46,0xFF,0xFF,0xFF,0x44,0xFF,0xFF,0xFF,0x41,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xFF,0x3D,0xFF,0xFF,0xFF,0x3A,0xFF,0xFF,0xFF,0x38,0xFF,0xFF,0xFF,0x35,0xFF,0xFF,0xFF,0x33,0xFF,0xFF,0xFF,0x30,0xFF,0xFF,0xFF,0x2E,0xFF,0xFF,0xFF,0x2B,0xFF,0xFF,0xFF,0x29,0xFF,0xFF,0xFF,0x26,0xFF,0xFF,0xFF,0x24,0xFF,0xFF,0xFF,0x21,0xFF,0xFF,0xFF,0x1F,0xFF,0xFF,0xFF,0x1C,0xFF,0xFF,0xFF,0x1A,0xFF,0xFF,0xFF,0x17,0xFF,0xFF,0xFF,0x15,0xFF,0xFF,0xFF,0x13,0xFF,0xFF,0xFF,0x10,0xFF,0xFF,0xFF,0x0E,0xFF,0xFF,0xFF,0x0B,0xFF,0xFF,0xFF,0x09,0xFF,0xFF,0xFF,0x06,0xFF,0xFF,0xFF,0x04,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0x01,0xFF,0xFF,0xFF,0x04,0xFF,0xFF,0xFF,0x06,0xFF,0xFF,0xFF,0x08,0xFF,0xFF,0xFF,0x0B,0xFF,0xFF,0xFF,0x0D,0xFF,0xFF,0xFF,0x10,0xFF,0xFF,0xFF,0x12,0xFF,0xFF,0xFF,0x14,0xFF,0xFF,0xFF,0x17,0xFF,0xFF,0xFF,0x19,0xFF,0xFF,0xFF,0x1B,0xFF,0xFF,0xFF,0x1E,0xFF,0xFF,0xFF,0x20,0xFF,0xFF,0xFF,0x23,0xFF,0xFF,0xFF,0x25,0xFF,0xFF,0xFF,0x27,0xFF,0xFF,0xFF,0x2A,0xFF,0xFF,0xFF,0x2C,0xFF,0xFF,0xFF,0x2F,0xFF,0xFF,0xFF,0x31,0xFF,0xFF,0xFF,0x33,0xFF,0xFF,0xFF,0x36,0xFF,0xFF,0xFF,0x38,0xFF,0xFF,0xFF,0x3B,0xFF,0xFF,0xFF,0x3D,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xFF,0x42,
    0xFF,0xFF,0xFF,0x44,0xFF,0xFF,0xFF,0x46,0xFF,0xFF,0xFF,0x49,0xFF,0xFF,0xFF,0x4B,0xFF,0xFF,0xFF,0x4E,0xFF,0xFF,0xFF,0x50,0xFF,0xFF,0xFF,0x52,0xFF,0xFF,0xFF,0x55,0xFF,0xFF,0xFF,0x57,0xFF,0xFF,0xFF,0x5A,0xFF,0xFF,0xFF,0x5C,0xFF,0xFF,0xFF,0x5E,0xFF,0xFF,0xFF,0x61,0xFF,0xFF,0xFF,0x63,0xFF,0xFF,0xFF,0x66,0xFF,0xFF,0xFF,0x68,0xFF,0xFF,0xFF,0x6A,0xFF,0xFF,0xFF,0x6D,0xFF,0xFF,0xFF,0x6F,0xFF,0xFF,0xFF,0x71,0xFF,0xFF,0xFF,0x74,0xFF,0xFF,0xFF,0x76,0xFF,0xFF,0xFF,0x79,0xFF,0xFF,0xFF,0x7B,0xFF,0xFF,0xFF,0x7D,0xFF,0xFF,0xFF,0x80,0xFF,0xFF,0xFF,0x82,0xFF,0xFF,0xFF,0x85,0xFF,0xFF,0xFF,0x87,0xFF,0xFF,0xFF,0x89,0xFF,0xFF,0xFF,0x8C,0xFF,0xFF,0xFF,0x8E,0xFF,0xFF,0xFF,0x91,0xFF,0xFF,0xFF,0x93,0xFF,0xFF,0xFF,0x95,0xFF,0xFF,0xFF,0x98,0xFF,0xFF,0xFF,0x9A,0xFF,0xFF,0xFF,0x9C,0xFF,0xFF,0xFF,0x9F,0xFF,0xFF,0xFF,0xA1,0xFF,0xFF,0xFF,0xA4,0xFF,0xFF,0xFF,0xA6,0xFF,0xFF,0xFF,0xA8,0xFF,0xFF,0xFF,0xAB,0xFF,0xFF,0xFF,0xAD,0xFF,0xFF,0xFF,0xB0,0xFF,0xFF,0xFF,0xB2,0xFF,0xFF,0xFF,0xB4,0xFF,0xFF,0xFF,0xB7,0xFF,0xFF,0xFF,0xB9,0xFF,0xFF,0xFF,0xBC,0xFF,0xFF,0xFF,0xBE,0xFF,0xFF,0xFF,0xC0,0xFF,0xFF,0xFF,0xC3,0xFF,0xFF,0xFF,0xC5,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xCA,0xFF,0xFF,0xFF,0xCC,0xFF,0xFF,0xFF,0xCF,0xFF,0xFF,0xFF,0xD1,0xFF,0xFF,0xFF,0xD3,0xFF,0xFF,0xFF,0xD6,0xFF,0xFF,0xFF,0xD8,0xFF,0xFF,0xFF,0xDB,
    0xFF,0xFF,0xFF,0xDD,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xE2,0xFF,0xFF,0xFF,0xE4,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xE9,0xFF,0xFF,0xFF,0xEB,0xFF,0xFF,0xFF,0xEE,0xFF,0xFF,0xFF,0xF0,0xFF,0xFF,0xFF,0xF2,0xFF,0xFF,0xFF,0xF5,0xFF,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFA,0xFF,0xFF,0xFF,0xFC,0xFF,0xFF,0xFF,0xFE,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xF8,0xFF,0xFF,0xFF,0xF6,0xFF,0xFF,0xFF,0xF3,0xFF,0xFF,0xFF,0xF1,0xFF,0xFF,0xFF,0xEE,0xFF,0xFF,0xFF,0xEC,0xFF,0xFF,0xFF,0xE9,0xFF,0xFF,0xFF,0xE7,0xFF,0xFF,0xFF,0xE4,0xFF,0xFF,0xFF,0xE2,0xFF,0xFF,0xFF,0xDF,0xFF,0xFF,0xFF,0xDD,0xFF,0xFF,0xFF,0xDB,0xFF,0xFF,0xFF,0xD8,0xFF,0xFF,0xFF,0xD6,0xFF,0xFF,0xFF,0xD3,0xFF,0xFF,0xFF,0xD1,0xFF,0xFF,0xFF,0xCE,0xFF,0xFF,0xFF,0xCC,0xFF,0xFF,0xFF,0xC9,0xFF,0xFF,0xFF,0xC7,0xFF,0xFF,0xFF,0xC4,0xFF,0xFF,0xFF,0xC2,0xFF,0xFF,0xFF,0xBF,0xFF,0xFF,0xFF,0xBD,0xFF,0xFF,0xFF,0xBA,0xFF,0xFF,0xFF,0xB8,0xFF,0xFF,0xFF,0xB6,0xFF,0xFF,0xFF,0xB3,0xFF,0xFF,0xFF,0xB1,0xFF,0xFF,0xFF,0xAE,0xFF,0xFF,0xFF,0xAC,0xFF,0xFF,0xFF,0xA9,0xFF,0xFF,0xFF,0xA7,0xFF,0xFF,0xFF,0xA4,0xFF,0xFF,0xFF,0xA2,0xFF,0xFF,0xFF,0x9F,0xFF,0xFF,0xFF,0x9D,0xFF,0xFF,0xFF,0x9A,0xFF,0xFF,0xFF,0x98,0xFF,0xFF,0xFF,0x95,0xFF,0xFF,0xFF,0x93,0xFF,0xFF,0xFF,0x90,0xFF,0xFF,0xFF,0x8E,0xFF,0xFF,0xFF,0x8C,0xFF,0xFF,0xFF,0x89,0xFF,0xFF,0xFF,0x87,
    0xFF,0xFF,0xFF,0x84,0xFF,0xFF,0xFF,0x82,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0x7D,0xFF,0xFF,0xFF,0x7A,0xFF,0xFF,0xFF,0x78,0xFF,0xFF,0xFF,0x75,0xFF,0xFF,0xFF,0x73,0xFF,0xFF,0xFF,0x70,0xFF,0xFF,0xFF,0x6E,0xFF,0xFF,0xFF,0x6B,0xFF,0xFF,0xFF,0x69,0xFF,0xFF,0xFF,0x66,0xFF,0xFF,0xFF,0x64,0xFF,0xFF,0xFF,0x62,0xFF,0xFF,0xFF,0x5F,0xFF,0xFF,0xFF,0x5D,0xFF,0xFF,0xFF,0x5A,0xFF,0xFF,0xFF,0x58,0xFF,0xFF,0xFF,0x55,0xFF,0xFF,0xFF,0x53,0xFF,0xFF,0xFF,0x50,0xFF,0xFF,0xFF,0x4E,0xFF,0xFF,0xFF,0x4B,0xFF,0xFF,0xFF,0x49,0xFF,0xFF,0xFF,0x46,0xFF,0xFF,0xFF,0x44,0xFF,0xFF,0xFF,0x41,0xFF,0xFF,0xFF,0x3F,0xFF,0xFF,0xFF,0x3D,0xFF,0xFF,0xFF,0x3A,0xFF,0xFF,0xFF,0x38,0xFF,0xFF,0xFF,0x35,0xFF,0xFF,0xFF,0x33,0xFF,0xFF,0xFF,0x30,0xFF,0xFF,0xFF,0x2E,0xFF,0xFF,0xFF,0x2B,0xFF,0xFF,0xFF,0x29,0xFF,0xFF,0xFF,0x26,0xFF,0xFF,0xFF,0x24,0xFF,0xFF,0xFF,0x21,0xFF,0xFF,0xFF,0x1F,0xFF,0xFF,0xFF,0x1C,0xFF,0xFF,0xFF,0x1A,0xFF,0xFF,0xFF,0x17,0xFF,0xFF,0xFF,0x15,0xFF,0xFF,0xFF,0x13,0xFF,0xFF,0xFF,0x10,0xFF,0xFF,0xFF,0x0E,0xFF,0xFF,0xFF,0x0B,0xFF,0xFF,0xFF,0x09,0xFF,0xFF,0xFF,0x06,0xFF,0xFF,0xFF,0x04,0xFF,0xFF,0xFF,0x01,
};
const lv_img_dsc_t ui_img_dark_ic_line_light_png = {
    .header.always_zero = 0,
    .header.w = 210,
    .header.h = 3,
    .data_size = sizeof(ui_img_dark_ic_line_light_png_data),
    .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
    .data = ui_img_dark_ic_line_light_png_data
};

