//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

// IMAGE DATA: assets/dark/ic_sta_signals_f.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t ui_img_dark_ic_sta_signals_f_png_data[] = {
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xAA,0x0E,0x0E,0x12,0xBE,0x11,0x1A,0x3B,0xCA,0x15,0x1C,0x48,0xC7,0x13,0x1A,0x44,0xBB,0x11,0x11,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD0,0x15,0x1E,0x3C,0xE7,0x1E,0x27,0x55,0xE7,0x1E,0x27,0x55,0xE7,0x1E,0x27,0x55,0xE7,0x1E,0x27,0x54,0xE4,0x1E,0x27,0x54,0xE4,0x1E,0x24,0x54,0xE4,0x1E,0x24,0x54,0xE1,0x1B,0x24,0x54,0xC9,0x16,0x1B,0x2F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDB,0x1A,0x22,0xA5,0xE7,0x1E,0x26,0xFF,0xE7,0x1E,0x26,0xFF,0xE7,0x1E,0x27,0xFF,0xE7,0x1E,0x27,0xFF,0xE8,0x1E,0x27,0xFF,0xE0,0x1C,0x24,0xEE,0xDE,0x1C,0x22,0x9C,0xCE,0x16,0x1B,0x2F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDB,0x1A,0x21,0x4D,0xED,0x20,0x29,0xFF,0xEC,0x1F,0x29,0xFF,0xEB,0x1F,0x29,0xFF,0xEA,0x1F,0x28,0xFF,0xE9,0x1E,0x28,0xFF,0xE8,0x1E,0x27,0xFF,0xE7,0x1E,0x27,0xFF,0xE7,0x1E,0x26,0xFF,0xE6,0x1E,0x26,0xFF,0xE4,0x1D,0x25,0xFD,0xCA,0x15,0x1B,0x30,0x00,0x00,0x00,0x00,0xDE,0x1B,0x23,0xF0,0xE5,0x1D,0x26,0xFF,0xE5,0x1D,0x26,0xFF,0xE5,0x1D,0x26,0xFF,
    0xE5,0x1D,0x26,0xFF,0xE6,0x1D,0x26,0xFF,0xE7,0x1E,0x26,0xFF,0xE7,0x1E,0x27,0xFF,0xE7,0x1E,0x27,0xFE,0xDD,0x1B,0x23,0x9F,0xB9,0x17,0x17,0x0B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD9,0x1C,0x23,0x49,0xEC,0x1F,0x29,0xFF,0xEB,0x1F,0x29,0xFF,0xE9,0x1E,0x28,0xFF,0xE8,0x1E,0x28,0xFF,0xE7,0x1E,0x27,0xFF,0xE6,0x1E,0x26,0xFF,0xE5,0x1D,0x26,0xFF,0xE5,0x1D,0x26,0xFF,0xE4,0x1D,0x25,0xFF,0xE2,0x1C,0x24,0xFC,0xC6,0x17,0x1C,0x2D,0xB6,0x0F,0x16,0x23,0xE3,0x1D,0x24,0xFF,0xE3,0x1D,0x24,0xFF,0xE3,0x1D,0x24,0xFF,0xE3,0x1D,0x25,0xFF,0xE4,0x1D,0x25,0xFF,0xE4,0x1D,0x26,0xFF,0xE5,0x1D,0x26,0xFF,0xE6,0x1D,0x26,0xFF,0xE7,0x1E,0x26,0xFF,0xE8,0x1E,0x28,0xFF,0xE2,0x1B,0x25,0xD6,0xD0,0x13,0x1C,0x1B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCB,0x18,0x1C,0x36,0xE1,0x1B,0x24,0x54,0xE1,0x1B,0x24,0x54,0xDD,0x1C,0x22,0x53,0xDD,0x19,0x22,0x53,0xDD,0x19,0x22,0x53,0xDA,0x19,0x22,0x53,0xDA,0x19,0x22,0x53,0xDA,0x19,0x1F,0x52,0xC7,0x13,0x19,0x29,0x00,0x00,0x00,0x00,0xD1,0x17,0x1E,0x4D,0xE1,0x1C,0x24,0xFF,0xE1,0x1C,0x24,0xFF,0xE0,0x1C,0x23,0xFE,0xB1,0x14,0x14,0x0D,0xB5,0x10,0x10,0x1F,0xCD,0x16,0x1D,0x47,0xD9,0x1A,0x22,0x94,0xE1,0x1C,0x24,0xF7,0xE6,0x1D,0x26,0xFF,0xE7,0x1E,0x26,0xFF,
    0xE8,0x1E,0x28,0xFF,0xE1,0x1C,0x25,0xD4,0xBB,0x11,0x11,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD5,0x17,0x20,0x6E,0xE0,0x1C,0x23,0xFF,0xE0,0x1C,0x24,0xFF,0xD9,0x1A,0x21,0xEB,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC2,0x14,0x14,0x19,0xDC,0x1B,0x23,0xC0,0xE6,0x1E,0x26,0xFF,0xE7,0x1E,0x27,0xFF,0xE8,0x1E,0x28,0xFF,0xDE,0x1B,0x22,0xA3,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC4,0x14,0x1A,0x27,0xD1,0x17,0x1E,0x4D,0xCD,0x17,0x1B,0x4C,0xCD,0x17,0x1B,0x4C,0xCC,0x14,0x1B,0x4B,0xCC,0x14,0x1B,0x4B,0xCB,0x15,0x1C,0x4A,0xC6,0x15,0x1C,0x48,0xB3,0x0D,0x1A,0x14,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD3,0x16,0x1F,0x95,0xDF,0x1B,0x23,0xFF,0xE0,0x1C,0x23,0xFF,0xDA,0x1A,0x21,0xC3,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x99,0x00,0x00,0x05,0xDD,0x1B,0x23,0xBE,0xE6,0x1E,0x26,0xFF,0xE7,0x1E,0x27,0xFF,0xE7,0x1E,0x27,0xFE,0xD3,0x16,0x1C,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0xD8,0x1B,0x1F,0x41,0xE8,0x1E,0x27,0xFE,0xE8,0x1E,0x27,0xFF,0xE5,0x1E,0x26,0xFF,0xE4,0x1D,0x25,0xFF,0xE2,0x1C,0x24,0xFF,0xE1,0x1C,0x24,0xFF,0xE0,0x1B,0x23,0xFF,0xDF,0x1B,0x23,0xFF,0xDB,0x1A,0x20,0xED,0xB8,0x0E,0x0E,0x12,0x00,0x00,0x00,0x00,0xD8,0x19,0x21,0xA4,0xDF,0x1B,0x23,0xFF,0xDF,0x1B,0x23,0xFF,0xDC,0x1A,0x22,0xA7,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xBF,0x12,0x1B,0x1C,0xE2,0x1C,0x24,0xF6,0xE6,0x1E,0x27,0xFF,0xE8,0x1E,0x28,0xFF,0xDE,0x1C,0x22,0x94,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDA,0x19,0x22,0x53,0xE9,0x1E,0x28,0xFF,0xE7,0x1E,0x27,0xFF,0xE5,0x1D,0x26,0xFF,0xE4,0x1D,0x25,0xFF,0xE2,0x1C,0x24,0xFF,0xE0,0x1B,0x24,0xFF,0xDF,0x1B,0x23,0xFF,0xDF,0x1B,0x22,0xFF,0xDC,0x1A,0x21,0xFA,0xBA,0x14,0x14,0x1A,0x00,0x00,0x00,0x00,0xDC,0x1A,0x23,0xA9,0xDF,0x1A,0x23,0xFF,0xDF,0x1B,0x23,0xFF,0xD5,0x18,0x1F,0x9D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDB,0x1C,0x22,0x9D,0xE6,0x1E,0x26,0xFF,0xE8,0x1E,0x27,0xFF,0xE0,0x1C,0x24,0xDE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD7,0x19,0x20,0x47,0xDA,0x1A,0x23,0x6E,0xD8,0x19,0x20,0x6F,0xD8,0x1A,0x20,0x6E,0xD5,0x1A,0x1E,0x6E,
    0xD5,0x17,0x1E,0x6D,0xD5,0x17,0x1E,0x6D,0xD5,0x19,0x1E,0x67,0xCD,0x16,0x1C,0x2E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDC,0x1A,0x21,0xB4,0xDE,0x1A,0x23,0xFF,0xDF,0x1A,0x23,0xFF,0xD2,0x18,0x1D,0x94,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDB,0x1B,0x21,0x55,0xE6,0x1E,0x26,0xFF,0xE7,0x1E,0x27,0xFF,0xE5,0x1D,0x26,0xFD,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD8,0x18,0x20,0xC2,0xDE,0x1A,0x22,0xFF,0xDE,0x1A,0x23,0xFF,0xD3,0x19,0x1E,0x90,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD1,0x17,0x1E,0x4D,0xE6,0x1E,0x26,0xFF,0xE7,0x1E,0x27,0xFF,0xE8,0x1E,0x27,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xAF,0x10,0x10,0x10,0xBE,0x0E,0x17,0x37,0xBE,0x0E,0x17,0x37,0xBD,0x13,0x18,0x36,0xBC,0x13,0x18,0x35,0xBC,0x13,0x18,0x35,0xBA,0x0F,0x14,0x34,0xBA,0x10,0x15,0x30,0x99,0x00,0x00,0x05,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0xDA,0x19,0x20,0xB5,0xDE,0x1A,0x22,0xFF,0xDF,0x1A,0x23,0xFF,0xD3,0x18,0x1E,0x97,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDC,0x1B,0x23,0x5F,0xE6,0x1E,0x26,0xFF,0xE7,0x1E,0x27,0xFF,0xE4,0x1C,0x25,0xFB,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD9,0x18,0x21,0x36,0xE5,0x1D,0x26,0xF9,0xE7,0x1E,0x27,0xFF,0xE5,0x1D,0x26,0xFF,0xE3,0x1D,0x25,0xFF,0xE1,0x1C,0x24,0xFF,0xE0,0x1B,0x24,0xFF,0xDF,0x1B,0x22,0xFF,0xDF,0x1B,0x22,0xFF,0xD8,0x1A,0x20,0xE1,0xB1,0x14,0x14,0x0D,0x00,0x00,0x00,0x00,0xDC,0x1A,0x23,0xA9,0xDF,0x1A,0x23,0xFF,0xDF,0x1B,0x23,0xFF,0xD5,0x18,0x1F,0x9E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDC,0x1C,0x22,0xAF,0xE6,0x1E,0x26,0xFF,0xE8,0x1E,0x28,0xFF,0xE1,0x1C,0x24,0xCF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDB,0x19,0x22,0x5B,0xEA,0x1F,0x28,0xFF,0xE7,0x1E,0x27,0xFF,0xE5,0x1E,0x26,0xFF,0xE4,0x1D,0x25,0xFF,0xE2,0x1C,0x24,0xFF,0xE1,0x1C,0x24,0xFF,0xE0,0x1B,0x23,0xFF,0xDF,0x1B,0x23,0xFF,0xDD,0x1A,0x21,0xFD,0xB5,0x10,0x10,0x1F,0x00,0x00,0x00,0x00,0xD8,0x1A,0x20,0xA5,0xDF,0x1B,0x23,0xFF,0xDF,0x1B,0x23,0xFF,0xDC,0x1A,0x20,0xA6,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCF,0x15,0x1B,0x30,0xE4,0x1D,0x25,0xFD,0xE7,0x1E,0x27,0xFF,0xE8,0x1E,0x27,0xFF,0xDE,0x19,0x21,0x83,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x00,0x00,0x01,0xD7,0x19,0x21,0x65,0xD7,0x18,0x21,0x92,0xD7,0x18,0x1F,0x92,0xD5,0x19,0x20,0x91,0xD5,0x19,0x20,0x90,0xD3,0x19,0x20,0x90,0xD2,0x17,0x1E,0x8F,0xD1,0x18,0x1D,0x8B,0xCE,0x17,0x1E,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD3,0x17,0x1E,0x98,0xDF,0x1B,0x23,0xFF,0xE0,0x1C,0x23,0xFF,0xDA,0x1A,0x21,0xC3,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC5,0x17,0x17,0x16,0xE0,0x1C,0x23,0xDD,0xE6,0x1E,0x26,0xFF,0xE8,0x1E,0x27,0xFF,0xE6,0x1D,0x26,0xFA,0xC1,0x12,0x1A,0x1D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD3,0x18,0x1F,0x75,0xE0,0x1C,0x23,0xFF,0xE1,0x1C,0x24,0xFF,0xD9,0x19,0x21,0xE8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCE,0x17,0x1E,0x43,0xE1,0x1C,0x24,0xE2,0xE6,0x1E,0x26,0xFF,0xE7,0x1E,0x27,0xFF,
    0xE8,0x1E,0x28,0xFF,0xDB,0x1A,0x24,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xAA,0x0E,0x0E,0x12,0xAA,0x0E,0x0E,0x12,0xAA,0x0E,0x0E,0x12,0xAA,0x0E,0x0E,0x12,0xA5,0x0F,0x0F,0x11,0xB4,0x0F,0x0F,0x11,0xB4,0x0F,0x0F,0x11,0xB6,0x12,0x12,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD6,0x1A,0x20,0x50,0xE2,0x1C,0x24,0xFF,0xE2,0x1C,0x24,0xFF,0xE0,0x1C,0x23,0xFD,0xCC,0x14,0x1B,0x4B,0xD6,0x1A,0x1D,0x50,0xD9,0x1A,0x22,0x78,0xDE,0x1B,0x23,0xC6,0xE5,0x1D,0x25,0xFF,0xE6,0x1D,0x26,0xFF,0xE7,0x1E,0x26,0xFF,0xE8,0x1E,0x28,0xFF,0xDF,0x1C,0x24,0xB0,0xFF,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD8,0x1C,0x21,0x2E,0xE6,0x1E,0x27,0xEF,0xEA,0x1F,0x29,0xFF,0xE9,0x1E,0x28,0xFF,0xE8,0x1E,0x27,0xFF,0xE7,0x1E,0x26,0xFF,0xE6,0x1E,0x26,0xFF,0xE5,0x1D,0x26,0xFF,0xE5,0x1D,0x26,0xFF,0xE4,0x1D,0x25,0xFF,0xDE,0x1B,0x23,0xE1,0xC4,0x14,0x1D,0x1A,0xBC,0x12,0x18,0x2A,0xE4,0x1D,0x24,0xFF,0xE3,0x1D,0x25,0xFF,0xE3,0x1D,0x25,0xFF,0xE3,0x1D,0x25,0xFF,0xE4,0x1D,0x26,0xFF,0xE5,0x1D,0x26,0xFF,0xE5,0x1D,0x26,0xFF,0xE6,0x1E,0x26,0xFF,0xE7,0x1E,0x27,0xFF,0xE8,0x1E,0x28,0xFF,0xDE,0x1B,0x22,0xAA,0xB3,0x1A,0x1A,0x0A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDF,0x1D,0x25,0x61,
    0xED,0x20,0x2A,0xFF,0xEC,0x1F,0x29,0xFF,0xEB,0x1F,0x29,0xFF,0xE9,0x1E,0x28,0xFF,0xE8,0x1E,0x28,0xFF,0xE7,0x1E,0x27,0xFF,0xE7,0x1E,0x26,0xFF,0xE6,0x1E,0x26,0xFF,0xE6,0x1E,0x26,0xFF,0xE5,0x1E,0x26,0xFF,0xCB,0x17,0x1A,0x44,0x00,0x00,0x00,0x00,0xE0,0x1C,0x23,0xF3,0xE5,0x1D,0x26,0xFF,0xE5,0x1D,0x26,0xFF,0xE6,0x1D,0x26,0xFF,0xE6,0x1D,0x26,0xFF,0xE6,0x1E,0x26,0xFF,0xE7,0x1E,0x26,0xFF,0xE8,0x1E,0x27,0xFF,0xE4,0x1C,0x26,0xEF,0xD7,0x1A,0x21,0x6C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xB6,0x00,0x00,0x07,0xDD,0x1C,0x24,0x80,0xE2,0x1C,0x25,0xA5,0xE2,0x1A,0x24,0xA5,0xE0,0x1C,0x24,0xA5,0xDF,0x1C,0x24,0xA5,0xDE,0x1C,0x24,0xA4,0xDE,0x1A,0x24,0xA4,0xDE,0x1A,0x24,0xA4,0xDD,0x1B,0x22,0xA3,0xD4,0x19,0x20,0x70,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xD5,0x1A,0x21,0x6D,0xDF,0x1C,0x24,0xC7,0xE0,0x1C,0x23,0xF7,0xE5,0x1D,0x26,0xFE,0xE5,0x1D,0x26,0xFE,0xDF,0x1C,0x24,0xF1,0xE1,0x1C,0x25,0xB5,0xD8,0x1B,0x22,0x69,0xAA,0x15,0x15,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const lv_img_dsc_t ui_img_dark_ic_sta_signals_f_png = {
    .header.always_zero = 0,
    .header.w = 29,
    .header.h = 29,
    .data_size = sizeof(ui_img_dark_ic_sta_signals_f_png_data),
    .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
    .data = ui_img_dark_ic_sta_signals_f_png_data
};

