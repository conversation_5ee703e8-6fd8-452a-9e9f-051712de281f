//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

// IMAGE DATA: assets/setting/ic_setting_keyboard.png
const LV_ATTRIBUTE_MEM_ALIGN uint8_t ui_img_setting_ic_setting_keyboard_png_data[] = {
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC8,0x03,0x5D,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC7,0x02,0x9F,0xFF,0xC8,0x03,0x5D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC5,0x07,0x95,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x05,0xFF,0xFF,0xC5,0x05,0xFF,0xFF,0xC5,0x05,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC6,0x06,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x05,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x05,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x05,0xFF,0xFF,0xC5,0x05,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x06,0xFF,
    0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x06,0xFF,0xFF,0xC5,0x07,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC3,0x0A,0x95,0xFF,0xC3,0x0B,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0B,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0B,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0B,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0B,0xFF,0xFF,0xC3,0x0B,0xFF,0xFF,0xC3,0x0B,0xFF,0xFF,0xC3,0x0B,0xFF,0xFF,0xC3,0x0B,0xFF,0xFF,0xC3,0x0A,0xFF,0xFF,0xC3,0x0A,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC1,0x0F,0x95,0xFF,0xC1,0x10,0xFF,0xFF,0xC1,0x0F,0xFF,0xFF,0xBF,0x0D,0x28,0xFF,0xBF,0x10,0x10,0xFF,0xBF,0x10,0x10,0xFF,0xBF,0x10,0x10,0xFF,0xC0,0x0F,0xE0,0xFF,0xC2,0x10,0x81,0xFF,0xBF,0x10,0x10,0xFF,0xBF,0x10,0x10,0xFF,0xC1,0x0F,0xD9,0xFF,0xC1,0x0F,0x87,0xFF,0xBF,0x10,0x10,0xFF,0xBF,0x10,0x10,0xFF,0xC1,0x10,0xD3,0xFF,0xC0,0x0E,0x8E,0xFF,0xBF,0x10,0x10,0xFF,0xBF,0x10,0x10,0xFF,0xBF,0x10,0x10,0xFF,0xBF,0x10,0x10,0xFF,0xBF,0x0D,0x28,0xFF,0xC1,0x10,0xFF,0xFF,0xC1,0x0F,0xFF,0xFF,0xC1,0x0F,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0xFF,0xBE,0x15,0x95,0xFF,0xBE,0x14,0xFF,0xFF,0xBE,0x14,0xFF,0xFF,0xBA,0x14,0x1A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBE,0x14,0xDE,0xFF,0xBD,0x15,0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBE,0x14,0xD7,0xFF,0xBD,0x14,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBE,0x14,0xD0,0xFF,0xBF,0x15,0x87,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBA,0x14,0x1A,0xFF,0xBE,0x14,0xFF,0xFF,0xBE,0x14,0xFF,0xFF,0xBE,0x15,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xBC,0x1A,0x95,0xFF,0xBC,0x19,0xFF,0xFF,0xBB,0x19,0xFF,0xFF,0xBC,0x1B,0x54,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x1B,0x41,0xFF,0xBB,0x1A,0xE6,0xFF,0xBD,0x1A,0x9A,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x19,0xE1,0xFF,0xBA,0x1A,0xA0,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x1A,0xDC,0xFF,0xBB,0x1A,0xA5,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x1B,0x41,0xFF,0xBC,0x1B,0x54,0xFF,0xBC,0x19,0xFF,0xFF,0xBB,0x19,0xFF,0xFF,0xBC,0x1A,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB9,0x1F,0x95,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,
    0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1E,0xFF,0xFF,0xB9,0x1F,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB7,0x22,0x95,0xFF,0xB7,0x23,0xFF,0xFF,0xB7,0x23,0xFF,0xFF,0xB8,0x21,0x2F,0xFF,0xBC,0x21,0x17,0xFF,0xB8,0x23,0x48,0xFF,0xB7,0x23,0xFF,0xFF,0xB7,0x22,0x35,0xFF,0xBC,0x21,0x17,0xFF,0xB6,0x23,0x42,0xFF,0xB7,0x23,0xFF,0xFF,0xBA,0x23,0x3B,0xFF,0xBC,0x21,0x17,0xFF,0xBA,0x23,0x3B,0xFF,0xB7,0x23,0xFF,0xFF,0xB6,0x23,0x42,0xFF,0xBC,0x21,0x17,0xFF,0xB7,0x22,0x35,0xFF,0xB7,0x23,0xFF,0xFF,0xB8,0x23,0x48,0xFF,0xBC,0x21,0x17,0xFF,0xB8,0x21,0x2F,0xFF,0xB7,0x23,0xFF,0xFF,0xB6,0x23,0xFF,0xFF,0xB7,0x22,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB5,0x27,0x95,0xFF,0xB5,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB1,0x27,0x1A,0x00,0x00,0x00,0x00,0xFF,0xB3,0x26,0x36,0xFF,0xB4,0x28,0xFF,0xFF,0xB2,0x27,0x21,0x00,0x00,0x00,0x00,0xFF,0xB3,0x26,0x2F,0xFF,0xB4,0x28,0xFF,
    0xFF,0xB3,0x26,0x28,0x00,0x00,0x00,0x00,0xFF,0xB3,0x26,0x28,0xFF,0xB4,0x28,0xFF,0xFF,0xB3,0x26,0x2F,0x00,0x00,0x00,0x00,0xFF,0xB2,0x27,0x21,0xFF,0xB5,0x28,0xFF,0xFF,0xB3,0x2A,0x36,0x00,0x00,0x00,0x00,0xFF,0xB1,0x27,0x1A,0xFF,0xB5,0x28,0xFF,0xFF,0xB4,0x28,0xFF,0xFF,0xB4,0x27,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB2,0x2C,0x95,0xFF,0xB2,0x2D,0xFF,0xFF,0xB2,0x2D,0xFF,0xFF,0xB1,0x2E,0x4E,0xFF,0xB3,0x31,0x39,0xFF,0xB2,0x2C,0x63,0xFF,0xB2,0x2D,0xFF,0xFF,0xB2,0x2E,0x53,0xFF,0xAE,0x31,0x39,0xFF,0xB0,0x2E,0x5E,0xFF,0xB2,0x2D,0xFF,0xFF,0xB1,0x2E,0x58,0xFF,0xAE,0x31,0x39,0xFF,0xB1,0x2E,0x58,0xFF,0xB2,0x2D,0xFF,0xFF,0xB0,0x2E,0x5E,0xFF,0xAE,0x31,0x39,0xFF,0xB2,0x2E,0x53,0xFF,0xB2,0x2D,0xFF,0xFF,0xB2,0x2E,0x63,0xFF,0xB3,0x31,0x39,0xFF,0xB1,0x2E,0x4E,0xFF,0xB2,0x2D,0xFF,0xFF,0xB2,0x2D,0xFF,0xFF,0xB2,0x2C,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xB0,0x32,0x95,0xFF,0xB0,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xB0,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xB0,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,
    0xFF,0xAF,0x32,0xFF,0xFF,0xB0,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xAF,0x32,0xFF,0xFF,0xB0,0x32,0xFF,0xFF,0xB0,0x32,0xFF,0xFF,0xB0,0x32,0xFF,0xFF,0xAF,0x32,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAD,0x37,0x95,0xFF,0xAD,0x37,0xFF,0xFF,0xAD,0x37,0xFF,0xFF,0xAD,0x35,0x35,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAD,0x37,0xE2,0xFF,0xAD,0x36,0x88,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAD,0x36,0x88,0xFF,0xAD,0x37,0xE2,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAA,0x33,0x1E,0xFF,0xAD,0x35,0x35,0xFF,0xAD,0x37,0xFF,0xFF,0xAD,0x37,0xFF,0xFF,0xAD,0x37,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAB,0x3C,0x95,0xFF,0xAB,0x3C,0xFF,0xFF,0xAB,0x3C,0xFF,0xFF,0xA7,0x3B,0x1A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAB,0x3C,0xDE,0xFF,0xAA,0x3B,0x78,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xAA,0x3B,0x78,0xFF,0xAB,0x3C,0xDE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xA7,0x3B,0x1A,0xFF,0xAB,0x3C,0xFF,
    0xFF,0xAB,0x3C,0xFF,0xFF,0xAB,0x3C,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xA8,0x41,0x95,0xFF,0xA8,0x41,0xFF,0xFF,0xA8,0x41,0xFF,0xFF,0xA9,0x44,0x47,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x41,0xE5,0xFF,0xA8,0x40,0x93,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x40,0x93,0xFF,0xA8,0x41,0xE5,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA8,0x42,0x32,0xFF,0xA5,0x41,0x47,0xFF,0xA8,0x41,0xFF,0xFF,0xA8,0x41,0xFF,0xFF,0xA8,0x41,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xA6,0x46,0x95,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x45,0xFF,0xFF,0xA6,0x45,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x45,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0xFF,0xFF,0xA6,0x46,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0xFF,0xA3,0x4B,0x95,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA4,0x4A,0xFF,0xFF,0xA4,0x4A,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA4,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4A,0xFF,0xFF,0xA4,0x4B,0xFF,0xFF,0xA4,0x4A,0xFF,0xFF,0xA3,0x4A,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0xFF,0xFF,0xA3,0x4B,0x95,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xA1,0x50,0x49,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA0,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA0,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA2,0x4E,0x7C,0xFF,0xA1,0x50,0x49,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const lv_img_dsc_t ui_img_setting_ic_setting_keyboard_png = {
    .header.always_zero = 0,
    .header.w = 29,
    .header.h = 30,
    .data_size = sizeof(ui_img_setting_ic_setting_keyboard_png_data),
    .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
    .data = ui_img_setting_ic_setting_keyboard_png_data
};

