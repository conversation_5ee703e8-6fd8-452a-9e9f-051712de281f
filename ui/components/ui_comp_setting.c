//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

void ui_event_comp_Setting_Settingbtn1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_Setting = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_state_modify(comp_Setting[UI_COMP_SETTING_SETTINGBTN3], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_Setting[UI_COMP_SETTING_SETTINGBTN2], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_Setting[UI_COMP_SETTING_SETTINGBTN1], LV_STATE_CHECKED, _UI_MODIFY_STATE_ADD);
    }
    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP], LV_OBJ_FLAG_HIDDEN,
                        _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP], LV_OBJ_FLAG_HIDDEN,
                        _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP], LV_OBJ_FLAG_HIDDEN,
                        _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_Setting_Settingbtn2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_Setting = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_state_modify(comp_Setting[UI_COMP_SETTING_SETTINGBTN3], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_Setting[UI_COMP_SETTING_SETTINGBTN1], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_Setting[UI_COMP_SETTING_SETTINGBTN2], LV_STATE_CHECKED, _UI_MODIFY_STATE_ADD);
    }
    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP], LV_OBJ_FLAG_HIDDEN,
                        _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP], LV_OBJ_FLAG_HIDDEN,
                        _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP], LV_OBJ_FLAG_HIDDEN,
                        _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_Setting_Settingbtn3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_Setting = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_state_modify(comp_Setting[UI_COMP_SETTING_SETTINGBTN2], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_Setting[UI_COMP_SETTING_SETTINGBTN1], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_Setting[UI_COMP_SETTING_SETTINGBTN3], LV_STATE_CHECKED, _UI_MODIFY_STATE_ADD);
    }
    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP], LV_OBJ_FLAG_HIDDEN,
                        _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP], LV_OBJ_FLAG_HIDDEN,
                        _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP], LV_OBJ_FLAG_HIDDEN,
                        _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_Setting_settinglistitem5(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_Setting = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_dialog, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_Setting_list_item_label9(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_Setting = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_dialog, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_Setting_settinglistitem11(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_Setting = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_dialog, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_Setting_list_item_label2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_Setting = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_dialog, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_Setting_SliderVolume2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t ** comp_Setting = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_VALUE_CHANGED) {
        _ui_slider_set_text_value(
            comp_Setting[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_RIGHT6_LIST_ITEM_LABEL_EXTEND6],
            target, "", "");
    }
}

// COMPONENT Setting

lv_obj_t * ui_Setting_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Setting;
    cui_Setting = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_Setting, 876);
    lv_obj_set_height(cui_Setting, 476);
    lv_obj_set_x(cui_Setting, 48);
    lv_obj_set_y(cui_Setting, 40);
    lv_obj_set_align(cui_Setting, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_Setting, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_Setting, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Setting, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Setting, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Setting, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_Setting, &ui_img_dark_bg_dashboard_main_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_setting_warp_left;
    cui_setting_warp_left = lv_obj_create(cui_Setting);
    lv_obj_set_height(cui_setting_warp_left, lv_pct(100));
    lv_obj_set_flex_grow(cui_setting_warp_left, 3);
    lv_obj_set_x(cui_setting_warp_left, 1);
    lv_obj_set_y(cui_setting_warp_left, 0);
    lv_obj_set_align(cui_setting_warp_left, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_setting_warp_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_setting_warp_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_setting_warp_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_setting_warp_left_label;
    cui_setting_warp_left_label = lv_obj_create(cui_setting_warp_left);
    lv_obj_set_height(cui_setting_warp_left_label, 78);
    lv_obj_set_width(cui_setting_warp_left_label, lv_pct(100));
    lv_obj_set_flex_flow(cui_setting_warp_left_label, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_setting_warp_left_label, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_setting_warp_left_label, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_setting_warp_left_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_setting_warp_left_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_setting_title_label;
    cui_setting_title_label = lv_label_create(cui_setting_warp_left_label);
    lv_obj_set_width(cui_setting_title_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_setting_title_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_setting_title_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_setting_title_label, "#SETTING@26");
    lv_obj_set_style_text_color(cui_setting_title_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_setting_title_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_setting_title_label, &ui_font_AlibabaPuHui26, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_setting_title_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_setting_title_label, 28, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_setting_title_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_setting_title_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_setting_warp_left_menus;
    cui_setting_warp_left_menus = lv_obj_create(cui_setting_warp_left);
    lv_obj_set_width(cui_setting_warp_left_menus, lv_pct(100));
    lv_obj_set_height(cui_setting_warp_left_menus, lv_pct(82));
    lv_obj_set_align(cui_setting_warp_left_menus, LV_ALIGN_BOTTOM_RIGHT);
    lv_obj_set_flex_flow(cui_setting_warp_left_menus, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_setting_warp_left_menus, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_bg_color(cui_setting_warp_left_menus, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_setting_warp_left_menus, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Settingbtn1;
    cui_Settingbtn1 = ui_Settingbtn_create(cui_setting_warp_left_menus);
    lv_obj_set_x(cui_Settingbtn1, -299);
    lv_obj_set_y(cui_Settingbtn1, -14);
    lv_obj_add_state(cui_Settingbtn1, LV_STATE_CHECKED);       /// States

    lv_label_set_long_mode(ui_comp_get_child(cui_Settingbtn1, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), LV_LABEL_LONG_WRAP);
    lv_label_set_text(ui_comp_get_child(cui_Settingbtn1, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), "#SYSTEM@18");

    lv_obj_t * cui_Settingbtn2;
    cui_Settingbtn2 = ui_Settingbtn_create(cui_setting_warp_left_menus);
    lv_obj_set_x(cui_Settingbtn2, -299);
    lv_obj_set_y(cui_Settingbtn2, -14);

    lv_label_set_long_mode(ui_comp_get_child(cui_Settingbtn2, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), LV_LABEL_LONG_WRAP);
    lv_label_set_text(ui_comp_get_child(cui_Settingbtn2, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), "#VEHICLE@18");

    lv_obj_t * cui_Settingbtn3;
    cui_Settingbtn3 = ui_Settingbtn_create(cui_setting_warp_left_menus);
    lv_obj_set_x(cui_Settingbtn3, -299);
    lv_obj_set_y(cui_Settingbtn3, -14);

    lv_label_set_long_mode(ui_comp_get_child(cui_Settingbtn3, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), LV_LABEL_LONG_WRAP);
    lv_label_set_text(ui_comp_get_child(cui_Settingbtn3, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), "#OTHER@18");

    lv_obj_t * cui_Spaceline;
    cui_Spaceline = ui_Spaceline_create(cui_setting_warp_left);
    lv_obj_set_x(cui_Spaceline, 82);
    lv_obj_set_y(cui_Spaceline, 0);

    lv_obj_t * cui_setting_warp_right;
    cui_setting_warp_right = lv_obj_create(cui_Setting);
    lv_obj_set_height(cui_setting_warp_right, lv_pct(100));
    lv_obj_set_flex_grow(cui_setting_warp_right, 11);
    lv_obj_set_x(cui_setting_warp_right, 141);
    lv_obj_set_y(cui_setting_warp_right, -5);
    lv_obj_set_align(cui_setting_warp_right, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(cui_setting_warp_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_setting_warp_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_setting_warp_right, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_setting_warp_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_setting_warp_right, 26, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_setting_warp_right, 25, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_system_setting_warp;
    cui_system_setting_warp = lv_obj_create(cui_setting_warp_right);
    lv_obj_set_width(cui_system_setting_warp, lv_pct(100));
    lv_obj_set_height(cui_system_setting_warp, lv_pct(100));
    lv_obj_set_align(cui_system_setting_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_system_setting_warp, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(cui_system_setting_warp, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_set_style_bg_color(cui_system_setting_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_system_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_system_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_system_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_system_setting_warp, 30, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_system_setting_warp, 30, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_system_setting_warp1;
    cui_system_setting_warp1 = lv_obj_create(cui_system_setting_warp);
    lv_obj_set_width(cui_system_setting_warp1, 329);
    lv_obj_set_height(cui_system_setting_warp1, 115);
    lv_obj_set_x(cui_system_setting_warp1, -20);
    lv_obj_set_y(cui_system_setting_warp1, 94);
    lv_obj_set_align(cui_system_setting_warp1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_system_setting_warp1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_system_setting_warp1, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_system_setting_warp1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_system_setting_warp1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_system_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_system_setting_warp1, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_system_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_system_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_system_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem5;
    cui_settinglistitem5 = lv_obj_create(cui_system_setting_warp1);
    lv_obj_set_width(cui_settinglistitem5, 313);
    lv_obj_set_height(cui_settinglistitem5, 95);
    lv_obj_set_align(cui_settinglistitem5, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem5, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem5, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem5, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem5, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem5, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem5, &ui_img_dark_ic_setting_btn_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem5, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem5, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem5, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem5, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon9;
    cui_list_item_icon9 = lv_img_create(cui_settinglistitem5);
    lv_img_set_src(cui_list_item_icon9, &ui_img_setting_ic_setting_wifi_png);
    lv_obj_set_width(cui_list_item_icon9, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon9, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon9, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon9, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon9, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label9;
    cui_list_item_label9 = lv_obj_create(cui_settinglistitem5);
    lv_obj_set_width(cui_list_item_label9, 213);
    lv_obj_set_height(cui_list_item_label9, 50);
    lv_obj_set_align(cui_list_item_label9, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label9, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label9, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label9, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label9, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label9, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_left9;
    cui_list_item_label_left9 = lv_obj_create(cui_list_item_label9);
    lv_obj_set_height(cui_list_item_label_left9, 50);
    lv_obj_set_width(cui_list_item_label_left9, LV_SIZE_CONTENT);   /// 106
    lv_obj_set_x(cui_list_item_label_left9, -25);
    lv_obj_set_y(cui_list_item_label_left9, 4);
    lv_obj_set_align(cui_list_item_label_left9, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_left9, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_left9, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_left9, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_left9, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_left9, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label_left9, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label_left9, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label_left9, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label_left9, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value9;
    cui_list_item_label_value9 = lv_label_create(cui_list_item_label_left9);
    lv_obj_set_width(cui_list_item_label_value9, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value9, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value9, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value9, "#WIFI");
    lv_obj_set_style_text_color(cui_list_item_label_value9, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value9, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_right9;
    cui_list_item_label_right9 = lv_obj_create(cui_list_item_label9);
    lv_obj_set_height(cui_list_item_label_right9, 50);
    lv_obj_set_width(cui_list_item_label_right9, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_list_item_label_right9, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_right9, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_right9, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_right9, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_right9, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_right9, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_extend9;
    cui_list_item_label_extend9 = lv_label_create(cui_list_item_label_right9);
    lv_obj_set_width(cui_list_item_label_extend9, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_extend9, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_extend9, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_extend9, "#ON");
    lv_obj_set_style_text_color(cui_list_item_label_extend9, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_extend9, 200, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_arrow9;
    cui_list_item_arrow9 = lv_img_create(cui_settinglistitem5);
    lv_img_set_src(cui_list_item_arrow9, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_list_item_arrow9, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_arrow9, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_arrow9, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_arrow9, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_arrow9, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_system_setting_warp2;
    cui_system_setting_warp2 = lv_obj_create(cui_system_setting_warp);
    lv_obj_set_width(cui_system_setting_warp2, 329);
    lv_obj_set_height(cui_system_setting_warp2, 108);
    lv_obj_set_x(cui_system_setting_warp2, -20);
    lv_obj_set_y(cui_system_setting_warp2, 94);
    lv_obj_set_align(cui_system_setting_warp2, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_system_setting_warp2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_system_setting_warp2, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_system_setting_warp2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_system_setting_warp2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_system_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_system_setting_warp2, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_system_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_system_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_system_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem11;
    cui_settinglistitem11 = lv_obj_create(cui_system_setting_warp2);
    lv_obj_set_width(cui_settinglistitem11, 313);
    lv_obj_set_height(cui_settinglistitem11, 95);
    lv_obj_set_align(cui_settinglistitem11, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem11, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem11, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem11, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem11, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem11, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem11, &ui_img_dark_ic_setting_btn_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem11, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem11, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem11, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem11, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon2;
    cui_list_item_icon2 = lv_img_create(cui_settinglistitem11);
    lv_img_set_src(cui_list_item_icon2, &ui_img_setting_ic_setting_bluetooth_png);
    lv_obj_set_width(cui_list_item_icon2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon2, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon2, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label2;
    cui_list_item_label2 = lv_obj_create(cui_settinglistitem11);
    lv_obj_set_width(cui_list_item_label2, 213);
    lv_obj_set_height(cui_list_item_label2, 50);
    lv_obj_set_align(cui_list_item_label2, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label2, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_left2;
    cui_list_item_label_left2 = lv_obj_create(cui_list_item_label2);
    lv_obj_set_height(cui_list_item_label_left2, 50);
    lv_obj_set_width(cui_list_item_label_left2, LV_SIZE_CONTENT);   /// 106
    lv_obj_set_x(cui_list_item_label_left2, -25);
    lv_obj_set_y(cui_list_item_label_left2, 4);
    lv_obj_set_align(cui_list_item_label_left2, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_left2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_left2, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_left2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_left2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_left2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label_left2, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label_left2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label_left2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label_left2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value2;
    cui_list_item_label_value2 = lv_label_create(cui_list_item_label_left2);
    lv_obj_set_width(cui_list_item_label_value2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value2, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value2, "#BLUETOOTH");
    lv_obj_set_style_text_color(cui_list_item_label_value2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_right2;
    cui_list_item_label_right2 = lv_obj_create(cui_list_item_label2);
    lv_obj_set_height(cui_list_item_label_right2, 50);
    lv_obj_set_width(cui_list_item_label_right2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_list_item_label_right2, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_right2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_right2, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_right2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_right2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_right2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_extend2;
    cui_list_item_label_extend2 = lv_label_create(cui_list_item_label_right2);
    lv_obj_set_width(cui_list_item_label_extend2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_extend2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_extend2, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_extend2, "#OFF");
    lv_obj_set_style_text_color(cui_list_item_label_extend2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_extend2, 200, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_arrow2;
    cui_list_item_arrow2 = lv_img_create(cui_settinglistitem11);
    lv_img_set_src(cui_list_item_arrow2, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_list_item_arrow2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_arrow2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_arrow2, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_arrow2, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_arrow2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_system_setting_warp3;
    cui_system_setting_warp3 = lv_obj_create(cui_system_setting_warp);
    lv_obj_set_width(cui_system_setting_warp3, 329);
    lv_obj_set_height(cui_system_setting_warp3, 108);
    lv_obj_set_x(cui_system_setting_warp3, -20);
    lv_obj_set_y(cui_system_setting_warp3, 94);
    lv_obj_set_align(cui_system_setting_warp3, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_system_setting_warp3, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_system_setting_warp3, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_system_setting_warp3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_system_setting_warp3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_system_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_system_setting_warp3, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_system_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_system_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_system_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem13;
    cui_settinglistitem13 = lv_obj_create(cui_system_setting_warp3);
    lv_obj_set_width(cui_settinglistitem13, 313);
    lv_obj_set_height(cui_settinglistitem13, 95);
    lv_obj_set_align(cui_settinglistitem13, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem13, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem13, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem13, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem13, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem13, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem13, &ui_img_dark_ic_setting_btn_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem13, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem13, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem13, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem13, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon6;
    cui_list_item_icon6 = lv_img_create(cui_settinglistitem13);
    lv_img_set_src(cui_list_item_icon6, &ui_img_setting_ic_setting_volume_png);
    lv_obj_set_width(cui_list_item_icon6, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon6, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon6, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon6, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon6, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label6;
    cui_list_item_label6 = lv_obj_create(cui_settinglistitem13);
    lv_obj_set_width(cui_list_item_label6, 231);
    lv_obj_set_height(cui_list_item_label6, 50);
    lv_obj_set_x(cui_list_item_label6, -27);
    lv_obj_set_y(cui_list_item_label6, 3);
    lv_obj_set_align(cui_list_item_label6, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label6, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label6, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label6, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label6, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label6, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_left6;
    cui_list_item_label_left6 = lv_obj_create(cui_list_item_label6);
    lv_obj_set_width(cui_list_item_label_left6, 210);
    lv_obj_set_height(cui_list_item_label_left6, 50);
    lv_obj_set_x(cui_list_item_label_left6, -25);
    lv_obj_set_y(cui_list_item_label_left6, 4);
    lv_obj_set_align(cui_list_item_label_left6, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_left6, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_left6, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_left6, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_left6, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_left6, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label_left6, 18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label_left6, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label_left6, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label_left6, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_SliderVolume2;
    cui_SliderVolume2 = lv_slider_create(cui_list_item_label_left6);
    lv_slider_set_range(cui_SliderVolume2, 0, 10);
    lv_slider_set_value(cui_SliderVolume2, 5, LV_ANIM_OFF);
    if(lv_slider_get_mode(cui_SliderVolume2) == LV_SLIDER_MODE_RANGE) lv_slider_set_left_value(cui_SliderVolume2, 0,
                                                                                                   LV_ANIM_OFF);
    lv_obj_set_width(cui_SliderVolume2, 180);
    lv_obj_set_height(cui_SliderVolume2, 12);
    lv_obj_set_x(cui_SliderVolume2, -65);
    lv_obj_set_y(cui_SliderVolume2, 145);
    lv_obj_set_align(cui_SliderVolume2, LV_ALIGN_CENTER);
    lv_obj_set_style_radius(cui_SliderVolume2, 35, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SliderVolume2, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SliderVolume2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(cui_SliderVolume2, lv_color_hex(0x25AAFC), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SliderVolume2, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(cui_SliderVolume2, lv_color_hex(0x0DE1F2), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(cui_SliderVolume2, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(cui_SliderVolume2, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(cui_SliderVolume2, LV_GRAD_DIR_HOR, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_set_style_radius(cui_SliderVolume2, 14, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SliderVolume2, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SliderVolume2, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(cui_SliderVolume2, lv_color_hex(0xFF005B), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(cui_SliderVolume2, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(cui_SliderVolume2, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(cui_SliderVolume2, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(cui_SliderVolume2, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(cui_SliderVolume2, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_SliderVolume2, 2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_SliderVolume2, 2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_SliderVolume2, 2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_SliderVolume2, 2, LV_PART_KNOB | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value6;
    cui_list_item_label_value6 = lv_label_create(cui_list_item_label_left6);
    lv_obj_set_width(cui_list_item_label_value6, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value6, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value6, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value6, "#VOLUME");
    lv_obj_add_flag(cui_list_item_label_value6, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_set_style_text_color(cui_list_item_label_value6, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value6, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_right6;
    cui_list_item_label_right6 = lv_obj_create(cui_list_item_label6);
    lv_obj_set_height(cui_list_item_label_right6, 50);
    lv_obj_set_width(cui_list_item_label_right6, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_list_item_label_right6, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_right6, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_right6, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_right6, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_right6, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_right6, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_extend6;
    cui_list_item_label_extend6 = lv_label_create(cui_list_item_label_right6);
    lv_obj_set_width(cui_list_item_label_extend6, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_extend6, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_extend6, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_extend6, "5");
    lv_obj_set_style_text_color(cui_list_item_label_extend6, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_extend6, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_arrow6;
    cui_list_item_arrow6 = lv_img_create(cui_settinglistitem13);
    lv_img_set_src(cui_list_item_arrow6, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_list_item_arrow6, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_arrow6, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_arrow6, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_arrow6, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_arrow6, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_system_setting_warp4;
    cui_system_setting_warp4 = lv_obj_create(cui_system_setting_warp);
    lv_obj_set_width(cui_system_setting_warp4, 329);
    lv_obj_set_height(cui_system_setting_warp4, 108);
    lv_obj_set_x(cui_system_setting_warp4, -20);
    lv_obj_set_y(cui_system_setting_warp4, 94);
    lv_obj_set_align(cui_system_setting_warp4, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_system_setting_warp4, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_system_setting_warp4, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_system_setting_warp4, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_system_setting_warp4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_system_setting_warp4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_system_setting_warp4, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_system_setting_warp4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_system_setting_warp4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_system_setting_warp4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem15;
    cui_settinglistitem15 = lv_obj_create(cui_system_setting_warp4);
    lv_obj_set_width(cui_settinglistitem15, 313);
    lv_obj_set_height(cui_settinglistitem15, 95);
    lv_obj_set_align(cui_settinglistitem15, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem15, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem15, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem15, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem15, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem15, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem15, &ui_img_dark_ic_setting_btn_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem15, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem15, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem15, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem15, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon7;
    cui_list_item_icon7 = lv_img_create(cui_settinglistitem15);
    lv_img_set_src(cui_list_item_icon7, &ui_img_setting_ic_setting_date_png);
    lv_obj_set_width(cui_list_item_icon7, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon7, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon7, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon7, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon7, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label7;
    cui_list_item_label7 = lv_obj_create(cui_settinglistitem15);
    lv_obj_set_width(cui_list_item_label7, 213);
    lv_obj_set_height(cui_list_item_label7, 50);
    lv_obj_set_align(cui_list_item_label7, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label7, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label7, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label7, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label7, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label7, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_left7;
    cui_list_item_label_left7 = lv_obj_create(cui_list_item_label7);
    lv_obj_set_height(cui_list_item_label_left7, 50);
    lv_obj_set_width(cui_list_item_label_left7, LV_SIZE_CONTENT);   /// 106
    lv_obj_set_x(cui_list_item_label_left7, -25);
    lv_obj_set_y(cui_list_item_label_left7, 4);
    lv_obj_set_align(cui_list_item_label_left7, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_left7, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_left7, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_left7, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_left7, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_left7, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label_left7, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label_left7, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label_left7, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label_left7, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value7;
    cui_list_item_label_value7 = lv_label_create(cui_list_item_label_left7);
    lv_obj_set_width(cui_list_item_label_value7, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value7, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value7, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value7, "#DATE");
    lv_obj_set_style_text_color(cui_list_item_label_value7, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value7, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_right7;
    cui_list_item_label_right7 = lv_obj_create(cui_list_item_label7);
    lv_obj_set_height(cui_list_item_label_right7, 50);
    lv_obj_set_width(cui_list_item_label_right7, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_list_item_label_right7, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_right7, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_right7, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_right7, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_right7, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_right7, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_extend7;
    cui_list_item_label_extend7 = lv_label_create(cui_list_item_label_right7);
    lv_obj_set_width(cui_list_item_label_extend7, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_extend7, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_extend7, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_extend7, "2025-5-22 12:35");
    lv_obj_set_style_text_color(cui_list_item_label_extend7, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_extend7, 200, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_arrow7;
    cui_list_item_arrow7 = lv_img_create(cui_settinglistitem15);
    lv_img_set_src(cui_list_item_arrow7, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_list_item_arrow7, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_arrow7, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_arrow7, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_arrow7, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_arrow7, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_vehicle_setting_warp;
    cui_vehicle_setting_warp = lv_obj_create(cui_setting_warp_right);
    lv_obj_set_width(cui_vehicle_setting_warp, lv_pct(100));
    lv_obj_set_height(cui_vehicle_setting_warp, lv_pct(100));
    lv_obj_set_align(cui_vehicle_setting_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_vehicle_setting_warp, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(cui_vehicle_setting_warp, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_add_flag(cui_vehicle_setting_warp, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_set_style_bg_color(cui_vehicle_setting_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_vehicle_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_vehicle_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_vehicle_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_vehicle_setting_warp, 30, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_vehicle_setting_warp, 30, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_vehicle_setting_warp1;
    cui_vehicle_setting_warp1 = lv_obj_create(cui_vehicle_setting_warp);
    lv_obj_set_width(cui_vehicle_setting_warp1, 329);
    lv_obj_set_height(cui_vehicle_setting_warp1, 108);
    lv_obj_set_x(cui_vehicle_setting_warp1, -20);
    lv_obj_set_y(cui_vehicle_setting_warp1, 94);
    lv_obj_set_align(cui_vehicle_setting_warp1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_vehicle_setting_warp1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_vehicle_setting_warp1, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_vehicle_setting_warp1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_vehicle_setting_warp1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_vehicle_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_vehicle_setting_warp1, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_vehicle_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_vehicle_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_vehicle_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem1;
    cui_settinglistitem1 = lv_obj_create(cui_vehicle_setting_warp1);
    lv_obj_set_width(cui_settinglistitem1, 313);
    lv_obj_set_height(cui_settinglistitem1, 95);
    lv_obj_set_align(cui_settinglistitem1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem1, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem1, &ui_img_dark_ic_setting_btn_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem1, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem1, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon8;
    cui_list_item_icon8 = lv_img_create(cui_settinglistitem1);
    lv_img_set_src(cui_list_item_icon8, &ui_img_setting_ic_setting_btkey_png);
    lv_obj_set_width(cui_list_item_icon8, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon8, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon8, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon8, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon8, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label8;
    cui_list_item_label8 = lv_obj_create(cui_settinglistitem1);
    lv_obj_set_width(cui_list_item_label8, 213);
    lv_obj_set_height(cui_list_item_label8, 50);
    lv_obj_set_align(cui_list_item_label8, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label8, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label8, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label8, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label8, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label8, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_left8;
    cui_list_item_label_left8 = lv_obj_create(cui_list_item_label8);
    lv_obj_set_height(cui_list_item_label_left8, 50);
    lv_obj_set_width(cui_list_item_label_left8, LV_SIZE_CONTENT);   /// 106
    lv_obj_set_x(cui_list_item_label_left8, -25);
    lv_obj_set_y(cui_list_item_label_left8, 4);
    lv_obj_set_align(cui_list_item_label_left8, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_left8, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_left8, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_left8, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_left8, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_left8, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label_left8, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label_left8, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label_left8, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label_left8, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value8;
    cui_list_item_label_value8 = lv_label_create(cui_list_item_label_left8);
    lv_obj_set_width(cui_list_item_label_value8, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value8, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value8, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value8, "#UNLOCK");
    lv_obj_set_style_text_color(cui_list_item_label_value8, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value8, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_right8;
    cui_list_item_label_right8 = lv_obj_create(cui_list_item_label8);
    lv_obj_set_height(cui_list_item_label_right8, 50);
    lv_obj_set_width(cui_list_item_label_right8, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_list_item_label_right8, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_right8, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_right8, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_right8, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_right8, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_right8, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_extend8;
    cui_list_item_label_extend8 = lv_label_create(cui_list_item_label_right8);
    lv_obj_set_width(cui_list_item_label_extend8, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_extend8, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_extend8, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_extend8, "#ON");
    lv_obj_set_style_text_color(cui_list_item_label_extend8, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_extend8, 200, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_arrow8;
    cui_list_item_arrow8 = lv_img_create(cui_settinglistitem1);
    lv_img_set_src(cui_list_item_arrow8, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_list_item_arrow8, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_arrow8, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_arrow8, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_arrow8, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_arrow8, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_vehicle_setting_warp2;
    cui_vehicle_setting_warp2 = lv_obj_create(cui_vehicle_setting_warp);
    lv_obj_set_width(cui_vehicle_setting_warp2, 329);
    lv_obj_set_height(cui_vehicle_setting_warp2, 108);
    lv_obj_set_x(cui_vehicle_setting_warp2, -20);
    lv_obj_set_y(cui_vehicle_setting_warp2, 94);
    lv_obj_set_align(cui_vehicle_setting_warp2, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_vehicle_setting_warp2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_vehicle_setting_warp2, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_vehicle_setting_warp2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_vehicle_setting_warp2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_vehicle_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_vehicle_setting_warp2, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_vehicle_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_vehicle_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_vehicle_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem3;
    cui_settinglistitem3 = lv_obj_create(cui_vehicle_setting_warp2);
    lv_obj_set_width(cui_settinglistitem3, 313);
    lv_obj_set_height(cui_settinglistitem3, 95);
    lv_obj_set_align(cui_settinglistitem3, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem3, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem3, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem3, &ui_img_dark_ic_setting_btn_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem3, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem3, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon1;
    cui_list_item_icon1 = lv_img_create(cui_settinglistitem3);
    lv_obj_set_width(cui_list_item_icon1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon1, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon1, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label1;
    cui_list_item_label1 = lv_obj_create(cui_settinglistitem3);
    lv_obj_set_width(cui_list_item_label1, 213);
    lv_obj_set_height(cui_list_item_label1, 50);
    lv_obj_set_align(cui_list_item_label1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label1, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_left1;
    cui_list_item_label_left1 = lv_obj_create(cui_list_item_label1);
    lv_obj_set_height(cui_list_item_label_left1, 50);
    lv_obj_set_width(cui_list_item_label_left1, LV_SIZE_CONTENT);   /// 106
    lv_obj_set_x(cui_list_item_label_left1, -25);
    lv_obj_set_y(cui_list_item_label_left1, 4);
    lv_obj_set_align(cui_list_item_label_left1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_left1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_left1, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_left1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_left1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_left1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label_left1, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label_left1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label_left1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label_left1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value1;
    cui_list_item_label_value1 = lv_label_create(cui_list_item_label_left1);
    lv_obj_set_width(cui_list_item_label_value1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value1, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value1, "#LOCATION");
    lv_obj_set_style_text_color(cui_list_item_label_value1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_right1;
    cui_list_item_label_right1 = lv_obj_create(cui_list_item_label1);
    lv_obj_set_height(cui_list_item_label_right1, 50);
    lv_obj_set_width(cui_list_item_label_right1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_list_item_label_right1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label_right1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label_right1, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label_right1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label_right1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label_right1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_extend1;
    cui_list_item_label_extend1 = lv_label_create(cui_list_item_label_right1);
    lv_obj_set_width(cui_list_item_label_extend1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_extend1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_extend1, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_extend1, "#OFF");
    lv_obj_set_style_text_color(cui_list_item_label_extend1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_extend1, 200, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_arrow1;
    cui_list_item_arrow1 = lv_img_create(cui_settinglistitem3);
    lv_img_set_src(cui_list_item_arrow1, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_list_item_arrow1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_arrow1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_arrow1, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_arrow1, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_arrow1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_vehicle_setting_warp3;
    cui_vehicle_setting_warp3 = lv_obj_create(cui_vehicle_setting_warp);
    lv_obj_set_width(cui_vehicle_setting_warp3, 658);
    lv_obj_set_height(cui_vehicle_setting_warp3, 80);
    lv_obj_set_x(cui_vehicle_setting_warp3, -20);
    lv_obj_set_y(cui_vehicle_setting_warp3, 94);
    lv_obj_set_align(cui_vehicle_setting_warp3, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_vehicle_setting_warp3, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_vehicle_setting_warp3, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_vehicle_setting_warp3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_vehicle_setting_warp3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_vehicle_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_vehicle_setting_warp3, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_vehicle_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_vehicle_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_vehicle_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem6;
    cui_settinglistitem6 = lv_obj_create(cui_vehicle_setting_warp3);
    lv_obj_set_width(cui_settinglistitem6, 643);
    lv_obj_set_height(cui_settinglistitem6, lv_pct(100));
    lv_obj_set_align(cui_settinglistitem6, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem6, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem6, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem6, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem6, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem6, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem6, &ui_img_dark_ic_setting_btn_xl_bg_png,
                                LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem6, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem6, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem6, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem6, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon3;
    cui_list_item_icon3 = lv_img_create(cui_settinglistitem6);
    lv_img_set_src(cui_list_item_icon3, &ui_img_setting_ic_setting_dashbord_png);
    lv_obj_set_width(cui_list_item_icon3, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon3, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon3, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label3;
    cui_list_item_label3 = lv_obj_create(cui_settinglistitem6);
    lv_obj_set_width(cui_list_item_label3, 242);
    lv_obj_set_height(cui_list_item_label3, 50);
    lv_obj_set_align(cui_list_item_label3, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label3, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label3, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label3, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value3;
    cui_list_item_label_value3 = lv_label_create(cui_list_item_label3);
    lv_obj_set_width(cui_list_item_label_value3, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value3, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value3, "#ODO");
    lv_obj_set_style_text_color(cui_list_item_label_value3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Panel4;
    cui_Panel4 = lv_obj_create(cui_settinglistitem6);
    lv_obj_set_width(cui_Panel4, 46);
    lv_obj_set_height(cui_Panel4, lv_pct(100));
    lv_obj_set_x(cui_Panel4, 74);
    lv_obj_set_y(cui_Panel4, 1);
    lv_obj_set_align(cui_Panel4, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Panel4, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Panel4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Panel4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_Panel4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_Panel4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_Panel4, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_Panel4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Spaceline2;
    cui_Spaceline2 = ui_Spaceline_create(cui_Panel4);
    lv_obj_set_width(cui_Spaceline2, 1);
    lv_obj_set_height(cui_Spaceline2, 32);
    lv_obj_set_x(cui_Spaceline2, 0);
    lv_obj_set_y(cui_Spaceline2, 0);

    lv_obj_set_style_bg_color(ui_comp_get_child(cui_Spaceline2, UI_COMP_SPACELINE_PANEL3), lv_color_hex(0x072B66),
                              LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_comp_get_child(cui_Spaceline2, UI_COMP_SPACELINE_PANEL3), 240,
                            LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(ui_comp_get_child(cui_Spaceline2, UI_COMP_SPACELINE_PANEL3), 0,
                                  LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(ui_comp_get_child(cui_Spaceline2, UI_COMP_SPACELINE_PANEL3), 220,
                                  LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_comp_get_child(cui_Spaceline2, UI_COMP_SPACELINE_PANEL2), lv_color_hex(0xFFFFFF),
                              LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_comp_get_child(cui_Spaceline2, UI_COMP_SPACELINE_PANEL2), 255,
                            LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_comp_get_child(cui_Spaceline2, UI_COMP_SPACELINE_PANEL2), lv_color_hex(0x014DAA),
                                   LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon14;
    cui_list_item_icon14 = lv_img_create(cui_settinglistitem6);
    lv_img_set_src(cui_list_item_icon14, &ui_img_setting_ic_setting_lan_png);
    lv_obj_set_width(cui_list_item_icon14, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon14, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon14, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon14, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon14, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label12;
    cui_list_item_label12 = lv_obj_create(cui_settinglistitem6);
    lv_obj_set_width(cui_list_item_label12, 254);
    lv_obj_set_height(cui_list_item_label12, 50);
    lv_obj_set_align(cui_list_item_label12, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label12, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label12, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label12, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label12, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label12, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value12;
    cui_list_item_label_value12 = lv_label_create(cui_list_item_label12);
    lv_obj_set_width(cui_list_item_label_value12, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value12, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value12, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value12, "#SUB_MILEAGE");
    lv_obj_set_style_text_color(cui_list_item_label_value12, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value12, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon12;
    cui_list_item_icon12 = lv_img_create(cui_settinglistitem6);
    lv_img_set_src(cui_list_item_icon12, &ui_img_setting_ic_setting_reset_png);
    lv_obj_set_width(cui_list_item_icon12, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon12, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon12, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon12, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon12, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_other_setting_warp;
    cui_other_setting_warp = lv_obj_create(cui_setting_warp_right);
    lv_obj_set_width(cui_other_setting_warp, lv_pct(100));
    lv_obj_set_height(cui_other_setting_warp, lv_pct(100));
    lv_obj_set_align(cui_other_setting_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_other_setting_warp, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(cui_other_setting_warp, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_add_flag(cui_other_setting_warp, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_set_style_bg_color(cui_other_setting_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_other_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_other_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_other_setting_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_other_setting_warp, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_other_setting_warp, 20, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_other_setting_warp1;
    cui_other_setting_warp1 = lv_obj_create(cui_other_setting_warp);
    lv_obj_set_width(cui_other_setting_warp1, 658);
    lv_obj_set_height(cui_other_setting_warp1, 82);
    lv_obj_set_x(cui_other_setting_warp1, -20);
    lv_obj_set_y(cui_other_setting_warp1, 94);
    lv_obj_set_align(cui_other_setting_warp1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_other_setting_warp1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_other_setting_warp1, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_other_setting_warp1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_other_setting_warp1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_other_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_other_setting_warp1, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_other_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_other_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_other_setting_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem4;
    cui_settinglistitem4 = lv_obj_create(cui_other_setting_warp1);
    lv_obj_set_width(cui_settinglistitem4, 643);
    lv_obj_set_height(cui_settinglistitem4, lv_pct(100));
    lv_obj_set_align(cui_settinglistitem4, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem4, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem4, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem4, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem4, &ui_img_dark_ic_setting_btn_xl_bg_png,
                                LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem4, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem4, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon17;
    cui_list_item_icon17 = lv_img_create(cui_settinglistitem4);
    lv_img_set_src(cui_list_item_icon17, &ui_img_setting_ic_setting_alarm_png);
    lv_obj_set_width(cui_list_item_icon17, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon17, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon17, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon17, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon17, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label15;
    cui_list_item_label15 = lv_obj_create(cui_settinglistitem4);
    lv_obj_set_width(cui_list_item_label15, 260);
    lv_obj_set_height(cui_list_item_label15, 50);
    lv_obj_set_align(cui_list_item_label15, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label15, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label15, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label15, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label15, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label15, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label15, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label15, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label15, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label15, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value15;
    cui_list_item_label_value15 = lv_label_create(cui_list_item_label15);
    lv_obj_set_width(cui_list_item_label_value15, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value15, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value15, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value15, "#SW");
    lv_obj_set_style_text_color(cui_list_item_label_value15, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value15, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label4;
    cui_list_item_label4 = lv_obj_create(cui_settinglistitem4);
    lv_obj_set_width(cui_list_item_label4, 306);
    lv_obj_set_height(cui_list_item_label4, 50);
    lv_obj_set_align(cui_list_item_label4, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label4, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label4, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label4, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value4;
    cui_list_item_label_value4 = lv_label_create(cui_list_item_label4);
    lv_obj_set_width(cui_list_item_label_value4, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value4, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value4, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value4, "#SW_INFO");
    lv_obj_set_style_text_color(cui_list_item_label_value4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value4, 200, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label_value4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label_value4, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label_value4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label_value4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_other_setting_warp2;
    cui_other_setting_warp2 = lv_obj_create(cui_other_setting_warp);
    lv_obj_set_width(cui_other_setting_warp2, 658);
    lv_obj_set_height(cui_other_setting_warp2, 82);
    lv_obj_set_x(cui_other_setting_warp2, -20);
    lv_obj_set_y(cui_other_setting_warp2, 94);
    lv_obj_set_align(cui_other_setting_warp2, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_other_setting_warp2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_other_setting_warp2, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_other_setting_warp2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_other_setting_warp2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_other_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_other_setting_warp2, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_other_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_other_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_other_setting_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem7;
    cui_settinglistitem7 = lv_obj_create(cui_other_setting_warp2);
    lv_obj_set_width(cui_settinglistitem7, 643);
    lv_obj_set_height(cui_settinglistitem7, lv_pct(100));
    lv_obj_set_align(cui_settinglistitem7, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem7, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem7, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem7, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem7, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem7, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem7, &ui_img_dark_ic_setting_btn_xl_bg_png,
                                LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem7, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem7, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem7, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem7, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon5;
    cui_list_item_icon5 = lv_img_create(cui_settinglistitem7);
    lv_img_set_src(cui_list_item_icon5, &ui_img_setting_ic_setting_keyboard_png);
    lv_obj_set_width(cui_list_item_icon5, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon5, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon5, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon5, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon5, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label11;
    cui_list_item_label11 = lv_obj_create(cui_settinglistitem7);
    lv_obj_set_width(cui_list_item_label11, 260);
    lv_obj_set_height(cui_list_item_label11, 50);
    lv_obj_set_align(cui_list_item_label11, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label11, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label11, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label11, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label11, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label11, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label11, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label11, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label11, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label11, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value11;
    cui_list_item_label_value11 = lv_label_create(cui_list_item_label11);
    lv_obj_set_width(cui_list_item_label_value11, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value11, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value11, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value11, "#HW");
    lv_obj_set_style_text_color(cui_list_item_label_value11, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value11, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label5;
    cui_list_item_label5 = lv_obj_create(cui_settinglistitem7);
    lv_obj_set_width(cui_list_item_label5, 306);
    lv_obj_set_height(cui_list_item_label5, 50);
    lv_obj_set_align(cui_list_item_label5, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label5, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label5, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label5, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label5, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label5, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value5;
    cui_list_item_label_value5 = lv_label_create(cui_list_item_label5);
    lv_obj_set_width(cui_list_item_label_value5, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value5, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value5, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value5, "#HW_INFO");
    lv_obj_set_style_text_color(cui_list_item_label_value5, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value5, 200, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label_value5, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label_value5, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label_value5, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label_value5, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_other_setting_warp3;
    cui_other_setting_warp3 = lv_obj_create(cui_other_setting_warp);
    lv_obj_set_width(cui_other_setting_warp3, 658);
    lv_obj_set_height(cui_other_setting_warp3, 82);
    lv_obj_set_x(cui_other_setting_warp3, -20);
    lv_obj_set_y(cui_other_setting_warp3, 94);
    lv_obj_set_align(cui_other_setting_warp3, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_other_setting_warp3, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_other_setting_warp3, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_other_setting_warp3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_other_setting_warp3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_other_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_other_setting_warp3, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_other_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_other_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_other_setting_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_settinglistitem8;
    cui_settinglistitem8 = lv_obj_create(cui_other_setting_warp3);
    lv_obj_set_width(cui_settinglistitem8, 643);
    lv_obj_set_height(cui_settinglistitem8, lv_pct(100));
    lv_obj_set_align(cui_settinglistitem8, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_settinglistitem8, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_settinglistitem8, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_settinglistitem8, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_settinglistitem8, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_settinglistitem8, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_settinglistitem8, &ui_img_dark_ic_setting_btn_xl_bg_png,
                                LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_settinglistitem8, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_settinglistitem8, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_settinglistitem8, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_settinglistitem8, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_icon10;
    cui_list_item_icon10 = lv_img_create(cui_settinglistitem8);
    lv_img_set_src(cui_list_item_icon10, &ui_img_setting_ic_setting_reset_png);
    lv_obj_set_width(cui_list_item_icon10, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_icon10, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_icon10, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_icon10, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_icon10, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_list_item_label13;
    cui_list_item_label13 = lv_obj_create(cui_settinglistitem8);
    lv_obj_set_width(cui_list_item_label13, 260);
    lv_obj_set_height(cui_list_item_label13, 50);
    lv_obj_set_align(cui_list_item_label13, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label13, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label13, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label13, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label13, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label13, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_list_item_label13, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_list_item_label13, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_list_item_label13, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_list_item_label13, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value13;
    cui_list_item_label_value13 = lv_label_create(cui_list_item_label13);
    lv_obj_set_width(cui_list_item_label_value13, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value13, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value13, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value13, "#RESET");
    lv_obj_set_style_text_color(cui_list_item_label_value13, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value13, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label14;
    cui_list_item_label14 = lv_obj_create(cui_settinglistitem8);
    lv_obj_set_width(cui_list_item_label14, 281);
    lv_obj_set_height(cui_list_item_label14, 50);
    lv_obj_set_align(cui_list_item_label14, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_list_item_label14, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_list_item_label14, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_list_item_label14, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_list_item_label14, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_list_item_label14, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_label_value14;
    cui_list_item_label_value14 = lv_label_create(cui_list_item_label14);
    lv_obj_set_width(cui_list_item_label_value14, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_label_value14, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_label_value14, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_label_value14, "#EXECUTE");
    lv_obj_set_style_text_color(cui_list_item_label_value14, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_label_value14, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_arrow4;
    cui_list_item_arrow4 = lv_img_create(cui_settinglistitem8);
    lv_img_set_src(cui_list_item_arrow4, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_list_item_arrow4, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_arrow4, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_arrow4, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_arrow4, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_arrow4, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SETTING_NUM);
    children[UI_COMP_SETTING_SETTING] = cui_Setting;
    children[UI_COMP_SETTING_SETTING_WARP_LEFT] = cui_setting_warp_left;
    children[UI_COMP_SETTING_SETTING_WARP_LEFT_SETTING_WARP_LEFT_LABEL] = cui_setting_warp_left_label;
    children[UI_COMP_SETTING_SETTING_WARP_LEFT_SETTING_WARP_LEFT_LABEL_SETTING_TITLE_LABEL] = cui_setting_title_label;
    children[UI_COMP_SETTING_SETTING_WARP_LEFT_SETTING_WARP_LEFT_MENUS] = cui_setting_warp_left_menus;
    children[UI_COMP_SETTING_SETTINGBTN1] = cui_Settingbtn1;
    children[UI_COMP_SETTING_SETTINGBTN1_SETTINGBTN1_SYSTEM_LABEL] = ui_comp_get_child(cui_Settingbtn1,
                                                                                       UI_COMP_SETTINGBTN_SETTING_BTN_LABEL);
    children[UI_COMP_SETTING_SETTINGBTN2] = cui_Settingbtn2;
    children[UI_COMP_SETTING_SETTINGBTN2_SETTINGBTN2_SYSTEM_LABEL2] = ui_comp_get_child(cui_Settingbtn2,
                                                                                        UI_COMP_SETTINGBTN_SETTING_BTN_LABEL);
    children[UI_COMP_SETTING_SETTINGBTN3] = cui_Settingbtn3;
    children[UI_COMP_SETTING_SETTINGBTN3_SETTINGBTN3_SYSTEM_LABEL3] = ui_comp_get_child(cui_Settingbtn3,
                                                                                        UI_COMP_SETTINGBTN_SETTING_BTN_LABEL);
    children[UI_COMP_SETTING_SPACELINE] = cui_Spaceline;
    children[UI_COMP_SETTING_SPACELINE_SPACELINE_PANEL3] = ui_comp_get_child(cui_Spaceline, UI_COMP_SPACELINE_PANEL3);
    children[UI_COMP_SETTING_SPACELINE_SPACELINE_PANEL2] = ui_comp_get_child(cui_Spaceline, UI_COMP_SPACELINE_PANEL2);
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT] = cui_setting_warp_right;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP] = cui_system_setting_warp;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1] = cui_system_setting_warp1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5] =
        cui_settinglistitem5;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_ICON9] =
        cui_list_item_icon9;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9]
        = cui_list_item_label9;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9_LIST_ITEM_LABEL_LEFT9]
        = cui_list_item_label_left9;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9_LIST_ITEM_LABEL_LEFT9_LIST_ITEM_LABEL_VALUE9]
        = cui_list_item_label_value9;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9_LIST_ITEM_LABEL_RIGHT9]
        = cui_list_item_label_right9;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_LABEL9_LIST_ITEM_LABEL_RIGHT9_LIST_ITEM_LABEL_EXTEND9]
        = cui_list_item_label_extend9;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP1_SETTINGLISTITEM5_LIST_ITEM_ARROW9]
        = cui_list_item_arrow9;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2] = cui_system_setting_warp2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11] =
        cui_settinglistitem11;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_ICON2]
        = cui_list_item_icon2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2]
        = cui_list_item_label2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2_LIST_ITEM_LABEL_LEFT2]
        = cui_list_item_label_left2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2_LIST_ITEM_LABEL_LEFT2_LIST_ITEM_LABEL_VALUE2]
        = cui_list_item_label_value2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2_LIST_ITEM_LABEL_RIGHT2]
        = cui_list_item_label_right2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_LABEL2_LIST_ITEM_LABEL_RIGHT2_LIST_ITEM_LABEL_EXTEND2]
        = cui_list_item_label_extend2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP2_SETTINGLISTITEM11_LIST_ITEM_ARROW2]
        = cui_list_item_arrow2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3] = cui_system_setting_warp3;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13] =
        cui_settinglistitem13;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_ICON6]
        = cui_list_item_icon6;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6]
        = cui_list_item_label6;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_LEFT6]
        = cui_list_item_label_left6;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_LEFT6_SLIDERVOLUME2]
        = cui_SliderVolume2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_LEFT6_LIST_ITEM_LABEL_VALUE6]
        = cui_list_item_label_value6;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_RIGHT6]
        = cui_list_item_label_right6;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_LABEL6_LIST_ITEM_LABEL_RIGHT6_LIST_ITEM_LABEL_EXTEND6]
        = cui_list_item_label_extend6;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP3_SETTINGLISTITEM13_LIST_ITEM_ARROW6]
        = cui_list_item_arrow6;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4] = cui_system_setting_warp4;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15] =
        cui_settinglistitem15;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_ICON7]
        = cui_list_item_icon7;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7]
        = cui_list_item_label7;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7_LIST_ITEM_LABEL_LEFT7]
        = cui_list_item_label_left7;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7_LIST_ITEM_LABEL_LEFT7_LIST_ITEM_LABEL_VALUE7]
        = cui_list_item_label_value7;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7_LIST_ITEM_LABEL_RIGHT7]
        = cui_list_item_label_right7;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_LABEL7_LIST_ITEM_LABEL_RIGHT7_LIST_ITEM_LABEL_EXTEND7]
        = cui_list_item_label_extend7;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_SYSTEM_SETTING_WARP_SYSTEM_SETTING_WARP4_SETTINGLISTITEM15_LIST_ITEM_ARROW7]
        = cui_list_item_arrow7;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP] = cui_vehicle_setting_warp;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1] = cui_vehicle_setting_warp1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1] =
        cui_settinglistitem1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_ICON8]
        = cui_list_item_icon8;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8]
        = cui_list_item_label8;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8_LIST_ITEM_LABEL_LEFT8]
        = cui_list_item_label_left8;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8_LIST_ITEM_LABEL_LEFT8_LIST_ITEM_LABEL_VALUE8]
        = cui_list_item_label_value8;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8_LIST_ITEM_LABEL_RIGHT8]
        = cui_list_item_label_right8;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_LABEL8_LIST_ITEM_LABEL_RIGHT8_LIST_ITEM_LABEL_EXTEND8]
        = cui_list_item_label_extend8;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP1_SETTINGLISTITEM1_LIST_ITEM_ARROW8]
        = cui_list_item_arrow8;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2] = cui_vehicle_setting_warp2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3] =
        cui_settinglistitem3;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_ICON1]
        = cui_list_item_icon1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1]
        = cui_list_item_label1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1_LIST_ITEM_LABEL_LEFT1]
        = cui_list_item_label_left1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1_LIST_ITEM_LABEL_LEFT1_LIST_ITEM_LABEL_VALUE1]
        = cui_list_item_label_value1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1_LIST_ITEM_LABEL_RIGHT1]
        = cui_list_item_label_right1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_LABEL1_LIST_ITEM_LABEL_RIGHT1_LIST_ITEM_LABEL_EXTEND1]
        = cui_list_item_label_extend1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP2_SETTINGLISTITEM3_LIST_ITEM_ARROW1]
        = cui_list_item_arrow1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3] = cui_vehicle_setting_warp3;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6] =
        cui_settinglistitem6;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_ICON3]
        = cui_list_item_icon3;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_LABEL3]
        = cui_list_item_label3;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_LABEL3_LIST_ITEM_LABEL_VALUE3]
        = cui_list_item_label_value3;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_PANEL4] =
        cui_Panel4;
    children[UI_COMP_SETTING_SPACELINE2] = cui_Spaceline2;
    children[UI_COMP_SETTING_SPACELINE2_SPACELINE2_PANEL3] = ui_comp_get_child(cui_Spaceline2, UI_COMP_SPACELINE_PANEL3);
    children[UI_COMP_SETTING_SPACELINE2_SPACELINE2_PANEL2] = ui_comp_get_child(cui_Spaceline2, UI_COMP_SPACELINE_PANEL2);
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_ICON14]
        = cui_list_item_icon14;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_LABEL12]
        = cui_list_item_label12;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_LABEL12_LIST_ITEM_LABEL_VALUE12]
        = cui_list_item_label_value12;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_VEHICLE_SETTING_WARP_VEHICLE_SETTING_WARP3_SETTINGLISTITEM6_LIST_ITEM_ICON12]
        = cui_list_item_icon12;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP] = cui_other_setting_warp;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1] = cui_other_setting_warp1;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4] =
        cui_settinglistitem4;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_ICON17] =
        cui_list_item_icon17;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_LABEL15] =
        cui_list_item_label15;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_LABEL15_LIST_ITEM_LABEL_VALUE15]
        = cui_list_item_label_value15;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_LABEL4] =
        cui_list_item_label4;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP1_SETTINGLISTITEM4_LIST_ITEM_LABEL4_LIST_ITEM_LABEL_VALUE4]
        = cui_list_item_label_value4;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2] = cui_other_setting_warp2;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7] =
        cui_settinglistitem7;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_ICON5] =
        cui_list_item_icon5;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_LABEL11] =
        cui_list_item_label11;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_LABEL11_LIST_ITEM_LABEL_VALUE11]
        = cui_list_item_label_value11;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_LABEL5] =
        cui_list_item_label5;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP2_SETTINGLISTITEM7_LIST_ITEM_LABEL5_LIST_ITEM_LABEL_VALUE5]
        = cui_list_item_label_value5;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3] = cui_other_setting_warp3;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8] =
        cui_settinglistitem8;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_ICON10] =
        cui_list_item_icon10;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_LABEL13] =
        cui_list_item_label13;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_LABEL13_LIST_ITEM_LABEL_VALUE13]
        = cui_list_item_label_value13;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_LABEL14] =
        cui_list_item_label14;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_LABEL14_LIST_ITEM_LABEL_VALUE14]
        = cui_list_item_label_value14;
    children[UI_COMP_SETTING_SETTING_WARP_RIGHT_OTHER_SETTING_WARP_OTHER_SETTING_WARP3_SETTINGLISTITEM8_LIST_ITEM_ARROW4] =
        cui_list_item_arrow4;
    lv_obj_add_event_cb(cui_Setting, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Setting, del_component_child_event_cb, LV_EVENT_DELETE, children);
    lv_obj_add_event_cb(cui_Settingbtn1, ui_event_comp_Setting_Settingbtn1, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_Settingbtn2, ui_event_comp_Setting_Settingbtn2, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_Settingbtn3, ui_event_comp_Setting_Settingbtn3, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_list_item_label9, ui_event_comp_Setting_list_item_label9, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_settinglistitem5, ui_event_comp_Setting_settinglistitem5, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_list_item_label2, ui_event_comp_Setting_list_item_label2, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_settinglistitem11, ui_event_comp_Setting_settinglistitem11, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_SliderVolume2, ui_event_comp_Setting_SliderVolume2, LV_EVENT_ALL, children);
    ui_comp_Setting_create_hook(cui_Setting);
    return cui_Setting;
}

