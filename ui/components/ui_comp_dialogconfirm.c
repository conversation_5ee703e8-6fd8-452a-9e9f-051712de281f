//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT DialogConfirm

lv_obj_t * ui_DialogConfirm_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_DialogConfirm;
    cui_DialogConfirm = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_DialogConfirm, 480);
    lv_obj_set_height(cui_DialogConfirm, 328);
    lv_obj_set_align(cui_DialogConfirm, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_DialogConfirm, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_DialogConfirm, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_DialogConfirm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_DialogConfirm, &ui_img_dialog_pic_bg_l_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_dialog_confirm_content;
    cui_dialog_confirm_content = lv_obj_create(cui_DialogConfirm);
    lv_obj_set_width(cui_dialog_confirm_content, lv_pct(100));
    lv_obj_set_height(cui_dialog_confirm_content, lv_pct(100));
    lv_obj_set_align(cui_dialog_confirm_content, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_dialog_confirm_content, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_dialog_confirm_content, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER,
                          LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_dialog_confirm_content, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_dialog_confirm_content, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_dialog_confirm_content, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_dialog_confirm_content, 40, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_dialog_confirm_content, 40, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_dialog_confirm_content, 40, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_dialog_confirm_content, 40, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_dialog_title;
    cui_dialog_title = lv_obj_create(cui_dialog_confirm_content);
    lv_obj_set_height(cui_dialog_title, 36);
    lv_obj_set_width(cui_dialog_title, lv_pct(90));
    lv_obj_set_align(cui_dialog_title, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_dialog_title, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_dialog_title, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_dialog_title, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_dialog_title, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_dialog_title, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_dialog_title, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_dialog_title, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_dialog_title, 12, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_dialog_title, 15, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_wifi_name_label;
    cui_wifi_name_label = lv_label_create(cui_dialog_title);
    lv_obj_set_width(cui_wifi_name_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_wifi_name_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_wifi_name_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_wifi_name_label, "Wifi Name");
    lv_obj_set_style_text_color(cui_wifi_name_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_wifi_name_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_wifi_name_label, &ui_font_AlibabaPuHui26, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_dialog_tips;
    cui_dialog_tips = lv_obj_create(cui_dialog_confirm_content);
    lv_obj_set_height(cui_dialog_tips, 36);
    lv_obj_set_width(cui_dialog_tips, lv_pct(90));
    lv_obj_set_align(cui_dialog_tips, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_dialog_tips, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_dialog_tips, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_dialog_tips, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_dialog_tips, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_dialog_tips, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_dialog_tips, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_dialog_tips, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_dialog_tips, 12, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_dialog_tips, 15, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_wifi_con_tips;
    cui_wifi_con_tips = lv_label_create(cui_dialog_tips);
    lv_obj_set_width(cui_wifi_con_tips, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_wifi_con_tips, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_wifi_con_tips, LV_ALIGN_CENTER);
    lv_label_set_text(cui_wifi_con_tips, "#PLEASE_ENTER");
    lv_obj_set_style_text_color(cui_wifi_con_tips, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_wifi_con_tips, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_TextArea;
    cui_TextArea = lv_textarea_create(cui_dialog_confirm_content);
    lv_obj_set_width(cui_TextArea, 385);
    lv_obj_set_height(cui_TextArea, LV_SIZE_CONTENT);    /// 70
    lv_obj_set_align(cui_TextArea, LV_ALIGN_CENTER);
    lv_textarea_set_max_length(cui_TextArea, 32);
    lv_textarea_set_placeholder_text(cui_TextArea, "#PLEASE");
    lv_textarea_set_one_line(cui_TextArea, true);
    lv_textarea_set_password_mode(cui_TextArea, true);
    lv_obj_set_style_text_color(cui_TextArea, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_TextArea, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(cui_TextArea, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_TextArea, &ui_font_AlibabaPuHui18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(cui_TextArea, 35, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_TextArea, lv_color_hex(0x181818), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_TextArea, 160, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(cui_TextArea, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(cui_TextArea, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(cui_TextArea, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(cui_TextArea, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(cui_TextArea, lv_color_hex(0x0A8FEF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(cui_TextArea, 125, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(cui_TextArea, 9, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(cui_TextArea, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(cui_TextArea, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(cui_TextArea, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_TextArea, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_TextArea, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_TextArea, 22, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_TextArea, 22, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_text_color(cui_TextArea, lv_color_hex(0xFFFFFF), LV_PART_TEXTAREA_PLACEHOLDER | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_TextArea, 200, LV_PART_TEXTAREA_PLACEHOLDER | LV_STATE_DEFAULT);

    lv_obj_t * cui_dialog_confirm_btn;
    cui_dialog_confirm_btn = lv_btn_create(cui_dialog_confirm_content);
    lv_obj_set_width(cui_dialog_confirm_btn, 384);
    lv_obj_set_height(cui_dialog_confirm_btn, 68);
    lv_obj_set_align(cui_dialog_confirm_btn, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_dialog_confirm_btn, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(cui_dialog_confirm_btn, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_dialog_confirm_btn, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_dialog_confirm_btn, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_dialog_confirm_btn, &ui_img_dialog_dialog_confirm_btn_png,
                                LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_opa(cui_dialog_confirm_btn, 255, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_recolor(cui_dialog_confirm_btn, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_recolor_opa(cui_dialog_confirm_btn, 15, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_recolor(cui_dialog_confirm_btn, lv_color_hex(0x313131), LV_PART_MAIN | LV_STATE_DISABLED);
    lv_obj_set_style_bg_img_recolor_opa(cui_dialog_confirm_btn, 15, LV_PART_MAIN | LV_STATE_DISABLED);

    lv_obj_t * cui_dialog_confirm_btn_label;
    cui_dialog_confirm_btn_label = lv_label_create(cui_dialog_confirm_btn);
    lv_obj_set_width(cui_dialog_confirm_btn_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_dialog_confirm_btn_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_dialog_confirm_btn_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_dialog_confirm_btn_label, "#CONFIRM");
    lv_obj_set_style_text_color(cui_dialog_confirm_btn_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_dialog_confirm_btn_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_dialog_confirm_btn_label, &ui_font_AlibabaPuHui18, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_dialog_close;
    cui_dialog_close = lv_img_create(cui_DialogConfirm);
    lv_img_set_src(cui_dialog_close, &ui_img_dialog_ic_close_png);
    lv_obj_set_width(cui_dialog_close, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_dialog_close, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_dialog_close, 193);
    lv_obj_set_y(cui_dialog_close, -123);
    lv_obj_set_align(cui_dialog_close, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_dialog_close, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_CHECKABLE |
                    LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_dialog_close, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_DIALOGCONFIRM_NUM);
    children[UI_COMP_DIALOGCONFIRM_DIALOGCONFIRM] = cui_DialogConfirm;
    children[UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT] = cui_dialog_confirm_content;
    children[UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_TITLE] = cui_dialog_title;
    children[UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_TITLE_WIFI_NAME_LABEL] = cui_wifi_name_label;
    children[UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_TIPS] = cui_dialog_tips;
    children[UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_TIPS_WIFI_CON_TIPS] = cui_wifi_con_tips;
    children[UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_TEXTAREA] = cui_TextArea;
    children[UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_CONFIRM_BTN] = cui_dialog_confirm_btn;
    children[UI_COMP_DIALOGCONFIRM_DIALOG_CONFIRM_CONTENT_DIALOG_CONFIRM_BTN_DIALOG_CONFIRM_BTN_LABEL] =
        cui_dialog_confirm_btn_label;
    children[UI_COMP_DIALOGCONFIRM_DIALOG_CLOSE] = cui_dialog_close;
    lv_obj_add_event_cb(cui_DialogConfirm, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_DialogConfirm, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_DialogConfirm_create_hook(cui_DialogConfirm);
    return cui_DialogConfirm;
}

