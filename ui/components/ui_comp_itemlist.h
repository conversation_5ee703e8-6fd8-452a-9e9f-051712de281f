//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_ITEMLIST_H
#define _UI_COMP_ITEMLIST_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT ItemList
#define UI_COMP_ITEMLIST_ITEMLIST 0
#define UI_COMP_ITEMLIST_PAIRED_LABEL 1
#define UI_COMP_ITEMLIST_ITEM_LIST_PAIRED 2
#define UI_COMP_ITEMLIST_ITEM_LIST_PAIRED_ITEM_WARP1 3
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8 4
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT 5
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT_BLUETOOTHITEM8_PANEL9 6
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT_BLUETOOTHITEM8_PANEL9_BLUETOOTHITEM8_IMAGE2 7
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT_BLUETOOTHITEM8_LABEL2 8
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT 9
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC 10
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_VALUE 11
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DEL 12
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DEL_BLUETOOTHITEM8_IMAGE7 13
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DIS 14
#define UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DIS_BLUETOOTHITEM8_IMAGE3 15
#define UI_COMP_ITEMLIST_ITEM_LIST_PAIRED_ITEM_WARP2 16
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6 17
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT 18
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT_BLUETOOTHITEM6_PANEL9 19
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT_BLUETOOTHITEM6_PANEL9_BLUETOOTHITEM6_IMAGE2 20
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT_BLUETOOTHITEM6_LABEL2 21
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT 22
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC 23
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_VALUE 24
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DEL 25
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DEL_BLUETOOTHITEM6_IMAGE7 26
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DIS 27
#define UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DIS_BLUETOOTHITEM6_IMAGE3 28
#define UI_COMP_ITEMLIST_UNPAIRED_LABEL 29
#define UI_COMP_ITEMLIST_ITEM_LIST_UNPAIRED 30
#define UI_COMP_ITEMLIST_ITEM_LIST_UNPAIRED_ITEM_WARP3 31
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2 32
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT 33
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT_BLUETOOTHITEM2_PANEL9 34
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT_BLUETOOTHITEM2_PANEL9_BLUETOOTHITEM2_IMAGE2 35
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT_BLUETOOTHITEM2_LABEL2 36
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT 37
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC 38
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_VALUE 39
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DEL 40
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DEL_BLUETOOTHITEM2_IMAGE7 41
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DIS 42
#define UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DIS_BLUETOOTHITEM2_IMAGE3 43
#define UI_COMP_ITEMLIST_ITEM_LIST_UNPAIRED_ITEM_WARP4 44
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1 45
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT 46
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT_BLUETOOTHITEM1_PANEL9 47
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT_BLUETOOTHITEM1_PANEL9_BLUETOOTHITEM1_IMAGE2 48
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT_BLUETOOTHITEM1_LABEL2 49
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT 50
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC 51
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_VALUE 52
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DEL 53
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DEL_BLUETOOTHITEM1_IMAGE7 54
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DIS 55
#define UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DIS_BLUETOOTHITEM1_IMAGE3 56
#define UI_COMP_ITEMLIST_BLANK 57
#define _UI_COMP_ITEMLIST_NUM 58
lv_obj_t * ui_ItemList_create(lv_obj_t * comp_parent);
void ui_event_comp_ItemList_BluetoothItem8(lv_event_t * e);
void ui_event_comp_ItemList_BluetoothItem2(lv_event_t * e);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
