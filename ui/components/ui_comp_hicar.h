//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_HICAR_H
#define _UI_COMP_HICAR_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT Hicar
#define UI_COMP_HICAR_HICAR 0
#define UI_COMP_HICAR_HICAR_WARP 1
#define UI_COMP_HICAR_CONNECTED_STATUS 2
#define UI_COMP_HICAR_CONNECTED_STATUS_STATUS_ICON 3
#define UI_COMP_HICAR_CONNECTED_STATUS_STATUS_LABEL 4
#define UI_COMP_HICAR_CONNECTED_STATUS_STATUS_LABEL_STATUS_LABEL_VAL 5
#define UI_COMP_HICAR_CONNECTED_STATUS_STATUS_SUB_LABEL 6
#define UI_COMP_HICAR_CONNECTED_STATUS_STATUS_SUB_LABEL_STATUS_SUB_LABEL_VAL 7
#define _UI_COMP_HICAR_NUM 8
lv_obj_t * ui_Hicar_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
