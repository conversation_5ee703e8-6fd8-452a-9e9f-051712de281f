//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_TOASTMSG_H
#define _UI_COMP_TOASTMSG_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT Toastmsg
#define UI_COMP_TOASTMSG_TOASTMSG 0
#define UI_COMP_TOASTMSG_TOAST_MSG_ICON 1
#define UI_COMP_TOASTMSG_TOAST_MSG_ICON_TOAST_MSG_ICON_ASSET 2
#define UI_COMP_TOASTMSG_TOAST_MSG_LABEL 3
#define UI_COMP_TOASTMSG_TOAST_MSG_LABEL_TOAST_MSG_LABEL_VALUE 4
#define UI_COMP_TOASTMSG_TOAST_MSG_SUB_LABEL 5
#define UI_COMP_TOASTMSG_TOAST_MSG_SUB_LABEL_TOAST_MSG_SUB_LABEL_VALUE 6
#define _UI_COMP_TOASTMSG_NUM 7
lv_obj_t * ui_Toastmsg_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
