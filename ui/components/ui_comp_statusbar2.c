////////////////////////////////////////////////////
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT Statusbar2

lv_obj_t * ui_Statusbar2_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Statusbar2;
    cui_Statusbar2 = lv_obj_create(comp_parent);
    lv_obj_set_height(cui_Statusbar2, 100);
    lv_obj_set_width(cui_Statusbar2, lv_pct(100));
    lv_obj_set_align(cui_Statusbar2, LV_ALIGN_TOP_MID);
    lv_obj_set_flex_flow(cui_Statusbar2, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_Statusbar2, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_SPACE_BETWEEN);
    lv_obj_clear_flag(cui_Statusbar2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Statusbar2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Statusbar2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_Statusbar2, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_Statusbar2, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_Statusbar2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_Statusbar2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_LEFT;
    cui_LEFT = lv_obj_create(cui_Statusbar2);
    lv_obj_set_height(cui_LEFT, lv_pct(100));
    lv_obj_set_width(cui_LEFT, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_LEFT, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_LEFT, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_LEFT, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_LEFT, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_LEFT, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_LEFT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_REAL;
    cui_REAL = lv_obj_create(cui_LEFT);
    lv_obj_set_height(cui_REAL, 48);
    lv_obj_set_width(cui_REAL, LV_SIZE_CONTENT);   /// 100
    lv_obj_set_flex_flow(cui_REAL, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_REAL, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_REAL, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_REAL, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_REAL, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_STA;
    cui_STA = lv_obj_create(cui_REAL);
    lv_obj_set_width(cui_STA, 160);
    lv_obj_set_height(cui_STA, 50);
    lv_obj_set_align(cui_STA, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_STA, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_STA, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_STA, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_STA, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_STA, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_STA, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_STA, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_STA, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_STA, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_time;
    cui_time = lv_label_create(cui_STA);
    lv_obj_set_width(cui_time, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_time, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_time, LV_ALIGN_CENTER);
    lv_label_set_text(cui_time, "12:35");
    lv_obj_set_style_text_color(cui_time, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_time, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_time, &lv_font_montserrat_22, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_temp;
    cui_temp = lv_label_create(cui_STA);
    lv_obj_set_width(cui_temp, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_temp, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_temp, LV_ALIGN_CENTER);
    lv_label_set_text(cui_temp, "25 °C");
    lv_obj_set_style_text_color(cui_temp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_temp, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_temp, &lv_font_montserrat_22, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_temp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_temp, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_temp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_temp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_MODE;
    cui_MODE = lv_obj_create(cui_REAL);
    lv_obj_set_width(cui_MODE, LV_SIZE_CONTENT);   /// 83
    lv_obj_set_height(cui_MODE, LV_SIZE_CONTENT);    /// 26
    lv_obj_set_align(cui_MODE, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_MODE, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(cui_MODE, 4, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_MODE, lv_color_hex(0x0A1736), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_MODE, 230, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(cui_MODE, lv_color_hex(0x0076FF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(cui_MODE, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(cui_MODE, 200, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(cui_MODE, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_MODE, 6, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_MODE, 6, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_MODE, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_MODE, 2, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_mode_label;
    cui_mode_label = lv_label_create(cui_MODE);
    lv_obj_set_width(cui_mode_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_mode_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_mode_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_mode_label, "NORMAL");
    lv_obj_set_style_text_color(cui_mode_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_mode_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_mode_label, &lv_font_montserrat_18, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_GEAR;
    cui_GEAR = lv_obj_create(cui_REAL);
    lv_obj_set_width(cui_GEAR, 68);
    lv_obj_set_height(cui_GEAR, 50);
    lv_obj_set_align(cui_GEAR, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_GEAR, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_GEAR, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_GEAR, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_gear_label;
    cui_gear_label = lv_label_create(cui_GEAR);
    lv_obj_set_width(cui_gear_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_gear_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_gear_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_gear_label, "D1");
    lv_obj_set_style_text_color(cui_gear_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_gear_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_gear_label, &lv_font_montserrat_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_DYNAMIC_L;
    cui_DYNAMIC_L = lv_obj_create(cui_REAL);
    lv_obj_set_height(cui_DYNAMIC_L, 48);
    lv_obj_set_width(cui_DYNAMIC_L, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_DYNAMIC_L, 587);
    lv_obj_set_y(cui_DYNAMIC_L, 12);
    lv_obj_set_align(cui_DYNAMIC_L, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_DYNAMIC_L, LV_FLEX_FLOW_ROW_REVERSE);
    lv_obj_set_flex_align(cui_DYNAMIC_L, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_DYNAMIC_L, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_DYNAMIC_L, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_DYNAMIC_L, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_DYNAMIC_L, 4, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_DYNAMIC_L, 4, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_DYNAMIC_L, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_DYNAMIC_L, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_TRIP_AND_POWER;
    cui_TRIP_AND_POWER = lv_obj_create(cui_LEFT);
    lv_obj_set_height(cui_TRIP_AND_POWER, 48);
    lv_obj_set_width(cui_TRIP_AND_POWER, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_TRIP_AND_POWER, 0);
    lv_obj_set_y(cui_TRIP_AND_POWER, 50);
    lv_obj_set_flex_flow(cui_TRIP_AND_POWER, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_TRIP_AND_POWER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_TRIP_AND_POWER, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_TRIP_AND_POWER, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_TRIP_AND_POWER, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_TRIP;
    cui_TRIP = lv_obj_create(cui_TRIP_AND_POWER);
    lv_obj_set_width(cui_TRIP, 160);
    lv_obj_set_height(cui_TRIP, 50);
    lv_obj_set_align(cui_TRIP, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_TRIP, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_TRIP, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_TRIP, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_TRIP, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_TRIP, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_TRIP, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_TRIP, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_TRIP, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_TRIP, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_trip_info;
    cui_trip_info = lv_label_create(cui_TRIP);
    lv_obj_set_width(cui_trip_info, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_trip_info, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_trip_info, LV_ALIGN_CENTER);
    lv_label_set_text(cui_trip_info, "2:58h/99Km");
    lv_obj_set_style_text_color(cui_trip_info, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_trip_info, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_trip_info, &lv_font_montserrat_22, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_POWER;
    cui_POWER = lv_obj_create(cui_TRIP_AND_POWER);
    lv_obj_set_height(cui_POWER, 48);
    lv_obj_set_width(cui_POWER, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_POWER, -10);
    lv_obj_set_y(cui_POWER, 52);
    lv_obj_set_flex_flow(cui_POWER, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_POWER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_POWER, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_POWER, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_POWER, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_power_icon;
    cui_power_icon = lv_img_create(cui_POWER);
    lv_img_set_src(cui_power_icon, &ui_img_dark_ic_sta_charging_png);
    lv_obj_set_width(cui_power_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_power_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_power_icon, -298);
    lv_obj_set_y(cui_power_icon, -230);
    lv_obj_set_align(cui_power_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_power_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_power_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_power_label;
    cui_power_label = lv_label_create(cui_POWER);
    lv_obj_set_width(cui_power_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_power_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_power_label, -191);
    lv_obj_set_y(cui_power_label, -229);
    lv_obj_set_align(cui_power_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_power_label, "3 Kwh/100Km");
    lv_obj_set_style_text_color(cui_power_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_power_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_power_label, &lv_font_montserrat_22, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_power_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_power_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_power_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_power_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_CENTER;
    cui_CENTER = lv_obj_create(cui_Statusbar2);
    lv_obj_set_height(cui_CENTER, lv_pct(100));
    lv_obj_set_width(cui_CENTER, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_CENTER, -21);
    lv_obj_set_y(cui_CENTER, 5);
    lv_obj_set_align(cui_CENTER, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_CENTER, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_CENTER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_CENTER, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_CENTER, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_CENTER, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_RIGHT;
    cui_RIGHT = lv_obj_create(cui_Statusbar2);
    lv_obj_set_height(cui_RIGHT, lv_pct(100));
    lv_obj_set_width(cui_RIGHT, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_RIGHT, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_RIGHT, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_RIGHT, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_END);
    lv_obj_clear_flag(cui_RIGHT, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_RIGHT, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_RIGHT, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_NORMAL;
    cui_NORMAL = lv_obj_create(cui_RIGHT);
    lv_obj_set_height(cui_NORMAL, 50);
    lv_obj_set_width(cui_NORMAL, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_NORMAL, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_NORMAL, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_NORMAL, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_NORMAL, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_NORMAL, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_NORMAL, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_DYNAMIC_R;
    cui_DYNAMIC_R = lv_obj_create(cui_NORMAL);
    lv_obj_set_height(cui_DYNAMIC_R, 48);
    lv_obj_set_width(cui_DYNAMIC_R, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_DYNAMIC_R, 587);
    lv_obj_set_y(cui_DYNAMIC_R, 12);
    lv_obj_set_align(cui_DYNAMIC_R, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_DYNAMIC_R, LV_FLEX_FLOW_ROW_REVERSE);
    lv_obj_set_flex_align(cui_DYNAMIC_R, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_DYNAMIC_R, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_DYNAMIC_R, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_DYNAMIC_R, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_DYNAMIC_R, 4, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_DYNAMIC_R, 4, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_DYNAMIC_R, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_DYNAMIC_R, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_POSITION;
    cui_POSITION = lv_obj_create(cui_NORMAL);
    lv_obj_set_height(cui_POSITION, 48);
    lv_obj_set_width(cui_POSITION, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_POSITION, 696);
    lv_obj_set_y(cui_POSITION, 81);
    lv_obj_set_align(cui_POSITION, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_POSITION, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_POSITION, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_POSITION, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_POSITION, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_POSITION, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_POSITION, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_POSITION, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_POSITION, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_POSITION, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_location_icon;
    cui_location_icon = lv_img_create(cui_POSITION);
    lv_img_set_src(cui_location_icon, &ui_img_dark_ic_sta_location_png);
    lv_obj_set_width(cui_location_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_location_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_location_icon, -298);
    lv_obj_set_y(cui_location_icon, -230);
    lv_obj_set_align(cui_location_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_location_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_location_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_location_label;
    cui_location_label = lv_label_create(cui_POSITION);
    lv_obj_set_width(cui_location_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_location_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_location_label, -191);
    lv_obj_set_y(cui_location_label, -229);
    lv_obj_set_align(cui_location_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_location_label, "9999km");
    lv_obj_set_style_text_color(cui_location_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_location_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_location_label, &lv_font_montserrat_22, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_location_label, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_location_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_location_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_location_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_BATTERY;
    cui_BATTERY = lv_obj_create(cui_NORMAL);
    lv_obj_set_height(cui_BATTERY, 48);
    lv_obj_set_width(cui_BATTERY, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_BATTERY, 521);
    lv_obj_set_y(cui_BATTERY, 100);
    lv_obj_set_align(cui_BATTERY, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_BATTERY, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_BATTERY, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_BATTERY, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_BATTERY, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_BATTERY, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_BATTERY, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_BATTERY, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_BATTERY, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_BATTERY, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_BATTERY, 5, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_BATTERY, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_BATTERY, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_BATTERY, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_bat_icon;
    cui_bat_icon = lv_img_create(cui_BATTERY);
    lv_img_set_src(cui_bat_icon, &ui_img_dark_ic_sta_battery_png);
    lv_obj_set_width(cui_bat_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_bat_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_bat_icon, -298);
    lv_obj_set_y(cui_bat_icon, -230);
    lv_obj_set_align(cui_bat_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_bat_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_bat_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_bat_label;
    cui_bat_label = lv_label_create(cui_BATTERY);
    lv_obj_set_width(cui_bat_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_bat_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_bat_label, 2);
    lv_obj_set_y(cui_bat_label, 0);
    lv_obj_set_align(cui_bat_label, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_bat_label, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_bat_label, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_label_set_text(cui_bat_label, "100%");
    lv_obj_set_style_text_color(cui_bat_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_bat_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_bat_label, &lv_font_montserrat_22, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_bat_label, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_bat_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_bat_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_bat_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_ODO;
    cui_ODO = lv_obj_create(cui_RIGHT);
    lv_obj_set_height(cui_ODO, 48);
    lv_obj_set_width(cui_ODO, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_ODO, 679);
    lv_obj_set_y(cui_ODO, 250);
    lv_obj_set_flex_flow(cui_ODO, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_ODO, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_ODO, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_ODO, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_ODO, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_ready;
    cui_ready = lv_label_create(cui_ODO);
    lv_obj_set_width(cui_ready, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_ready, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_ready, -189);
    lv_obj_set_y(cui_ready, 32);
    lv_obj_set_align(cui_ready, LV_ALIGN_CENTER);
    lv_label_set_text(cui_ready, "READY");
    lv_obj_set_style_text_color(cui_ready, lv_color_hex(0x09FA05), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_ready, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_ready, &lv_font_montserrat_26, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_ready, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_ready, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_ready, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_ready, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_icon;
    cui_icon = lv_label_create(cui_ODO);
    lv_obj_set_width(cui_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_icon, -189);
    lv_obj_set_y(cui_icon, 32);
    lv_obj_set_align(cui_icon, LV_ALIGN_CENTER);
    lv_label_set_text(cui_icon, "ODO");
    lv_obj_set_style_text_color(cui_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_icon, 127, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_icon, &lv_font_montserrat_26, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_icon, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_icon, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_label;
    cui_label = lv_label_create(cui_ODO);
    lv_obj_set_width(cui_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_label, "000000 Km");
    lv_obj_set_style_text_color(cui_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_label, &lv_font_montserrat_22, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_STATUSBAR2_NUM);
    children[UI_COMP_STATUSBAR2_STATUSBAR2] = cui_Statusbar2;
    children[UI_COMP_STATUSBAR2_LEFT] = cui_LEFT;
    children[UI_COMP_STATUSBAR2_LEFT_REAL] = cui_REAL;
    children[UI_COMP_STATUSBAR2_LEFT_REAL_STA] = cui_STA;
    children[UI_COMP_STATUSBAR2_LEFT_REAL_STA_TIME] = cui_time;
    children[UI_COMP_STATUSBAR2_LEFT_REAL_STA_TEMP] = cui_temp;
    children[UI_COMP_STATUSBAR2_LEFT_REAL_MODE] = cui_MODE;
    children[UI_COMP_STATUSBAR2_LEFT_REAL_MODE_MODE_LABEL] = cui_mode_label;
    children[UI_COMP_STATUSBAR2_LEFT_REAL_GEAR] = cui_GEAR;
    children[UI_COMP_STATUSBAR2_LEFT_REAL_GEAR_GEAR_LABEL] = cui_gear_label;
    children[UI_COMP_STATUSBAR2_LEFT_REAL_DYNAMIC_L] = cui_DYNAMIC_L;
    children[UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER] = cui_TRIP_AND_POWER;
    children[UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_TRIP] = cui_TRIP;
    children[UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_TRIP_TRIP_INFO] = cui_trip_info;
    children[UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_POWER] = cui_POWER;
    children[UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_POWER_POWER_ICON] = cui_power_icon;
    children[UI_COMP_STATUSBAR2_LEFT_TRIP_AND_POWER_POWER_POWER_LABEL] = cui_power_label;
    children[UI_COMP_STATUSBAR2_CENTER] = cui_CENTER;
    children[UI_COMP_STATUSBAR2_RIGHT] = cui_RIGHT;
    children[UI_COMP_STATUSBAR2_RIGHT_NORMAL] = cui_NORMAL;
    children[UI_COMP_STATUSBAR2_RIGHT_NORMAL_DYNAMIC_R] = cui_DYNAMIC_R;
    children[UI_COMP_STATUSBAR2_RIGHT_NORMAL_POSITION] = cui_POSITION;
    children[UI_COMP_STATUSBAR2_RIGHT_NORMAL_POSITION_LOCATION_ICON] = cui_location_icon;
    children[UI_COMP_STATUSBAR2_RIGHT_NORMAL_POSITION_LOCATION_LABEL] = cui_location_label;
    children[UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY] = cui_BATTERY;
    children[UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY_BAT_ICON] = cui_bat_icon;
    children[UI_COMP_STATUSBAR2_RIGHT_NORMAL_BATTERY_BAT_LABEL] = cui_bat_label;
    children[UI_COMP_STATUSBAR2_RIGHT_ODO] = cui_ODO;
    children[UI_COMP_STATUSBAR2_RIGHT_ODO_READY] = cui_ready;
    children[UI_COMP_STATUSBAR2_RIGHT_ODO_ICON] = cui_icon;
    children[UI_COMP_STATUSBAR2_RIGHT_ODO_LABEL] = cui_label;
    lv_obj_add_event_cb(cui_Statusbar2, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Statusbar2, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_Statusbar2_create_hook(cui_Statusbar2);
    return cui_Statusbar2;
}

