////////////////////////////////////////////////////
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_DIALOGVIEW2_H
#define _UI_COMP_DIALOGVIEW2_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT DialogView2
#define UI_COMP_DIALOGVIEW2_DIALOGVIEW2 0
#define UI_COMP_DIALOGVIEW2_BG_PIC 1
#define UI_COMP_DIALOGVIEW2_MAIN 2
#define UI_COMP_DIALOGVIEW2_MAIN_HEAD 3
#define UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT 4
#define UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT_ICON 5
#define UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT_ICON_IMAGE 6
#define UI_COMP_DIALOGVIEW2_MAIN_HEAD_LEFT_LABEL 7
#define UI_COMP_DIALOGVIEW2_MAIN_HEAD_RIGHT 8
#define UI_COMP_DIALOGVIEW2_MAIN_HEAD_RIGHT_SWITCH 9
#define UI_COMP_DIALOGVIEW2_MAIN_CONTENT 10
#define UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST 11
#define UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_PAIRED_LABE 12
#define UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_PAIRED_ITEMS 13
#define UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_UNPAIRED_LABEL 14
#define UI_COMP_DIALOGVIEW2_MAIN_CONTENT_LIST_UNPAIRED_ITEMS 15
#define _UI_COMP_DIALOGVIEW2_NUM 16
lv_obj_t * ui_DialogView2_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
