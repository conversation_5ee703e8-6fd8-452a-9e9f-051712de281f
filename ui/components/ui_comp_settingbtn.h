//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SETTINGBTN_H
#define _UI_COMP_SETTINGBTN_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT Settingbtn
#define UI_COMP_SETTINGBTN_SETTINGBTN 0
#define UI_COMP_SETTINGBTN_SETTING_BTN_LABEL 1
#define _UI_COMP_SETTINGBTN_NUM 2
lv_obj_t * ui_Settingbtn_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
