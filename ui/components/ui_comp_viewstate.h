//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_VIEWSTATE_H
#define _UI_COMP_VIEWSTATE_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT ViewState
#define UI_COMP_VIEWSTATE_VIEWSTATE 0
#define UI_COMP_VIEWSTATE_STATUS_ICON 1
#define UI_COMP_VIEWSTATE_STATUS_ICON_DIALOG_CONNECT_ICON_ASSET 2
#define UI_COMP_VIEWSTATE_STATUS_LABEL_WARP 3
#define UI_COMP_VIEWSTATE_STATUS_LABEL_WARP_STATUS_LABEL 4
#define UI_COMP_VIEWSTATE_STATUS_SUB_WARP 5
#define UI_COMP_VIEWSTATE_STATUS_SUB_WARP_STATUS_SUB_LABEL 6
#define _UI_COMP_VIEWSTATE_NUM 7
lv_obj_t * ui_ViewState_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
