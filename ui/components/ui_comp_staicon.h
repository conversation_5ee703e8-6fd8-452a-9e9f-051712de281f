////////////////////////////////////////////////////
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_STAICON_H
#define _UI_COMP_STAICON_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT StaIcon
#define UI_COMP_STAICON_STAICON 0
#define UI_COMP_STAICON_ICON 1
#define _UI_COMP_STAICON_NUM 2
lv_obj_t * ui_StaIcon_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
