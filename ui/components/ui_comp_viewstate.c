//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT ViewState

lv_obj_t * ui_ViewState_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_ViewState;
    cui_ViewState = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_ViewState, lv_pct(100));
    lv_obj_set_height(cui_ViewState, lv_pct(100));
    lv_obj_set_align(cui_ViewState, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_ViewState, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_ViewState, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_ViewState, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_ViewState, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_ViewState, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_status_icon;
    cui_status_icon = lv_obj_create(cui_ViewState);
    lv_obj_set_width(cui_status_icon, 75);
    lv_obj_set_height(cui_status_icon, 75);
    lv_obj_set_align(cui_status_icon, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_status_icon, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_status_icon, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_status_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_status_icon, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_status_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_dialog_connect_icon_asset;
    cui_dialog_connect_icon_asset = lv_img_create(cui_status_icon);
    lv_obj_set_width(cui_dialog_connect_icon_asset, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_dialog_connect_icon_asset, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_dialog_connect_icon_asset, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_dialog_connect_icon_asset, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_dialog_connect_icon_asset, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_status_label_warp;
    cui_status_label_warp = lv_obj_create(cui_ViewState);
    lv_obj_set_height(cui_status_label_warp, 36);
    lv_obj_set_width(cui_status_label_warp, lv_pct(100));
    lv_obj_set_align(cui_status_label_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_status_label_warp, LV_FLEX_FLOW_COLUMN_WRAP);
    lv_obj_set_flex_align(cui_status_label_warp, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_status_label_warp, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_status_label_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_status_label_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_status_label_warp, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_status_label_warp, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_status_label_warp, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_status_label_warp, 20, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_status_label_warp, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_status_label_warp, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_status_label_warp, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_status_label_warp, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_status_label;
    cui_status_label = lv_label_create(cui_status_label_warp);
    lv_obj_set_width(cui_status_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_status_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_status_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_status_label, "#BLUETOOTH_CONNECT_SUCCESS");
    lv_obj_set_style_text_color(cui_status_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_status_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(cui_status_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_status_sub_warp;
    cui_status_sub_warp = lv_obj_create(cui_ViewState);
    lv_obj_set_height(cui_status_sub_warp, 28);
    lv_obj_set_width(cui_status_sub_warp, lv_pct(100));
    lv_obj_set_align(cui_status_sub_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_status_sub_warp, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(cui_status_sub_warp, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_status_sub_warp, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_status_sub_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_status_sub_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_status_sub_warp, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_status_sub_warp, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_status_sub_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_status_sub_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_status_sub_warp, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_status_sub_warp, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_status_sub_warp, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_status_sub_warp, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_status_sub_label;
    cui_status_sub_label = lv_label_create(cui_status_sub_warp);
    lv_obj_set_width(cui_status_sub_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_status_sub_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_status_sub_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_status_sub_label, "#RETURN");
    lv_obj_set_style_text_color(cui_status_sub_label, lv_color_hex(0xC3C3C3), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_status_sub_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(cui_status_sub_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_status_sub_label, &lv_font_montserrat_12, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_VIEWSTATE_NUM);
    children[UI_COMP_VIEWSTATE_VIEWSTATE] = cui_ViewState;
    children[UI_COMP_VIEWSTATE_STATUS_ICON] = cui_status_icon;
    children[UI_COMP_VIEWSTATE_STATUS_ICON_DIALOG_CONNECT_ICON_ASSET] = cui_dialog_connect_icon_asset;
    children[UI_COMP_VIEWSTATE_STATUS_LABEL_WARP] = cui_status_label_warp;
    children[UI_COMP_VIEWSTATE_STATUS_LABEL_WARP_STATUS_LABEL] = cui_status_label;
    children[UI_COMP_VIEWSTATE_STATUS_SUB_WARP] = cui_status_sub_warp;
    children[UI_COMP_VIEWSTATE_STATUS_SUB_WARP_STATUS_SUB_LABEL] = cui_status_sub_label;
    lv_obj_add_event_cb(cui_ViewState, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_ViewState, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_ViewState_create_hook(cui_ViewState);
    return cui_ViewState;
}

