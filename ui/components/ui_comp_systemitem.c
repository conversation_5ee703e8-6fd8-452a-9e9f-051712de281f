//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT SystemItem

lv_obj_t * ui_SystemItem_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_SystemItem;
    cui_SystemItem = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_SystemItem, 313);
    lv_obj_set_height(cui_SystemItem, 95);
    lv_obj_set_align(cui_SystemItem, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_SystemItem, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_SystemItem, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_SystemItem, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_SystemItem, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SystemItem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_SystemItem, &ui_img_dark_ic_setting_btn_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_SystemItem, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_SystemItem, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_SystemItem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_SystemItem, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_icon1;
    cui_item_icon1 = lv_img_create(cui_SystemItem);
    lv_img_set_src(cui_item_icon1, &ui_img_setting_ic_setting_wifi_png);
    lv_obj_set_width(cui_item_icon1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_item_icon1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_item_icon1, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_item_icon1, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_item_icon1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_item_label1;
    cui_item_label1 = lv_obj_create(cui_SystemItem);
    lv_obj_set_width(cui_item_label1, 213);
    lv_obj_set_height(cui_item_label1, 50);
    lv_obj_set_align(cui_item_label1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_label1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_item_label1, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_label1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_label1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_label1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_label_left1;
    cui_item_label_left1 = lv_obj_create(cui_item_label1);
    lv_obj_set_height(cui_item_label_left1, 50);
    lv_obj_set_width(cui_item_label_left1, LV_SIZE_CONTENT);   /// 106
    lv_obj_set_x(cui_item_label_left1, -25);
    lv_obj_set_y(cui_item_label_left1, 4);
    lv_obj_set_align(cui_item_label_left1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_label_left1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_item_label_left1, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_label_left1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_label_left1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_label_left1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_item_label_left1, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_item_label_left1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_item_label_left1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_item_label_left1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_label_val1;
    cui_item_label_val1 = lv_label_create(cui_item_label_left1);
    lv_obj_set_width(cui_item_label_val1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_item_label_val1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_item_label_val1, LV_ALIGN_CENTER);
    lv_label_set_text(cui_item_label_val1, "#WIFI");
    lv_obj_set_style_text_color(cui_item_label_val1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_item_label_val1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_label_right1;
    cui_item_label_right1 = lv_obj_create(cui_item_label1);
    lv_obj_set_height(cui_item_label_right1, 50);
    lv_obj_set_width(cui_item_label_right1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_item_label_right1, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_label_right1, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_item_label_right1, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_label_right1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_label_right1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_label_right1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_sub1;
    cui_list_item_sub1 = lv_label_create(cui_item_label_right1);
    lv_obj_set_width(cui_list_item_sub1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_sub1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_sub1, LV_ALIGN_CENTER);
    lv_label_set_text(cui_list_item_sub1, "#ON");
    lv_obj_set_style_text_color(cui_list_item_sub1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_list_item_sub1, 200, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_list_item_arrow3;
    cui_list_item_arrow3 = lv_img_create(cui_SystemItem);
    lv_img_set_src(cui_list_item_arrow3, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_list_item_arrow3, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_list_item_arrow3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_list_item_arrow3, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_list_item_arrow3, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_list_item_arrow3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SYSTEMITEM_NUM);
    children[UI_COMP_SYSTEMITEM_SYSTEMITEM] = cui_SystemItem;
    children[UI_COMP_SYSTEMITEM_ITEM_ICON1] = cui_item_icon1;
    children[UI_COMP_SYSTEMITEM_ITEM_LABEL1] = cui_item_label1;
    children[UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_LEFT1] = cui_item_label_left1;
    children[UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_LEFT1_ITEM_LABEL_VAL1] = cui_item_label_val1;
    children[UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_RIGHT1] = cui_item_label_right1;
    children[UI_COMP_SYSTEMITEM_ITEM_LABEL1_ITEM_LABEL_RIGHT1_LIST_ITEM_SUB1] = cui_list_item_sub1;
    children[UI_COMP_SYSTEMITEM_LIST_ITEM_ARROW3] = cui_list_item_arrow3;
    lv_obj_add_event_cb(cui_SystemItem, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_SystemItem, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_SystemItem_create_hook(cui_SystemItem);
    return cui_SystemItem;
}

