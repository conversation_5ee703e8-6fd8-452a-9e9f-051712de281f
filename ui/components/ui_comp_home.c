//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT Home

lv_obj_t * ui_Home_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Home;
    cui_Home = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_Home, 876);
    lv_obj_set_height(cui_Home, 476);
    lv_obj_set_x(cui_Home, 48);
    lv_obj_set_y(cui_Home, 40);
    lv_obj_set_align(cui_Home, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_Home, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_Home, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Home, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Home, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Home, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_Home, &ui_img_bg_home_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_home_left;
    cui_home_left = lv_obj_create(cui_Home);
    lv_obj_set_width(cui_home_left, lv_pct(50));
    lv_obj_set_height(cui_home_left, lv_pct(100));
    lv_obj_set_align(cui_home_left, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_home_left, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_home_left, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_home_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_home_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_home_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_home_left, 28, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_home_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_home_left, 31, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_home_left, 25, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_run_light;
    cui_run_light = lv_obj_create(cui_home_left);
    lv_obj_set_width(cui_run_light, 300);
    lv_obj_set_height(cui_run_light, 44);
    lv_obj_set_x(cui_run_light, -157);
    lv_obj_set_y(cui_run_light, -145);
    lv_obj_set_align(cui_run_light, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_run_light, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_run_light, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_run_light, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_run_light, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_run_light, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_main_run_l;
    cui_sta_main_run_l = lv_obj_create(cui_run_light);
    lv_obj_set_width(cui_sta_main_run_l, 58);
    lv_obj_set_height(cui_sta_main_run_l, lv_pct(100));
    lv_obj_set_align(cui_sta_main_run_l, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_sta_main_run_l, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_sta_main_run_l, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_sta_main_run_l, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_sta_main_run_l, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sta_main_run_l, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_sta_main_run_l, 20, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_sta_main_run_l, 20, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_sta_main_run_l, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_sta_main_run_l, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_main_run1;
    cui_sta_main_run1 = lv_img_create(cui_sta_main_run_l);
    lv_img_set_src(cui_sta_main_run1, &ui_img_dark_ic_sta_signals_l_png);
    lv_obj_set_width(cui_sta_main_run1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sta_main_run1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_sta_main_run1, -186);
    lv_obj_set_y(cui_sta_main_run1, 74);
    lv_obj_set_align(cui_sta_main_run1, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_sta_main_run1, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_sta_main_run1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_sta_main_run_fd;
    cui_sta_main_run_fd = lv_obj_create(cui_run_light);
    lv_obj_set_width(cui_sta_main_run_fd, 68);
    lv_obj_set_height(cui_sta_main_run_fd, lv_pct(100));
    lv_obj_set_align(cui_sta_main_run_fd, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_sta_main_run_fd, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_sta_main_run_fd, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_sta_main_run_fd, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_sta_main_run_fd, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sta_main_run_fd, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_sta_main_run_fd, 20, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_sta_main_run_fd, 20, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_sta_main_run_fd, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_sta_main_run_fd, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_icon_r_8;
    cui_sta_icon_r_8 = lv_img_create(cui_sta_main_run_fd);
    lv_img_set_src(cui_sta_icon_r_8, &ui_img_dark_ic_sta_signals_fd_png);
    lv_obj_set_width(cui_sta_icon_r_8, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sta_icon_r_8, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_sta_icon_r_8, -64);
    lv_obj_set_y(cui_sta_icon_r_8, 102);
    lv_obj_set_align(cui_sta_icon_r_8, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_sta_icon_r_8, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_sta_icon_r_8, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_sta_main_run_f;
    cui_sta_main_run_f = lv_obj_create(cui_run_light);
    lv_obj_set_width(cui_sta_main_run_f, 32);
    lv_obj_set_height(cui_sta_main_run_f, lv_pct(100));
    lv_obj_set_align(cui_sta_main_run_f, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_sta_main_run_f, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_sta_main_run_f, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_sta_main_run_f, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_sta_main_run_f, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sta_main_run_f, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_icon_r_9;
    cui_sta_icon_r_9 = lv_img_create(cui_sta_main_run_f);
    lv_img_set_src(cui_sta_icon_r_9, &ui_img_dark_ic_sta_signals_f_png);
    lv_obj_set_width(cui_sta_icon_r_9, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sta_icon_r_9, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_sta_icon_r_9, -85);
    lv_obj_set_y(cui_sta_icon_r_9, 40);
    lv_obj_set_align(cui_sta_icon_r_9, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_sta_icon_r_9, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_sta_icon_r_9, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_sta_main_run_fe;
    cui_sta_main_run_fe = lv_obj_create(cui_run_light);
    lv_obj_set_width(cui_sta_main_run_fe, 91);
    lv_obj_set_height(cui_sta_main_run_fe, lv_pct(100));
    lv_obj_set_align(cui_sta_main_run_fe, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_sta_main_run_fe, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_sta_main_run_fe, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_sta_main_run_fe, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_sta_main_run_fe, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sta_main_run_fe, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_icon_r_10;
    cui_sta_icon_r_10 = lv_img_create(cui_sta_main_run_fe);
    lv_img_set_src(cui_sta_icon_r_10, &ui_img_dark_ic_sta_signals_fe_png);
    lv_obj_set_width(cui_sta_icon_r_10, 61);
    lv_obj_set_height(cui_sta_icon_r_10, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_sta_icon_r_10, 125);
    lv_obj_set_y(cui_sta_icon_r_10, 87);
    lv_obj_set_align(cui_sta_icon_r_10, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_sta_icon_r_10, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_sta_icon_r_10, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_sta_main_run_r;
    cui_sta_main_run_r = lv_obj_create(cui_run_light);
    lv_obj_set_width(cui_sta_main_run_r, 69);
    lv_obj_set_height(cui_sta_main_run_r, 44);
    lv_obj_set_align(cui_sta_main_run_r, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_sta_main_run_r, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_sta_main_run_r, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_sta_main_run_r, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_sta_main_run_r, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sta_main_run_r, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_icon_r_6;
    cui_sta_icon_r_6 = lv_img_create(cui_sta_main_run_r);
    lv_img_set_src(cui_sta_icon_r_6, &ui_img_dark_ic_sta_signals_r_png);
    lv_obj_set_width(cui_sta_icon_r_6, 44);
    lv_obj_set_height(cui_sta_icon_r_6, lv_pct(100));
    lv_obj_set_x(cui_sta_icon_r_6, 3);
    lv_obj_set_y(cui_sta_icon_r_6, 68);
    lv_obj_set_align(cui_sta_icon_r_6, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_sta_icon_r_6, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_sta_icon_r_6, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_run_mode;
    cui_run_mode = lv_obj_create(cui_home_left);
    lv_obj_set_width(cui_run_mode, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_run_mode, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_run_mode, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_run_mode, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_run_mode, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_run_mode, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_run_mode, 16, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_run_mode, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_run_mode, 32, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_run_mode, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_img_car_model;
    cui_img_car_model = lv_img_create(cui_run_mode);
    lv_img_set_src(cui_img_car_model, &ui_img_bg_car_model_png);
    lv_obj_set_width(cui_img_car_model, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_img_car_model, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_img_car_model, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_img_car_model, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_img_car_model, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_run_angle;
    cui_run_angle = lv_obj_create(cui_home_left);
    lv_obj_set_width(cui_run_angle, 248);
    lv_obj_set_height(cui_run_angle, 100);
    lv_obj_set_x(cui_run_angle, -156);
    lv_obj_set_y(cui_run_angle, 191);
    lv_obj_set_align(cui_run_angle, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_run_angle, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_run_angle, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_run_angle, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_run_angle, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_run_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_run_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_run_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_run_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_run_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_pitch_angle;
    cui_pitch_angle = lv_label_create(cui_run_angle);
    lv_obj_set_width(cui_pitch_angle, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_pitch_angle, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_pitch_angle, -196);
    lv_obj_set_y(cui_pitch_angle, 216);
    lv_obj_set_align(cui_pitch_angle, LV_ALIGN_CENTER);
    lv_label_set_text(cui_pitch_angle, "#PITCH_ANGLE");
    lv_obj_set_style_text_color(cui_pitch_angle, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_pitch_angle, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_pitch_angle, &ui_font_AlibabaPuHui24, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_pitch_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_pitch_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_pitch_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_pitch_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_roll_angle;
    cui_roll_angle = lv_label_create(cui_run_angle);
    lv_obj_set_width(cui_roll_angle, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_roll_angle, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_roll_angle, -192);
    lv_obj_set_y(cui_roll_angle, 186);
    lv_obj_set_align(cui_roll_angle, LV_ALIGN_CENTER);
    lv_label_set_text(cui_roll_angle, "#ROLL_ANGLE");
    lv_obj_set_style_text_color(cui_roll_angle, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_roll_angle, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_roll_angle, &ui_font_AlibabaPuHui24, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_roll_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_roll_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_roll_angle, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_roll_angle, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_ACCELERATION;
    cui_ACCELERATION = lv_label_create(cui_run_angle);
    lv_obj_set_width(cui_ACCELERATION, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_ACCELERATION, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_ACCELERATION, -198);
    lv_obj_set_y(cui_ACCELERATION, 159);
    lv_obj_set_align(cui_ACCELERATION, LV_ALIGN_CENTER);
    lv_label_set_text(cui_ACCELERATION, "#ACCELERATION");
    lv_obj_set_style_text_color(cui_ACCELERATION, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_ACCELERATION, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_ACCELERATION, &ui_font_AlibabaPuHui24, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_ACCELERATION, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_ACCELERATION, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_ACCELERATION, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_ACCELERATION, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_home_right;
    cui_home_right = lv_obj_create(cui_Home);
    lv_obj_set_width(cui_home_right, lv_pct(50));
    lv_obj_set_height(cui_home_right, lv_pct(100));
    lv_obj_set_align(cui_home_right, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_home_right, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_home_right, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_home_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_home_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_home_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_home_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_home_right, 50, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_home_right, 40, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_home_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_run_nos;
    cui_run_nos = lv_obj_create(cui_home_right);
    lv_obj_set_width(cui_run_nos, lv_pct(100));
    lv_obj_set_height(cui_run_nos, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_run_nos, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_run_nos, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_run_nos, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_run_nos, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_run_nos, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_run_nos, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_run_nos, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_run_nos, 12, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_run_nos, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_run_nos, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_nos_icon;
    cui_sta_nos_icon = lv_obj_create(cui_run_nos);
    lv_obj_set_width(cui_sta_nos_icon, 128);
    lv_obj_set_height(cui_sta_nos_icon, 51);
    lv_obj_set_align(cui_sta_nos_icon, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_sta_nos_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_sta_nos_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sta_nos_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_main_nos_icon;
    cui_sta_main_nos_icon = lv_label_create(cui_sta_nos_icon);
    lv_obj_set_width(cui_sta_main_nos_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sta_main_nos_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_sta_main_nos_icon, LV_ALIGN_CENTER);
    lv_label_set_text(cui_sta_main_nos_icon, "NOS");
    lv_obj_set_style_text_color(cui_sta_main_nos_icon, lv_color_hex(0xAA1C1C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_sta_main_nos_icon, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_sta_main_nos_icon, &lv_font_montserrat_20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(cui_sta_main_nos_icon, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_outline_color(cui_sta_main_nos_icon, lv_color_hex(0xAA1C1C), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_outline_opa(cui_sta_main_nos_icon, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_outline_width(cui_sta_main_nos_icon, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_outline_pad(cui_sta_main_nos_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_sta_main_nos_icon, 4, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_sta_main_nos_icon, 4, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_sta_main_nos_icon, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_sta_main_nos_icon, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_nos_speed_bg;
    cui_sta_nos_speed_bg = lv_obj_create(cui_run_nos);
    lv_obj_set_width(cui_sta_nos_speed_bg, 80);
    lv_obj_set_height(cui_sta_nos_speed_bg, 80);
    lv_obj_set_align(cui_sta_nos_speed_bg, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_sta_nos_speed_bg, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(cui_sta_nos_speed_bg, 50, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_sta_nos_speed_bg, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sta_nos_speed_bg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_sta_nos_speed_bg, &ui_img_dark_ic_sta_nos_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(cui_sta_nos_speed_bg, lv_color_hex(0xFF3E56), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(cui_sta_nos_speed_bg, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(cui_sta_nos_speed_bg, 1, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_nos_speed_value;
    cui_sta_nos_speed_value = lv_label_create(cui_sta_nos_speed_bg);
    lv_obj_set_width(cui_sta_nos_speed_value, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sta_nos_speed_value, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_sta_nos_speed_value, LV_ALIGN_CENTER);
    lv_label_set_text(cui_sta_nos_speed_value, "80");
    lv_obj_set_style_text_color(cui_sta_nos_speed_value, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_sta_nos_speed_value, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_sta_nos_speed_value, &lv_font_montserrat_36, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_run_speed;
    cui_run_speed = lv_obj_create(cui_home_right);
    lv_obj_set_height(cui_run_speed, 152);
    lv_obj_set_width(cui_run_speed, lv_pct(100));
    lv_obj_set_x(cui_run_speed, 234);
    lv_obj_set_y(cui_run_speed, 8);
    lv_obj_set_align(cui_run_speed, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_run_speed, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_run_speed, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_run_speed, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_run_speed, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_run_speed, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_run_speed, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_run_speed, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_run_speed, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_run_speed, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sta_run_speed_value;
    cui_sta_run_speed_value = lv_label_create(cui_run_speed);
    lv_obj_set_width(cui_sta_run_speed_value, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sta_run_speed_value, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_sta_run_speed_value, -210);
    lv_obj_set_y(cui_sta_run_speed_value, -97);
    lv_obj_set_align(cui_sta_run_speed_value, LV_ALIGN_CENTER);
    lv_label_set_text(cui_sta_run_speed_value, "150");
    lv_obj_set_style_text_color(cui_sta_run_speed_value, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_sta_run_speed_value, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_sta_run_speed_value, &ui_font_BrunoAceSCRegular150, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_run_unit;
    cui_run_unit = lv_obj_create(cui_home_right);
    lv_obj_set_width(cui_run_unit, 225);
    lv_obj_set_height(cui_run_unit, 56);
    lv_obj_set_x(cui_run_unit, 238);
    lv_obj_set_y(cui_run_unit, 101);
    lv_obj_set_align(cui_run_unit, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_run_unit, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_run_unit, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_run_unit, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_run_unit, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_run_unit, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_speed_nav_title_value;
    cui_speed_nav_title_value = lv_label_create(cui_run_unit);
    lv_obj_set_height(cui_speed_nav_title_value, 35);
    lv_obj_set_width(cui_speed_nav_title_value, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_x(cui_speed_nav_title_value, 235);
    lv_obj_set_y(cui_speed_nav_title_value, 95);
    lv_obj_set_align(cui_speed_nav_title_value, LV_ALIGN_CENTER);
    lv_label_set_text(cui_speed_nav_title_value, "Km/h");
    lv_obj_set_style_text_color(cui_speed_nav_title_value, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_speed_nav_title_value, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_speed_nav_title_value, &lv_font_montserrat_22, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_speed_nav_line;
    cui_speed_nav_line = lv_img_create(cui_run_unit);
    lv_img_set_src(cui_speed_nav_line, &ui_img_dark_ic_line_light_png);
    lv_obj_set_width(cui_speed_nav_line, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_speed_nav_line, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_speed_nav_line, 240);
    lv_obj_set_y(cui_speed_nav_line, 118);
    lv_obj_set_align(cui_speed_nav_line, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_speed_nav_line, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_speed_nav_line, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_battery_val;
    cui_battery_val = lv_bar_create(cui_home_right);
    lv_bar_set_value(cui_battery_val, 67, LV_ANIM_OFF);
    lv_bar_set_start_value(cui_battery_val, 0, LV_ANIM_OFF);
    lv_obj_set_width(cui_battery_val, 242);
    lv_obj_set_height(cui_battery_val, 63);
    lv_obj_set_x(cui_battery_val, 234);
    lv_obj_set_y(cui_battery_val, 161);
    lv_obj_set_align(cui_battery_val, LV_ALIGN_CENTER);
    lv_obj_set_style_radius(cui_battery_val, 1, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_battery_val, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_battery_val, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_battery_val, &ui_img_dark_sta_battery_null_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(cui_battery_val, lv_color_hex(0xFFFFFF), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_battery_val, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_battery_val, &ui_img_dark_sta_battery_png, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_t * cui_battery_info;
    cui_battery_info = lv_obj_create(cui_home_right);
    lv_obj_set_width(cui_battery_info, 152);
    lv_obj_set_height(cui_battery_info, 56);
    lv_obj_set_x(cui_battery_info, 231);
    lv_obj_set_y(cui_battery_info, 217);
    lv_obj_set_align(cui_battery_info, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_battery_info, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_battery_info, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_battery_info, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_battery_info, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_battery_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_battery_info, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_battery_info, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_battery_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_battery_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_battery_icon;
    cui_battery_icon = lv_img_create(cui_battery_info);
    lv_img_set_src(cui_battery_icon, &ui_img_dark_ic_sta_battery_png);
    lv_obj_set_width(cui_battery_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_battery_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_battery_icon, -298);
    lv_obj_set_y(cui_battery_icon, -230);
    lv_obj_set_align(cui_battery_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_battery_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_battery_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_battery_label;
    cui_battery_label = lv_label_create(cui_battery_info);
    lv_obj_set_width(cui_battery_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_battery_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(cui_battery_label, -191);
    lv_obj_set_y(cui_battery_label, -229);
    lv_obj_set_align(cui_battery_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_battery_label, "100%");
    lv_obj_set_style_text_color(cui_battery_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_battery_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_battery_label, &lv_font_montserrat_28, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_battery_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_battery_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_battery_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_battery_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_HOME_NUM);
    children[UI_COMP_HOME_HOME] = cui_Home;
    children[UI_COMP_HOME_HOME_LEFT] = cui_home_left;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT] = cui_run_light;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_L] = cui_sta_main_run_l;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_L_STA_MAIN_RUN1] = cui_sta_main_run1;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FD] = cui_sta_main_run_fd;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FD_STA_ICON_R_8] = cui_sta_icon_r_8;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_F] = cui_sta_main_run_f;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_F_STA_ICON_R_9] = cui_sta_icon_r_9;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FE] = cui_sta_main_run_fe;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_FE_STA_ICON_R_10] = cui_sta_icon_r_10;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_R] = cui_sta_main_run_r;
    children[UI_COMP_HOME_HOME_LEFT_RUN_LIGHT_STA_MAIN_RUN_R_STA_ICON_R_6] = cui_sta_icon_r_6;
    children[UI_COMP_HOME_HOME_LEFT_RUN_MODE] = cui_run_mode;
    children[UI_COMP_HOME_HOME_LEFT_RUN_MODE_IMG_CAR_MODEL] = cui_img_car_model;
    children[UI_COMP_HOME_HOME_LEFT_RUN_ANGLE] = cui_run_angle;
    children[UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_PITCH_ANGLE] = cui_pitch_angle;
    children[UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_ROLL_ANGLE] = cui_roll_angle;
    children[UI_COMP_HOME_HOME_LEFT_RUN_ANGLE_ACCELERATION] = cui_ACCELERATION;
    children[UI_COMP_HOME_HOME_RIGHT] = cui_home_right;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_NOS] = cui_run_nos;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_ICON] = cui_sta_nos_icon;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_ICON_STA_MAIN_NOS_ICON] = cui_sta_main_nos_icon;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_SPEED_BG] = cui_sta_nos_speed_bg;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_NOS_STA_NOS_SPEED_BG_STA_NOS_SPEED_VALUE] = cui_sta_nos_speed_value;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_SPEED] = cui_run_speed;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_SPEED_STA_RUN_SPEED_VALUE] = cui_sta_run_speed_value;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_UNIT] = cui_run_unit;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_UNIT_SPEED_NAV_TITLE_VALUE] = cui_speed_nav_title_value;
    children[UI_COMP_HOME_HOME_RIGHT_RUN_UNIT_SPEED_NAV_LINE] = cui_speed_nav_line;
    children[UI_COMP_HOME_HOME_RIGHT_BATTERY_VAL] = cui_battery_val;
    children[UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO] = cui_battery_info;
    children[UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO_BATTERY_ICON] = cui_battery_icon;
    children[UI_COMP_HOME_HOME_RIGHT_BATTERY_INFO_BATTERY_LABEL] = cui_battery_label;
    lv_obj_add_event_cb(cui_Home, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Home, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_Home_create_hook(cui_Home);
    return cui_Home;
}

