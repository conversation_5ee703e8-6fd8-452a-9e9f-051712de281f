//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _PRJ_EBIKE_X1_UI_COMP_HOOK_H
#define _PRJ_EBIKE_X1_UI_COMP_HOOK_H

#ifdef __cplusplus
extern "C" {
#endif

void ui_comp_DeviceItem_create_hook(lv_obj_t * comp);
void ui_comp_DeviceItemDark_create_hook(lv_obj_t * comp);
void ui_comp_DialogConfirm_create_hook(lv_obj_t * comp);
void ui_comp_DialogView_create_hook(lv_obj_t * comp);
void ui_comp_DialogView2_create_hook(lv_obj_t * comp);
void ui_comp_Hicar_create_hook(lv_obj_t * comp);
void ui_comp_Home_create_hook(lv_obj_t * comp);
void ui_comp_InfoLarge_create_hook(lv_obj_t * comp);
void ui_comp_ItemList_create_hook(lv_obj_t * comp);
void ui_comp_ListEmpty_create_hook(lv_obj_t * comp);
void ui_comp_Listitem_create_hook(lv_obj_t * comp);
void ui_comp_Setting_create_hook(lv_obj_t * comp);
void ui_comp_Settingbtn_create_hook(lv_obj_t * comp);
void ui_comp_SettingInfo_create_hook(lv_obj_t * comp);
void ui_comp_SettingItem_create_hook(lv_obj_t * comp);
void ui_comp_SettingSidebar_create_hook(lv_obj_t * comp);
void ui_comp_SettingVolume_create_hook(lv_obj_t * comp);
void ui_comp_Sidebar_create_hook(lv_obj_t * comp);
void ui_comp_SidebarBtn_create_hook(lv_obj_t * comp);
void ui_comp_SliderVolume_create_hook(lv_obj_t * comp);
void ui_comp_Spaceline_create_hook(lv_obj_t * comp);
void ui_comp_Speedometer_create_hook(lv_obj_t * comp);
void ui_comp_StaIcon_create_hook(lv_obj_t * comp);
void ui_comp_Statusbar2_create_hook(lv_obj_t * comp);
void ui_comp_SwitchBtn_create_hook(lv_obj_t * comp);
void ui_comp_SystemItem_create_hook(lv_obj_t * comp);
void ui_comp_Toastmsg_create_hook(lv_obj_t * comp);
void ui_comp_ViewState_create_hook(lv_obj_t * comp);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
