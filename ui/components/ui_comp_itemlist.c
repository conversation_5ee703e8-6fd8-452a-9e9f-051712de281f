//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

void ui_event_comp_ItemList_BluetoothItem8(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_ItemList = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_dialog_msg, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
        _ui_flag_modify(ui_setting, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_ItemList_BluetoothItem2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_ItemList = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_dialog_confirm, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

// COMPONENT ItemList

lv_obj_t * ui_ItemList_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_ItemList;
    cui_ItemList = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_ItemList, lv_pct(100));
    lv_obj_set_height(cui_ItemList, lv_pct(100));
    lv_obj_set_align(cui_ItemList, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_ItemList, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_ItemList, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_set_style_bg_color(cui_ItemList, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_ItemList, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_paired_label;
    cui_paired_label = lv_label_create(cui_ItemList);
    lv_obj_set_width(cui_paired_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_paired_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_paired_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_paired_label, "#UNPAIRED");
    lv_obj_set_style_text_color(cui_paired_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_paired_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_paired_label, &ui_font_AlibabaPuHui18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_paired_label, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_paired_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_paired_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_paired_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_list_paired;
    cui_item_list_paired = lv_obj_create(cui_ItemList);
    lv_obj_set_width(cui_item_list_paired, lv_pct(100));
    lv_obj_set_height(cui_item_list_paired, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_item_list_paired, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_list_paired, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_item_list_paired, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_item_list_paired, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_list_paired, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_list_paired, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_warp1;
    cui_item_warp1 = lv_obj_create(cui_item_list_paired);
    lv_obj_set_height(cui_item_warp1, 80);
    lv_obj_set_width(cui_item_warp1, lv_pct(100));
    lv_obj_set_align(cui_item_warp1, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_warp1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_warp1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_warp1, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_BluetoothItem8;
    cui_BluetoothItem8 = ui_Listitem_create(cui_item_warp1);
    lv_obj_set_x(cui_BluetoothItem8, 0);
    lv_obj_set_y(cui_BluetoothItem8, 0);
    lv_obj_add_state(cui_BluetoothItem8, LV_STATE_CHECKED);       /// States

    lv_obj_t * cui_item_warp2;
    cui_item_warp2 = lv_obj_create(cui_item_list_paired);
    lv_obj_set_height(cui_item_warp2, 80);
    lv_obj_set_width(cui_item_warp2, lv_pct(100));
    lv_obj_set_align(cui_item_warp2, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_warp2, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_warp2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_warp2, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_BluetoothItem6;
    cui_BluetoothItem6 = ui_Listitem_create(cui_item_warp2);
    lv_obj_set_x(cui_BluetoothItem6, 0);
    lv_obj_set_y(cui_BluetoothItem6, 0);
    lv_obj_add_state(cui_BluetoothItem6, LV_STATE_CHECKED);       /// States

    lv_obj_t * cui_unpaired_label;
    cui_unpaired_label = lv_label_create(cui_ItemList);
    lv_obj_set_width(cui_unpaired_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_unpaired_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_unpaired_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_unpaired_label, "#UNPAIRED");
    lv_obj_set_style_text_color(cui_unpaired_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_unpaired_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_unpaired_label, &ui_font_AlibabaPuHui18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_unpaired_label, 5, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_unpaired_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_unpaired_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_unpaired_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_list_unpaired;
    cui_item_list_unpaired = lv_obj_create(cui_ItemList);
    lv_obj_set_width(cui_item_list_unpaired, lv_pct(100));
    lv_obj_set_height(cui_item_list_unpaired, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_item_list_unpaired, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_list_unpaired, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_item_list_unpaired, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(cui_item_list_unpaired, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_list_unpaired, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_list_unpaired, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_warp3;
    cui_item_warp3 = lv_obj_create(cui_item_list_unpaired);
    lv_obj_set_height(cui_item_warp3, 80);
    lv_obj_set_width(cui_item_warp3, lv_pct(100));
    lv_obj_set_align(cui_item_warp3, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_warp3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_warp3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_warp3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_BluetoothItem2;
    cui_BluetoothItem2 = ui_Listitem_create(cui_item_warp3);
    lv_obj_set_x(cui_BluetoothItem2, 0);
    lv_obj_set_y(cui_BluetoothItem2, 0);
    lv_obj_clear_state(cui_BluetoothItem2, LV_STATE_CHECKED);     /// States

    lv_obj_add_flag(ui_comp_get_child(cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC),
                    LV_OBJ_FLAG_HIDDEN);    /// Flags

    lv_obj_t * cui_item_warp4;
    cui_item_warp4 = lv_obj_create(cui_item_list_unpaired);
    lv_obj_set_height(cui_item_warp4, 80);
    lv_obj_set_width(cui_item_warp4, lv_pct(100));
    lv_obj_set_align(cui_item_warp4, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_warp4, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_warp4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_warp4, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_BluetoothItem1;
    cui_BluetoothItem1 = ui_Listitem_create(cui_item_warp4);
    lv_obj_set_x(cui_BluetoothItem1, 0);
    lv_obj_set_y(cui_BluetoothItem1, 0);
    lv_obj_clear_state(cui_BluetoothItem1, LV_STATE_CHECKED);     /// States

    lv_obj_add_flag(ui_comp_get_child(cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC),
                    LV_OBJ_FLAG_HIDDEN);    /// Flags

    lv_obj_t * cui_Blank;
    cui_Blank = lv_obj_create(cui_ItemList);
    lv_obj_set_height(cui_Blank, 30);
    lv_obj_set_width(cui_Blank, lv_pct(100));
    lv_obj_set_align(cui_Blank, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Blank, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Blank, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Blank, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(cui_Blank, lv_color_hex(0xFFFFFF), LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Blank, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_ITEMLIST_NUM);
    children[UI_COMP_ITEMLIST_ITEMLIST] = cui_ItemList;
    children[UI_COMP_ITEMLIST_PAIRED_LABEL] = cui_paired_label;
    children[UI_COMP_ITEMLIST_ITEM_LIST_PAIRED] = cui_item_list_paired;
    children[UI_COMP_ITEMLIST_ITEM_LIST_PAIRED_ITEM_WARP1] = cui_item_warp1;
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8] = cui_BluetoothItem8;
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT] = ui_comp_get_child(cui_BluetoothItem8,
                                                                                                UI_COMP_LISTITEM_LIST_ITEM_LEFT);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT_BLUETOOTHITEM8_PANEL9] = ui_comp_get_child(
                                                                                                        cui_BluetoothItem8, UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT_BLUETOOTHITEM8_PANEL9_BLUETOOTHITEM8_IMAGE2] =
        ui_comp_get_child(cui_BluetoothItem8, UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9_IMAGE2);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT_BLUETOOTHITEM8_LABEL2] = ui_comp_get_child(
                                                                                                        cui_BluetoothItem8, UI_COMP_LISTITEM_LIST_ITEM_LEFT_LABEL2);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT] = ui_comp_get_child(cui_BluetoothItem8,
                                                                                                 UI_COMP_LISTITEM_LIST_ITEM_RIGHT);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC] =
        ui_comp_get_child(cui_BluetoothItem8, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_VALUE]
        = ui_comp_get_child(cui_BluetoothItem8, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_VALUE);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DEL]
        = ui_comp_get_child(cui_BluetoothItem8, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DEL_BLUETOOTHITEM8_IMAGE7]
        = ui_comp_get_child(cui_BluetoothItem8, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL_IMAGE7);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DIS]
        = ui_comp_get_child(cui_BluetoothItem8, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DIS_BLUETOOTHITEM8_IMAGE3]
        = ui_comp_get_child(cui_BluetoothItem8, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS_IMAGE3);
    children[UI_COMP_ITEMLIST_ITEM_LIST_PAIRED_ITEM_WARP2] = cui_item_warp2;
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6] = cui_BluetoothItem6;
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT] = ui_comp_get_child(cui_BluetoothItem6,
                                                                                                UI_COMP_LISTITEM_LIST_ITEM_LEFT);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT_BLUETOOTHITEM6_PANEL9] = ui_comp_get_child(
                                                                                                        cui_BluetoothItem6, UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT_BLUETOOTHITEM6_PANEL9_BLUETOOTHITEM6_IMAGE2] =
        ui_comp_get_child(cui_BluetoothItem6, UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9_IMAGE2);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT_BLUETOOTHITEM6_LABEL2] = ui_comp_get_child(
                                                                                                        cui_BluetoothItem6, UI_COMP_LISTITEM_LIST_ITEM_LEFT_LABEL2);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT] = ui_comp_get_child(cui_BluetoothItem6,
                                                                                                 UI_COMP_LISTITEM_LIST_ITEM_RIGHT);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC] =
        ui_comp_get_child(cui_BluetoothItem6, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_VALUE]
        = ui_comp_get_child(cui_BluetoothItem6, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_VALUE);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DEL]
        = ui_comp_get_child(cui_BluetoothItem6, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DEL_BLUETOOTHITEM6_IMAGE7]
        = ui_comp_get_child(cui_BluetoothItem6, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL_IMAGE7);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DIS]
        = ui_comp_get_child(cui_BluetoothItem6, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DIS_BLUETOOTHITEM6_IMAGE3]
        = ui_comp_get_child(cui_BluetoothItem6, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS_IMAGE3);
    children[UI_COMP_ITEMLIST_UNPAIRED_LABEL] = cui_unpaired_label;
    children[UI_COMP_ITEMLIST_ITEM_LIST_UNPAIRED] = cui_item_list_unpaired;
    children[UI_COMP_ITEMLIST_ITEM_LIST_UNPAIRED_ITEM_WARP3] = cui_item_warp3;
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2] = cui_BluetoothItem2;
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT] = ui_comp_get_child(cui_BluetoothItem2,
                                                                                                UI_COMP_LISTITEM_LIST_ITEM_LEFT);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT_BLUETOOTHITEM2_PANEL9] = ui_comp_get_child(
                                                                                                        cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT_BLUETOOTHITEM2_PANEL9_BLUETOOTHITEM2_IMAGE2] =
        ui_comp_get_child(cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9_IMAGE2);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT_BLUETOOTHITEM2_LABEL2] = ui_comp_get_child(
                                                                                                        cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_LEFT_LABEL2);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT] = ui_comp_get_child(cui_BluetoothItem2,
                                                                                                 UI_COMP_LISTITEM_LIST_ITEM_RIGHT);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC] =
        ui_comp_get_child(cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_VALUE]
        = ui_comp_get_child(cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_VALUE);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DEL]
        = ui_comp_get_child(cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DEL_BLUETOOTHITEM2_IMAGE7]
        = ui_comp_get_child(cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL_IMAGE7);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DIS]
        = ui_comp_get_child(cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DIS_BLUETOOTHITEM2_IMAGE3]
        = ui_comp_get_child(cui_BluetoothItem2, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS_IMAGE3);
    children[UI_COMP_ITEMLIST_ITEM_LIST_UNPAIRED_ITEM_WARP4] = cui_item_warp4;
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1] = cui_BluetoothItem1;
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT] = ui_comp_get_child(cui_BluetoothItem1,
                                                                                                UI_COMP_LISTITEM_LIST_ITEM_LEFT);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT_BLUETOOTHITEM1_PANEL9] = ui_comp_get_child(
                                                                                                        cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT_BLUETOOTHITEM1_PANEL9_BLUETOOTHITEM1_IMAGE2] =
        ui_comp_get_child(cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_LEFT_PANEL9_IMAGE2);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT_BLUETOOTHITEM1_LABEL2] = ui_comp_get_child(
                                                                                                        cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_LEFT_LABEL2);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT] = ui_comp_get_child(cui_BluetoothItem1,
                                                                                                 UI_COMP_LISTITEM_LIST_ITEM_RIGHT);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC] =
        ui_comp_get_child(cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_VALUE]
        = ui_comp_get_child(cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_VALUE);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DEL]
        = ui_comp_get_child(cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DEL_BLUETOOTHITEM1_IMAGE7]
        = ui_comp_get_child(cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DEL_IMAGE7);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DIS]
        = ui_comp_get_child(cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS);
    children[UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DIS_BLUETOOTHITEM1_IMAGE3]
        = ui_comp_get_child(cui_BluetoothItem1, UI_COMP_LISTITEM_LIST_ITEM_RIGHT_LIST_ITEM_RC_ITEM_STA_ICON_DIS_IMAGE3);
    children[UI_COMP_ITEMLIST_BLANK] = cui_Blank;
    lv_obj_add_event_cb(cui_ItemList, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_ItemList, del_component_child_event_cb, LV_EVENT_DELETE, children);
    lv_obj_add_event_cb(cui_BluetoothItem8, ui_event_comp_ItemList_BluetoothItem8, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_BluetoothItem2, ui_event_comp_ItemList_BluetoothItem2, LV_EVENT_ALL, children);
    ui_comp_ItemList_create_hook(cui_ItemList);
    return cui_ItemList;
}

