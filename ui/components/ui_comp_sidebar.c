//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT Sidebar

lv_obj_t * ui_Sidebar_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Sidebar;
    cui_Sidebar = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_Sidebar, 82);
    lv_obj_set_height(cui_Sidebar, 375);
    lv_obj_set_x(cui_Sidebar, 25);
    lv_obj_set_y(cui_Sidebar, -45);
    lv_obj_set_align(cui_Sidebar, LV_ALIGN_BOTTOM_LEFT);
    lv_obj_set_flex_flow(cui_Sidebar, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_Sidebar, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_Sidebar, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_Sidebar, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Sidebar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sidebar_btn_home;
    cui_sidebar_btn_home = lv_btn_create(cui_Sidebar);
    lv_obj_set_width(cui_sidebar_btn_home, 80);
    lv_obj_set_height(cui_sidebar_btn_home, 80);
    lv_obj_set_x(cui_sidebar_btn_home, 86);
    lv_obj_set_y(cui_sidebar_btn_home, 214);
    lv_obj_set_align(cui_sidebar_btn_home, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_sidebar_btn_home, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_sidebar_btn_home, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_state(cui_sidebar_btn_home, LV_STATE_CHECKED);       /// States
    lv_obj_add_flag(cui_sidebar_btn_home, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(cui_sidebar_btn_home, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(cui_sidebar_btn_home, 24, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_sidebar_btn_home, lv_color_hex(0x1A1A1A), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_home, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_sidebar_btn_home, lv_color_hex(0x021D40), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_home, 0, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_img_src(cui_sidebar_btn_home, &ui_img_dark_nav_btn_active_png, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_color(cui_sidebar_btn_home, lv_color_hex(0x021D40), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_home, 0, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(cui_sidebar_btn_home, &ui_img_dark_nav_btn_active_png, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_color(cui_sidebar_btn_home, lv_color_hex(0x1A1A1A), LV_PART_MAIN | LV_STATE_DISABLED);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_home, 255, LV_PART_MAIN | LV_STATE_DISABLED);

    lv_obj_t * cui_sidebar_btn_home_icon;
    cui_sidebar_btn_home_icon = lv_img_create(cui_sidebar_btn_home);
    lv_img_set_src(cui_sidebar_btn_home_icon, &ui_img_dark_ic_home_png);
    lv_obj_set_width(cui_sidebar_btn_home_icon, 30);
    lv_obj_set_height(cui_sidebar_btn_home_icon, 30);
    lv_obj_set_align(cui_sidebar_btn_home_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_sidebar_btn_home_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_sidebar_btn_home_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_sidebar_btn_home_label;
    cui_sidebar_btn_home_label = lv_label_create(cui_sidebar_btn_home);
    lv_obj_set_width(cui_sidebar_btn_home_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sidebar_btn_home_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_sidebar_btn_home_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_sidebar_btn_home_label, "#HOME");
    lv_obj_set_style_text_color(cui_sidebar_btn_home_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_sidebar_btn_home_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_sidebar_btn_home_label, &ui_font_AlibabaPuHui15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_sidebar_btn_home_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_sidebar_btn_home_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_sidebar_btn_home_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_sidebar_btn_home_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sidebar_btn_hicar;
    cui_sidebar_btn_hicar = lv_btn_create(cui_Sidebar);
    lv_obj_set_width(cui_sidebar_btn_hicar, 80);
    lv_obj_set_height(cui_sidebar_btn_hicar, 80);
    lv_obj_set_x(cui_sidebar_btn_hicar, 86);
    lv_obj_set_y(cui_sidebar_btn_hicar, 214);
    lv_obj_set_align(cui_sidebar_btn_hicar, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_sidebar_btn_hicar, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_sidebar_btn_hicar, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_flag(cui_sidebar_btn_hicar, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(cui_sidebar_btn_hicar, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(cui_sidebar_btn_hicar, 24, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_sidebar_btn_hicar, lv_color_hex(0x1A1A1A), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_hicar, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_sidebar_btn_hicar, lv_color_hex(0x021D40), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_hicar, 0, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_img_src(cui_sidebar_btn_hicar, &ui_img_dark_nav_btn_active_png, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_color(cui_sidebar_btn_hicar, lv_color_hex(0x021D40), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_hicar, 0, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(cui_sidebar_btn_hicar, &ui_img_dark_nav_btn_active_png, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_color(cui_sidebar_btn_hicar, lv_color_hex(0x1A1A1A), LV_PART_MAIN | LV_STATE_DISABLED);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_hicar, 255, LV_PART_MAIN | LV_STATE_DISABLED);

    lv_obj_t * cui_sidebar_btn_hicar_icon;
    cui_sidebar_btn_hicar_icon = lv_img_create(cui_sidebar_btn_hicar);
    lv_img_set_src(cui_sidebar_btn_hicar_icon, &ui_img_dark_ic_hicar_png);
    lv_obj_set_width(cui_sidebar_btn_hicar_icon, 30);
    lv_obj_set_height(cui_sidebar_btn_hicar_icon, 30);
    lv_obj_set_align(cui_sidebar_btn_hicar_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_sidebar_btn_hicar_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_sidebar_btn_hicar_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_sidebar_btn_hicar_label;
    cui_sidebar_btn_hicar_label = lv_label_create(cui_sidebar_btn_hicar);
    lv_obj_set_width(cui_sidebar_btn_hicar_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sidebar_btn_hicar_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_sidebar_btn_hicar_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_sidebar_btn_hicar_label, "#HICAR");
    lv_obj_set_style_text_color(cui_sidebar_btn_hicar_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_sidebar_btn_hicar_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_sidebar_btn_hicar_label, &ui_font_AlibabaPuHui15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_sidebar_btn_hicar_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_sidebar_btn_hicar_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_sidebar_btn_hicar_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_sidebar_btn_hicar_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sidebar_btn_setting;
    cui_sidebar_btn_setting = lv_btn_create(cui_Sidebar);
    lv_obj_set_width(cui_sidebar_btn_setting, 80);
    lv_obj_set_height(cui_sidebar_btn_setting, 80);
    lv_obj_set_x(cui_sidebar_btn_setting, 86);
    lv_obj_set_y(cui_sidebar_btn_setting, 214);
    lv_obj_set_align(cui_sidebar_btn_setting, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_sidebar_btn_setting, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_sidebar_btn_setting, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_flag(cui_sidebar_btn_setting, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(cui_sidebar_btn_setting, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(cui_sidebar_btn_setting, 24, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_sidebar_btn_setting, lv_color_hex(0x1A1A1A), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_setting, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_sidebar_btn_setting, lv_color_hex(0x021D40), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_setting, 0, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_img_src(cui_sidebar_btn_setting, &ui_img_dark_nav_btn_active_png, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_color(cui_sidebar_btn_setting, lv_color_hex(0x021D40), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_setting, 0, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(cui_sidebar_btn_setting, &ui_img_dark_nav_btn_active_png, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_color(cui_sidebar_btn_setting, lv_color_hex(0x1A1A1A), LV_PART_MAIN | LV_STATE_DISABLED);
    lv_obj_set_style_bg_opa(cui_sidebar_btn_setting, 255, LV_PART_MAIN | LV_STATE_DISABLED);

    lv_obj_t * cui_sidebar_btn_setting_icon;
    cui_sidebar_btn_setting_icon = lv_img_create(cui_sidebar_btn_setting);
    lv_img_set_src(cui_sidebar_btn_setting_icon, &ui_img_dark_ic_setting_png);
    lv_obj_set_width(cui_sidebar_btn_setting_icon, 30);
    lv_obj_set_height(cui_sidebar_btn_setting_icon, 30);
    lv_obj_set_align(cui_sidebar_btn_setting_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_sidebar_btn_setting_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_sidebar_btn_setting_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_sidebar_btn_setting_label;
    cui_sidebar_btn_setting_label = lv_label_create(cui_sidebar_btn_setting);
    lv_obj_set_width(cui_sidebar_btn_setting_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sidebar_btn_setting_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_sidebar_btn_setting_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_sidebar_btn_setting_label, "#SETTING");
    lv_obj_set_style_text_color(cui_sidebar_btn_setting_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_sidebar_btn_setting_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_sidebar_btn_setting_label, &ui_font_AlibabaPuHui15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_sidebar_btn_setting_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_sidebar_btn_setting_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_sidebar_btn_setting_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_sidebar_btn_setting_label, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SIDEBAR_NUM);
    children[UI_COMP_SIDEBAR_SIDEBAR] = cui_Sidebar;
    children[UI_COMP_SIDEBAR_SIDEBAR_BTN_HOME] = cui_sidebar_btn_home;
    children[UI_COMP_SIDEBAR_SIDEBAR_BTN_HOME_SIDEBAR_BTN_HOME_ICON] = cui_sidebar_btn_home_icon;
    children[UI_COMP_SIDEBAR_SIDEBAR_BTN_HOME_SIDEBAR_BTN_HOME_LABEL] = cui_sidebar_btn_home_label;
    children[UI_COMP_SIDEBAR_SIDEBAR_BTN_HICAR] = cui_sidebar_btn_hicar;
    children[UI_COMP_SIDEBAR_SIDEBAR_BTN_HICAR_SIDEBAR_BTN_HICAR_ICON] = cui_sidebar_btn_hicar_icon;
    children[UI_COMP_SIDEBAR_SIDEBAR_BTN_HICAR_SIDEBAR_BTN_HICAR_LABEL] = cui_sidebar_btn_hicar_label;
    children[UI_COMP_SIDEBAR_SIDEBAR_BTN_SETTING] = cui_sidebar_btn_setting;
    children[UI_COMP_SIDEBAR_SIDEBAR_BTN_SETTING_SIDEBAR_BTN_SETTING_ICON] = cui_sidebar_btn_setting_icon;
    children[UI_COMP_SIDEBAR_SIDEBAR_BTN_SETTING_SIDEBAR_BTN_SETTING_LABEL] = cui_sidebar_btn_setting_label;
    lv_obj_add_event_cb(cui_Sidebar, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Sidebar, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_Sidebar_create_hook(cui_Sidebar);
    return cui_Sidebar;
}

