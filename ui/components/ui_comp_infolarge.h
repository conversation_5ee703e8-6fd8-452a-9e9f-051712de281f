//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_INFOLARGE_H
#define _UI_COMP_INFOLARGE_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT InfoLarge
#define UI_COMP_INFOLARGE_INFOLARGE 0
#define UI_COMP_INFOLARGE_INFO_LEFT 1
#define UI_COMP_INFOLARGE_INFO_LEFT_INFO_ICON 2
#define UI_COMP_INFOLARGE_INFO_LEFT_LEFT_LABEL_VAL 3
#define UI_COMP_INFOLARGE_INFO_RIGHT 4
#define UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL 5
#define UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_ICON 6
#define _UI_COMP_INFOLARGE_NUM 7
lv_obj_t * ui_InfoLarge_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
