//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

void ui_event_comp_DialogView_item_list_BluetoothItem8(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_DialogView = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_dialog_msg, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
        _ui_flag_modify(comp_DialogView[UI_COMP_DIALOGVIEW_DIALOGVIEW], LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(ui_setting, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_DialogView_item_list_BluetoothItem2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_DialogView = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(comp_DialogView[UI_COMP_DIALOGVIEW_DIALOGVIEW], LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(ui_dialog_confirm, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

// COMPONENT DialogView

lv_obj_t * ui_DialogView_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_DialogView;
    cui_DialogView = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_DialogView, lv_pct(100));
    lv_obj_set_height(cui_DialogView, lv_pct(100));
    lv_obj_set_align(cui_DialogView, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_DialogView, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_DialogView, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_DialogView, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_DialogView, 124, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_DialogView, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_DialogView, 100, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_DialogView, 25, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_dialog_view_bg;
    cui_dialog_view_bg = lv_obj_create(cui_DialogView);
    lv_obj_set_width(cui_dialog_view_bg, lv_pct(100));
    lv_obj_set_height(cui_dialog_view_bg, lv_pct(100));
    lv_obj_set_align(cui_dialog_view_bg, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_dialog_view_bg, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_dialog_view_bg, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_dialog_view_bg, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_dialog_view_bg, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_dialog_view_bg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_dialog_view_bg, &ui_img_dialog_dialog_view_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_dialog_view_warp;
    cui_dialog_view_warp = lv_obj_create(cui_DialogView);
    lv_obj_set_width(cui_dialog_view_warp, lv_pct(100));
    lv_obj_set_height(cui_dialog_view_warp, lv_pct(100));
    lv_obj_set_align(cui_dialog_view_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_dialog_view_warp, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_dialog_view_warp, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_dialog_view_warp, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_dialog_view_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_dialog_view_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_dialog_view_warp, &ui_img_dialog_dialog_view_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_dialog_view_warp, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_dialog_view_warp, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_dialog_view_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_dialog_view_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_view_head;
    cui_view_head = lv_obj_create(cui_dialog_view_warp);
    lv_obj_set_height(cui_view_head, 90);
    lv_obj_set_width(cui_view_head, lv_pct(100));
    lv_obj_set_align(cui_view_head, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_view_head, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_view_head, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_SPACE_BETWEEN);
    lv_obj_clear_flag(cui_view_head, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_view_head, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_view_head, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_head_left;
    cui_head_left = lv_obj_create(cui_view_head);
    lv_obj_set_height(cui_head_left, 50);
    lv_obj_set_width(cui_head_left, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_head_left, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_head_left, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_head_left, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_head_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_head_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_head_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_head_icon;
    cui_head_icon = lv_obj_create(cui_head_left);
    lv_obj_set_width(cui_head_icon, 50);
    lv_obj_set_height(cui_head_icon, 50);
    lv_obj_set_align(cui_head_icon, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_head_icon, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_head_icon, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_head_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_head_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_head_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Image1;
    cui_Image1 = lv_img_create(cui_head_icon);
    lv_img_set_src(cui_Image1, &ui_img_dialog_ic_return_png);
    lv_obj_set_width(cui_Image1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_Image1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_Image1, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_Image1, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_Image1, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_head_label;
    cui_head_label = lv_label_create(cui_head_left);
    lv_obj_set_width(cui_head_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_head_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_head_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_head_label, "#BLUETOOTH");
    lv_obj_set_style_text_color(cui_head_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_head_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_head_label, &lv_font_montserrat_28, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_head_label, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_head_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_head_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_head_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_head_right;
    cui_head_right = lv_obj_create(cui_view_head);
    lv_obj_set_width(cui_head_right, 100);
    lv_obj_set_height(cui_head_right, 50);
    lv_obj_set_align(cui_head_right, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_head_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_head_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_head_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_SwitchBtn;
    cui_SwitchBtn = ui_SwitchBtn_create(cui_head_right);
    lv_obj_set_x(cui_SwitchBtn, 0);
    lv_obj_set_y(cui_SwitchBtn, 0);
    lv_obj_clear_flag(cui_SwitchBtn, LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_obj_t * cui_dialog_view_main;
    cui_dialog_view_main = lv_obj_create(cui_dialog_view_warp);
    lv_obj_set_height(cui_dialog_view_main, 386);
    lv_obj_set_width(cui_dialog_view_main, lv_pct(100));
    lv_obj_set_align(cui_dialog_view_main, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(cui_dialog_view_main, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_dialog_view_main, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_list;
    cui_item_list = ui_ItemList_create(cui_dialog_view_main);
    lv_obj_set_x(cui_item_list, 0);
    lv_obj_set_y(cui_item_list, 0);
    lv_obj_clear_flag(cui_item_list, LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_obj_t * cui_list_empty;
    cui_list_empty = ui_ViewState_create(cui_dialog_view_main);
    lv_obj_set_x(cui_list_empty, 0);
    lv_obj_set_y(cui_list_empty, 0);
    lv_obj_clear_flag(cui_list_empty, LV_OBJ_FLAG_HIDDEN);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_DIALOGVIEW_NUM);
    children[UI_COMP_DIALOGVIEW_DIALOGVIEW] = cui_DialogView;
    children[UI_COMP_DIALOGVIEW_DIALOG_VIEW_BG] = cui_dialog_view_bg;
    children[UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP] = cui_dialog_view_warp;
    children[UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD] = cui_view_head;
    children[UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_LEFT] = cui_head_left;
    children[UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_LEFT_HEAD_ICON] = cui_head_icon;
    children[UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_LEFT_HEAD_ICON_IMAGE1] = cui_Image1;
    children[UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_LEFT_HEAD_LABEL] = cui_head_label;
    children[UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_VIEW_HEAD_HEAD_RIGHT] = cui_head_right;
    children[UI_COMP_DIALOGVIEW_SWITCHBTN] = cui_SwitchBtn;
    children[UI_COMP_DIALOGVIEW_DIALOG_VIEW_WARP_DIALOG_VIEW_MAIN] = cui_dialog_view_main;
    children[UI_COMP_DIALOGVIEW_ITEM_LIST] = cui_item_list;
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_PAIRED_LABEL] = ui_comp_get_child(cui_item_list,
                                                                                      UI_COMP_ITEMLIST_PAIRED_LABEL);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED] = ui_comp_get_child(cui_item_list,
                                                                                          UI_COMP_ITEMLIST_ITEM_LIST_PAIRED);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1] = ui_comp_get_child(
                                                                                                 cui_item_list, UI_COMP_ITEMLIST_ITEM_LIST_PAIRED_ITEM_WARP1);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8] =
        ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM8);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_LEFT]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT_BLUETOOTHITEM8_PANEL9);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9_ITEM_LIST_IMAGE2]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT_BLUETOOTHITEM8_PANEL9_BLUETOOTHITEM8_IMAGE2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_LABEL2]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_LEFT_BLUETOOTHITEM8_LABEL2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_VALUE]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_VALUE);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DEL);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL_ITEM_LIST_IMAGE7]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DEL_BLUETOOTHITEM8_IMAGE7);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DIS);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP1_ITEM_LIST_BLUETOOTHITEM8_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS_ITEM_LIST_IMAGE3]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM8_BLUETOOTHITEM8_LIST_ITEM_RIGHT_BLUETOOTHITEM8_LIST_ITEM_RC_BLUETOOTHITEM8_ITEM_STA_ICON_DIS_BLUETOOTHITEM8_IMAGE3);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2] = ui_comp_get_child(
                                                                                                 cui_item_list, UI_COMP_ITEMLIST_ITEM_LIST_PAIRED_ITEM_WARP2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6] =
        ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM6);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_LEFT]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT_BLUETOOTHITEM6_PANEL9);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9_ITEM_LIST_IMAGE2]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT_BLUETOOTHITEM6_PANEL9_BLUETOOTHITEM6_IMAGE2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_LABEL2]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_LEFT_BLUETOOTHITEM6_LABEL2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_VALUE]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_VALUE);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DEL);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL_ITEM_LIST_IMAGE7]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DEL_BLUETOOTHITEM6_IMAGE7);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DIS);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_PAIRED_ITEM_LIST_ITEM_WARP2_ITEM_LIST_BLUETOOTHITEM6_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS_ITEM_LIST_IMAGE3]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM6_BLUETOOTHITEM6_LIST_ITEM_RIGHT_BLUETOOTHITEM6_LIST_ITEM_RC_BLUETOOTHITEM6_ITEM_STA_ICON_DIS_BLUETOOTHITEM6_IMAGE3);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_UNPAIRED_LABEL] = ui_comp_get_child(cui_item_list,
                                                                                        UI_COMP_ITEMLIST_UNPAIRED_LABEL);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED] = ui_comp_get_child(cui_item_list,
                                                                                            UI_COMP_ITEMLIST_ITEM_LIST_UNPAIRED);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3] = ui_comp_get_child(
                                                                                                   cui_item_list, UI_COMP_ITEMLIST_ITEM_LIST_UNPAIRED_ITEM_WARP3);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2] =
        ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_LEFT]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT_BLUETOOTHITEM2_PANEL9);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9_ITEM_LIST_IMAGE2]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT_BLUETOOTHITEM2_PANEL9_BLUETOOTHITEM2_IMAGE2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_LABEL2]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_LEFT_BLUETOOTHITEM2_LABEL2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_VALUE]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_VALUE);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DEL);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL_ITEM_LIST_IMAGE7]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DEL_BLUETOOTHITEM2_IMAGE7);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DIS);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP3_ITEM_LIST_BLUETOOTHITEM2_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS_ITEM_LIST_IMAGE3]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM2_BLUETOOTHITEM2_LIST_ITEM_RIGHT_BLUETOOTHITEM2_LIST_ITEM_RC_BLUETOOTHITEM2_ITEM_STA_ICON_DIS_BLUETOOTHITEM2_IMAGE3);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4] = ui_comp_get_child(
                                                                                                   cui_item_list, UI_COMP_ITEMLIST_ITEM_LIST_UNPAIRED_ITEM_WARP4);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1] =
        ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM1);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_LEFT]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT_BLUETOOTHITEM1_PANEL9);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_PANEL9_ITEM_LIST_IMAGE2]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT_BLUETOOTHITEM1_PANEL9_BLUETOOTHITEM1_IMAGE2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_LEFT_ITEM_LIST_LABEL2]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_LEFT_BLUETOOTHITEM1_LABEL2);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT]
        = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_VALUE]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_VALUE);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DEL);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DEL_ITEM_LIST_IMAGE7]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DEL_BLUETOOTHITEM1_IMAGE7);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DIS);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_ITEM_LIST_UNPAIRED_ITEM_LIST_ITEM_WARP4_ITEM_LIST_BLUETOOTHITEM1_ITEM_LIST_LIST_ITEM_RIGHT_ITEM_LIST_LIST_ITEM_RC_ITEM_LIST_ITEM_STA_ICON_DIS_ITEM_LIST_IMAGE3]
        = ui_comp_get_child(cui_item_list,
                            UI_COMP_ITEMLIST_BLUETOOTHITEM1_BLUETOOTHITEM1_LIST_ITEM_RIGHT_BLUETOOTHITEM1_LIST_ITEM_RC_BLUETOOTHITEM1_ITEM_STA_ICON_DIS_BLUETOOTHITEM1_IMAGE3);
    children[UI_COMP_DIALOGVIEW_ITEM_LIST_ITEM_LIST_BLANK] = ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLANK);
    children[UI_COMP_DIALOGVIEW_LIST_EMPTY] = cui_list_empty;
    children[UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_ICON] = ui_comp_get_child(cui_list_empty,
                                                                                       UI_COMP_VIEWSTATE_STATUS_ICON);
    children[UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_ICON_LIST_EMPTY_DIALOG_CONNECT_ICON_ASSET] = ui_comp_get_child(
                                                                                                              cui_list_empty, UI_COMP_VIEWSTATE_STATUS_ICON_DIALOG_CONNECT_ICON_ASSET);
    children[UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_LABEL_WARP] = ui_comp_get_child(cui_list_empty,
                                                                                             UI_COMP_VIEWSTATE_STATUS_LABEL_WARP);
    children[UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_LABEL_WARP_LIST_EMPTY_STATUS_LABEL] = ui_comp_get_child(
                                                                                                       cui_list_empty, UI_COMP_VIEWSTATE_STATUS_LABEL_WARP_STATUS_LABEL);
    children[UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_SUB_WARP] = ui_comp_get_child(cui_list_empty,
                                                                                           UI_COMP_VIEWSTATE_STATUS_SUB_WARP);
    children[UI_COMP_DIALOGVIEW_LIST_EMPTY_LIST_EMPTY_STATUS_SUB_WARP_LIST_EMPTY_STATUS_SUB_LABEL] = ui_comp_get_child(
                                                                                                         cui_list_empty, UI_COMP_VIEWSTATE_STATUS_SUB_WARP_STATUS_SUB_LABEL);
    lv_obj_add_event_cb(cui_DialogView, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_DialogView, del_component_child_event_cb, LV_EVENT_DELETE, children);
    lv_obj_add_event_cb(ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM8),
                        ui_event_comp_DialogView_item_list_BluetoothItem8, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(ui_comp_get_child(cui_item_list, UI_COMP_ITEMLIST_BLUETOOTHITEM2),
                        ui_event_comp_DialogView_item_list_BluetoothItem2, LV_EVENT_ALL, children);
    ui_comp_DialogView_create_hook(cui_DialogView);
    return cui_DialogView;
}

