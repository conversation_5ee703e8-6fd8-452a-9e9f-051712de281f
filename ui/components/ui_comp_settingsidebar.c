//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

void ui_event_comp_SettingSidebar_Settingbtn1(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_SettingSidebar = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_state_modify(comp_SettingSidebar[UI_COMP_SETTINGSIDEBAR_SETTINGBTN3], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_SettingSidebar[UI_COMP_SETTINGSIDEBAR_SETTINGBTN2], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_SettingSidebar[UI_COMP_SETTINGSIDEBAR_SETTINGBTN1], LV_STATE_CHECKED, _UI_MODIFY_STATE_ADD);
    }
}

void ui_event_comp_SettingSidebar_Settingbtn2(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_SettingSidebar = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_state_modify(comp_SettingSidebar[UI_COMP_SETTINGSIDEBAR_SETTINGBTN3], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_SettingSidebar[UI_COMP_SETTINGSIDEBAR_SETTINGBTN1], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_SettingSidebar[UI_COMP_SETTINGSIDEBAR_SETTINGBTN2], LV_STATE_CHECKED, _UI_MODIFY_STATE_ADD);
    }
    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_other_setting_warp, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(ui_system_setting_warp, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(ui_vehicle_setting_warp, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

void ui_event_comp_SettingSidebar_Settingbtn3(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t ** comp_SettingSidebar = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_CLICKED) {
        _ui_state_modify(comp_SettingSidebar[UI_COMP_SETTINGSIDEBAR_SETTINGBTN2], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_SettingSidebar[UI_COMP_SETTINGSIDEBAR_SETTINGBTN1], LV_STATE_CHECKED, _UI_MODIFY_STATE_REMOVE);
        _ui_state_modify(comp_SettingSidebar[UI_COMP_SETTINGSIDEBAR_SETTINGBTN3], LV_STATE_CHECKED, _UI_MODIFY_STATE_ADD);
    }
    if(event_code == LV_EVENT_CLICKED) {
        _ui_flag_modify(ui_vehicle_setting_warp, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(ui_system_setting_warp, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
        _ui_flag_modify(ui_other_setting_warp, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    }
}

// COMPONENT SettingSidebar

lv_obj_t * ui_SettingSidebar_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_SettingSidebar;
    cui_SettingSidebar = lv_obj_create(comp_parent);
    lv_obj_set_height(cui_SettingSidebar, lv_pct(100));
    lv_obj_set_flex_grow(cui_SettingSidebar, 3);
    lv_obj_set_x(cui_SettingSidebar, 1);
    lv_obj_set_y(cui_SettingSidebar, 0);
    lv_obj_set_align(cui_SettingSidebar, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_SettingSidebar, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_SettingSidebar, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SettingSidebar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sidebar_head;
    cui_sidebar_head = lv_obj_create(cui_SettingSidebar);
    lv_obj_set_height(cui_sidebar_head, 78);
    lv_obj_set_width(cui_sidebar_head, lv_pct(100));
    lv_obj_set_flex_flow(cui_sidebar_head, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_sidebar_head, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_sidebar_head, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_sidebar_head, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sidebar_head, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sidebar_label;
    cui_sidebar_label = lv_label_create(cui_sidebar_head);
    lv_obj_set_width(cui_sidebar_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sidebar_label, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_sidebar_label, LV_ALIGN_CENTER);
    lv_label_set_text(cui_sidebar_label, "#SETTING");
    lv_obj_set_style_text_color(cui_sidebar_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_sidebar_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_sidebar_label, &ui_font_AlibabaPuHui26, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_sidebar_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_sidebar_label, 28, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_sidebar_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_sidebar_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sidebar_items;
    cui_sidebar_items = lv_obj_create(cui_SettingSidebar);
    lv_obj_set_width(cui_sidebar_items, lv_pct(100));
    lv_obj_set_height(cui_sidebar_items, lv_pct(82));
    lv_obj_set_align(cui_sidebar_items, LV_ALIGN_BOTTOM_RIGHT);
    lv_obj_set_flex_flow(cui_sidebar_items, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_sidebar_items, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_bg_color(cui_sidebar_items, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sidebar_items, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_Settingbtn1;
    cui_Settingbtn1 = ui_Settingbtn_create(cui_sidebar_items);
    lv_obj_set_x(cui_Settingbtn1, -299);
    lv_obj_set_y(cui_Settingbtn1, -14);
    lv_obj_add_state(cui_Settingbtn1, LV_STATE_CHECKED);       /// States

    lv_label_set_long_mode(ui_comp_get_child(cui_Settingbtn1, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), LV_LABEL_LONG_WRAP);
    lv_label_set_text(ui_comp_get_child(cui_Settingbtn1, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), "#SYSTEM");

    lv_obj_t * cui_Settingbtn2;
    cui_Settingbtn2 = ui_Settingbtn_create(cui_sidebar_items);
    lv_obj_set_x(cui_Settingbtn2, -299);
    lv_obj_set_y(cui_Settingbtn2, -14);

    lv_label_set_long_mode(ui_comp_get_child(cui_Settingbtn2, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), LV_LABEL_LONG_WRAP);
    lv_label_set_text(ui_comp_get_child(cui_Settingbtn2, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), "#VEHICLE");

    lv_obj_t * cui_Settingbtn3;
    cui_Settingbtn3 = ui_Settingbtn_create(cui_sidebar_items);
    lv_obj_set_x(cui_Settingbtn3, -299);
    lv_obj_set_y(cui_Settingbtn3, -14);

    lv_label_set_long_mode(ui_comp_get_child(cui_Settingbtn3, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), LV_LABEL_LONG_WRAP);
    lv_label_set_text(ui_comp_get_child(cui_Settingbtn3, UI_COMP_SETTINGBTN_SETTING_BTN_LABEL), "#OTHER");

    lv_obj_t * cui_Spaceline;
    cui_Spaceline = ui_Spaceline_create(cui_SettingSidebar);
    lv_obj_set_x(cui_Spaceline, 82);
    lv_obj_set_y(cui_Spaceline, 0);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SETTINGSIDEBAR_NUM);
    children[UI_COMP_SETTINGSIDEBAR_SETTINGSIDEBAR] = cui_SettingSidebar;
    children[UI_COMP_SETTINGSIDEBAR_SIDEBAR_HEAD] = cui_sidebar_head;
    children[UI_COMP_SETTINGSIDEBAR_SIDEBAR_HEAD_SIDEBAR_LABEL] = cui_sidebar_label;
    children[UI_COMP_SETTINGSIDEBAR_SIDEBAR_ITEMS] = cui_sidebar_items;
    children[UI_COMP_SETTINGSIDEBAR_SETTINGBTN1] = cui_Settingbtn1;
    children[UI_COMP_SETTINGSIDEBAR_SETTINGBTN1_SETTINGBTN1_SYSTEM_LABEL] = ui_comp_get_child(cui_Settingbtn1,
                                                                                              UI_COMP_SETTINGBTN_SETTING_BTN_LABEL);
    children[UI_COMP_SETTINGSIDEBAR_SETTINGBTN2] = cui_Settingbtn2;
    children[UI_COMP_SETTINGSIDEBAR_SETTINGBTN2_SETTINGBTN2_SYSTEM_LABEL2] = ui_comp_get_child(cui_Settingbtn2,
                                                                                               UI_COMP_SETTINGBTN_SETTING_BTN_LABEL);
    children[UI_COMP_SETTINGSIDEBAR_SETTINGBTN3] = cui_Settingbtn3;
    children[UI_COMP_SETTINGSIDEBAR_SETTINGBTN3_SETTINGBTN3_SYSTEM_LABEL3] = ui_comp_get_child(cui_Settingbtn3,
                                                                                               UI_COMP_SETTINGBTN_SETTING_BTN_LABEL);
    children[UI_COMP_SETTINGSIDEBAR_SPACELINE] = cui_Spaceline;
    children[UI_COMP_SETTINGSIDEBAR_SPACELINE_SPACELINE_PANEL3] = ui_comp_get_child(cui_Spaceline,
                                                                                    UI_COMP_SPACELINE_PANEL3);
    children[UI_COMP_SETTINGSIDEBAR_SPACELINE_SPACELINE_PANEL2] = ui_comp_get_child(cui_Spaceline,
                                                                                    UI_COMP_SPACELINE_PANEL2);
    lv_obj_add_event_cb(cui_SettingSidebar, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_SettingSidebar, del_component_child_event_cb, LV_EVENT_DELETE, children);
    lv_obj_add_event_cb(cui_Settingbtn1, ui_event_comp_SettingSidebar_Settingbtn1, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_Settingbtn2, ui_event_comp_SettingSidebar_Settingbtn2, LV_EVENT_ALL, children);
    lv_obj_add_event_cb(cui_Settingbtn3, ui_event_comp_SettingSidebar_Settingbtn3, LV_EVENT_ALL, children);
    ui_comp_SettingSidebar_create_hook(cui_SettingSidebar);
    return cui_SettingSidebar;
}

