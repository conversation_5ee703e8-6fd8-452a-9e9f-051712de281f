//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

void ui_event_comp_SettingVolume_SliderVolume(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t ** comp_SettingVolume = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_VALUE_CHANGED) {
        _ui_slider_set_text_value(comp_SettingVolume[UI_COMP_SETTINGVOLUME_ITEM_WARP_ITEM_RIGHT_VOLUME_VAL], target, "", "");
    }
}

// COMPONENT SettingVolume

lv_obj_t * ui_SettingVolume_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_SettingVolume;
    cui_SettingVolume = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_SettingVolume, 313);
    lv_obj_set_height(cui_SettingVolume, 95);
    lv_obj_set_align(cui_SettingVolume, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_SettingVolume, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_SettingVolume, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_SettingVolume, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_SettingVolume, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SettingVolume, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_SettingVolume, &ui_img_dark_ic_setting_btn_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_SettingVolume, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_SettingVolume, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_SettingVolume, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_SettingVolume, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_icon;
    cui_item_icon = lv_img_create(cui_SettingVolume);
    lv_img_set_src(cui_item_icon, &ui_img_setting_ic_setting_volume_png);
    lv_obj_set_width(cui_item_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_item_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_item_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_item_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_item_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_item_warp;
    cui_item_warp = lv_obj_create(cui_SettingVolume);
    lv_obj_set_width(cui_item_warp, 231);
    lv_obj_set_height(cui_item_warp, 50);
    lv_obj_set_x(cui_item_warp, -27);
    lv_obj_set_y(cui_item_warp, 3);
    lv_obj_set_align(cui_item_warp, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_warp, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_item_warp, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_warp, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_left;
    cui_item_left = lv_obj_create(cui_item_warp);
    lv_obj_set_width(cui_item_left, 210);
    lv_obj_set_height(cui_item_left, 50);
    lv_obj_set_x(cui_item_left, -25);
    lv_obj_set_y(cui_item_left, 4);
    lv_obj_set_align(cui_item_left, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_left, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_item_left, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_item_left, 18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_item_left, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_item_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_item_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_SliderVolume;
    cui_SliderVolume = lv_slider_create(cui_item_left);
    lv_slider_set_range(cui_SliderVolume, 0, 10);
    lv_slider_set_value(cui_SliderVolume, 5, LV_ANIM_OFF);
    if(lv_slider_get_mode(cui_SliderVolume) == LV_SLIDER_MODE_RANGE) lv_slider_set_left_value(cui_SliderVolume, 0,
                                                                                                  LV_ANIM_OFF);
    lv_obj_set_width(cui_SliderVolume, 180);
    lv_obj_set_height(cui_SliderVolume, 12);
    lv_obj_set_x(cui_SliderVolume, -65);
    lv_obj_set_y(cui_SliderVolume, 145);
    lv_obj_set_align(cui_SliderVolume, LV_ALIGN_CENTER);
    lv_obj_set_style_radius(cui_SliderVolume, 35, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SliderVolume, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SliderVolume, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(cui_SliderVolume, lv_color_hex(0x25AAFC), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SliderVolume, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(cui_SliderVolume, lv_color_hex(0x0DE1F2), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(cui_SliderVolume, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(cui_SliderVolume, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(cui_SliderVolume, LV_GRAD_DIR_HOR, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_set_style_radius(cui_SliderVolume, 14, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SliderVolume, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SliderVolume, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_color(cui_SliderVolume, lv_color_hex(0xFF005B), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_opa(cui_SliderVolume, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(cui_SliderVolume, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_spread(cui_SliderVolume, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_x(cui_SliderVolume, -2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_ofs_y(cui_SliderVolume, 0, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_SliderVolume, 2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_SliderVolume, 2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_SliderVolume, 2, LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_SliderVolume, 2, LV_PART_KNOB | LV_STATE_DEFAULT);

    lv_obj_t * cui_item_right;
    cui_item_right = lv_obj_create(cui_item_warp);
    lv_obj_set_height(cui_item_right, 50);
    lv_obj_set_width(cui_item_right, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_item_right, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_item_right, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_item_right, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_item_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_item_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_item_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_volume_val;
    cui_volume_val = lv_label_create(cui_item_right);
    lv_obj_set_width(cui_volume_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_volume_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_volume_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_volume_val, "5");
    lv_obj_set_style_text_color(cui_volume_val, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_volume_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SETTINGVOLUME_NUM);
    children[UI_COMP_SETTINGVOLUME_SETTINGVOLUME] = cui_SettingVolume;
    children[UI_COMP_SETTINGVOLUME_ITEM_ICON] = cui_item_icon;
    children[UI_COMP_SETTINGVOLUME_ITEM_WARP] = cui_item_warp;
    children[UI_COMP_SETTINGVOLUME_ITEM_WARP_ITEM_LEFT] = cui_item_left;
    children[UI_COMP_SETTINGVOLUME_ITEM_WARP_ITEM_LEFT_SLIDERVOLUME] = cui_SliderVolume;
    children[UI_COMP_SETTINGVOLUME_ITEM_WARP_ITEM_RIGHT] = cui_item_right;
    children[UI_COMP_SETTINGVOLUME_ITEM_WARP_ITEM_RIGHT_VOLUME_VAL] = cui_volume_val;
    lv_obj_add_event_cb(cui_SettingVolume, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_SettingVolume, del_component_child_event_cb, LV_EVENT_DELETE, children);
    lv_obj_add_event_cb(cui_SliderVolume, ui_event_comp_SettingVolume_SliderVolume, LV_EVENT_ALL, children);
    ui_comp_SettingVolume_create_hook(cui_SettingVolume);
    return cui_SettingVolume;
}

