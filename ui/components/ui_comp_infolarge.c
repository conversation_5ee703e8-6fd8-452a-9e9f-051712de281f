//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT InfoLarge

lv_obj_t * ui_InfoLarge_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_InfoLarge;
    cui_InfoLarge = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_InfoLarge, 643);
    lv_obj_set_height(cui_InfoLarge, 82);
    lv_obj_set_align(cui_InfoLarge, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_InfoLarge, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_InfoLarge, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_InfoLarge, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_InfoLarge, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_InfoLarge, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_InfoLarge, &ui_img_dark_ic_setting_btn_xl_bg_png, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_InfoLarge, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_InfoLarge, 18, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_InfoLarge, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_InfoLarge, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_info_left;
    cui_info_left = lv_obj_create(cui_InfoLarge);
    lv_obj_set_height(cui_info_left, lv_pct(100));
    lv_obj_set_width(cui_info_left, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_info_left, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_info_left, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_info_left, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_info_left, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_info_left, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_info_left, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_info_icon;
    cui_info_icon = lv_img_create(cui_info_left);
    lv_img_set_src(cui_info_icon, &ui_img_setting_ic_setting_reset_png);
    lv_obj_set_width(cui_info_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_info_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_info_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_info_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_info_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_left_label_val;
    cui_left_label_val = lv_label_create(cui_info_left);
    lv_obj_set_width(cui_left_label_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_left_label_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_left_label_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_left_label_val, "#RESET");
    lv_obj_set_style_text_color(cui_left_label_val, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_left_label_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_left_label_val, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_left_label_val, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_left_label_val, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_left_label_val, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_info_right;
    cui_info_right = lv_obj_create(cui_InfoLarge);
    lv_obj_set_height(cui_info_right, lv_pct(100));
    lv_obj_set_width(cui_info_right, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_info_right, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_info_right, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_info_right, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_info_right, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_info_right, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_info_right, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_right_label_val;
    cui_right_label_val = lv_label_create(cui_info_right);
    lv_obj_set_width(cui_right_label_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_right_label_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_right_label_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_right_label_val, "#EXECUTE");
    lv_obj_set_style_text_color(cui_right_label_val, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_right_label_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_right_icon;
    cui_right_icon = lv_img_create(cui_info_right);
    lv_img_set_src(cui_right_icon, &ui_img_setting_ic_setting_arrow_png);
    lv_obj_set_width(cui_right_icon, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_right_icon, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_right_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_right_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_right_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_INFOLARGE_NUM);
    children[UI_COMP_INFOLARGE_INFOLARGE] = cui_InfoLarge;
    children[UI_COMP_INFOLARGE_INFO_LEFT] = cui_info_left;
    children[UI_COMP_INFOLARGE_INFO_LEFT_INFO_ICON] = cui_info_icon;
    children[UI_COMP_INFOLARGE_INFO_LEFT_LEFT_LABEL_VAL] = cui_left_label_val;
    children[UI_COMP_INFOLARGE_INFO_RIGHT] = cui_info_right;
    children[UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_LABEL_VAL] = cui_right_label_val;
    children[UI_COMP_INFOLARGE_INFO_RIGHT_RIGHT_ICON] = cui_right_icon;
    lv_obj_add_event_cb(cui_InfoLarge, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_InfoLarge, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_InfoLarge_create_hook(cui_InfoLarge);
    return cui_InfoLarge;
}

