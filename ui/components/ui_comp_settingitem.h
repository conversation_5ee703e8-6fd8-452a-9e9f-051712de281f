//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SETTINGITEM_H
#define _UI_COMP_SETTINGITEM_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT SettingItem
#define UI_COMP_SETTINGITEM_SETTINGITEM 0
#define UI_COMP_SETTINGITEM_ITEM_ICON 1
#define UI_COMP_SETTINGITEM_ITEM_LABEL 2
#define UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_LEFT 3
#define UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_LEFT_ITEM_LABEL_VAL 4
#define UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_RIGHT 5
#define UI_COMP_SETTINGITEM_ITEM_LABEL_ITEM_LABEL_RIGHT_LIST_ITEM_SUB 6
#define UI_COMP_SETTINGITEM_ARROW_ICON 7
#define _UI_COMP_SETTINGITEM_NUM 8
lv_obj_t * ui_SettingItem_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
