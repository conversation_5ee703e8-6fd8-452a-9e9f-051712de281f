//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT Hicar

lv_obj_t * ui_<PERSON>car_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_Hicar;
    cui_Hicar = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_<PERSON>car, 876);
    lv_obj_set_height(cui_<PERSON>car, 476);
    lv_obj_set_x(cui_<PERSON>car, 48);
    lv_obj_set_y(cui_<PERSON>car, 40);
    lv_obj_set_align(cui_Hicar, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_<PERSON>car, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_<PERSON>car, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_Hicar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(cui_Hicar, &ui_img_dark_bg_dashboard_main_png, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_hicar_warp;
    cui_hicar_warp = lv_obj_create(cui_Hicar);
    lv_obj_set_width(cui_hicar_warp, lv_pct(100));
    lv_obj_set_height(cui_hicar_warp, lv_pct(100));
    lv_obj_set_align(cui_hicar_warp, LV_ALIGN_CENTER);
    lv_obj_set_style_bg_color(cui_hicar_warp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_hicar_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_hicar_warp, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_hicar_warp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_hicar_warp, 26, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_hicar_warp, 25, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_connected_status;
    cui_connected_status = lv_obj_create(cui_Hicar);
    lv_obj_set_width(cui_connected_status, 652);
    lv_obj_set_height(cui_connected_status, 385);
    lv_obj_set_align(cui_connected_status, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_connected_status, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_connected_status, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_connected_status, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_connected_status, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_connected_status, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_connected_status, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_connected_status, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_connected_status, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_connected_status, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_status_icon;
    cui_status_icon = lv_img_create(cui_connected_status);
    lv_img_set_src(cui_status_icon, &ui_img_setting_ic_wifi_big_png);
    lv_obj_set_width(cui_status_icon, LV_SIZE_CONTENT);   /// 180
    lv_obj_set_height(cui_status_icon, LV_SIZE_CONTENT);    /// 180
    lv_obj_set_align(cui_status_icon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_status_icon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_status_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_status_label;
    cui_status_label = lv_obj_create(cui_connected_status);
    lv_obj_set_height(cui_status_label, 120);
    lv_obj_set_width(cui_status_label, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(cui_status_label, LV_ALIGN_CENTER);
    lv_obj_clear_flag(cui_status_label, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_status_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_status_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_status_label_val;
    cui_status_label_val = lv_label_create(cui_status_label);
    lv_obj_set_width(cui_status_label_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_status_label_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_status_label_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_status_label_val, "#HICAR_NOT_CONNECT");
    lv_obj_set_style_text_color(cui_status_label_val, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_status_label_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_status_label_val, &lv_font_montserrat_28, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_status_sub_label;
    cui_status_sub_label = lv_obj_create(cui_connected_status);
    lv_obj_set_width(cui_status_sub_label, 213);
    lv_obj_set_height(cui_status_sub_label, 50);
    lv_obj_set_align(cui_status_sub_label, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_status_sub_label, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(cui_status_sub_label, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_status_sub_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_status_sub_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_status_sub_label_val;
    cui_status_sub_label_val = lv_label_create(cui_status_sub_label);
    lv_obj_set_width(cui_status_sub_label_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_status_sub_label_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_status_sub_label_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_status_sub_label_val, "TextTextText");
    lv_obj_set_style_text_color(cui_status_sub_label_val, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_status_sub_label_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_HICAR_NUM);
    children[UI_COMP_HICAR_HICAR] = cui_Hicar;
    children[UI_COMP_HICAR_HICAR_WARP] = cui_hicar_warp;
    children[UI_COMP_HICAR_CONNECTED_STATUS] = cui_connected_status;
    children[UI_COMP_HICAR_CONNECTED_STATUS_STATUS_ICON] = cui_status_icon;
    children[UI_COMP_HICAR_CONNECTED_STATUS_STATUS_LABEL] = cui_status_label;
    children[UI_COMP_HICAR_CONNECTED_STATUS_STATUS_LABEL_STATUS_LABEL_VAL] = cui_status_label_val;
    children[UI_COMP_HICAR_CONNECTED_STATUS_STATUS_SUB_LABEL] = cui_status_sub_label;
    children[UI_COMP_HICAR_CONNECTED_STATUS_STATUS_SUB_LABEL_STATUS_SUB_LABEL_VAL] = cui_status_sub_label_val;
    lv_obj_add_event_cb(cui_Hicar, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_Hicar, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_Hicar_create_hook(cui_Hicar);
    return cui_Hicar;
}

