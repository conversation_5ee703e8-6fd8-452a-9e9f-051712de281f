//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SPACELINE_H
#define _UI_COMP_SPACELINE_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT Spaceline
#define UI_COMP_SPACELINE_SPACELINE 0
#define UI_COMP_SPACELINE_PANEL3 1
#define UI_COMP_SPACELINE_PANEL2 2
#define _UI_COMP_SPACELINE_NUM 3
lv_obj_t * ui_Spaceline_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
