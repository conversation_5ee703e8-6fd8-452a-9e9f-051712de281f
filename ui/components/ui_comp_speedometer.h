////////////////////////////////////////////////////
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SPEEDOMETER_H
#define _UI_COMP_SPEEDOMETER_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT Speedometer
#define UI_COMP_SPEEDOMETER_SPEEDOMETER 0
#define UI_COMP_SPEEDOMETER_CENTER 1
#define UI_COMP_SPEEDOMETER_CENTER_LEFT 2
#define UI_COMP_SPEEDOMETER_CENTER_LEFT_TURN_LEFT 3
#define UI_COMP_SPEEDOMETER_CENTER_RIGHT 4
#define UI_COMP_SPEEDOMETER_CENTER_RIGHT_TURN_RIGHT 5
#define UI_COMP_SPEEDOMETER_CENTER_SPEND 6
#define UI_COMP_SPEEDOMETER_CENTER_SPEND_SPEED_LABEL 7
#define UI_COMP_SPEEDOMETER_CENTER_UNIT 8
#define UI_COMP_SPEEDOMETER_CENTER_UNIT_UNIT_LABEL 9
#define UI_COMP_SPEEDOMETER_ICON_L 10
#define UI_COMP_SPEEDOMETER_ICON_R 11
#define _UI_COMP_SPEEDOMETER_NUM 12
lv_obj_t * ui_Speedometer_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
