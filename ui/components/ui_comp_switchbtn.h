//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SWITCHBTN_H
#define _UI_COMP_SWITCHBTN_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT SwitchBtn
#define UI_COMP_SWITCHBTN_SWITCHBTN 0
#define _UI_COMP_SWITCHBTN_NUM 1
lv_obj_t * ui_SwitchBtn_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
