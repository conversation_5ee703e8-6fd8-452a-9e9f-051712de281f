//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_SETTINGVOLUME_H
#define _UI_COMP_SETTINGVOLUME_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT SettingVolume
#define UI_COMP_SETTINGVOLUME_SETTINGVOLUME 0
#define UI_COMP_SETTINGVOLUME_ITEM_ICON 1
#define UI_COMP_SETTINGVOLUME_ITEM_WARP 2
#define UI_COMP_SETTINGVOLUME_ITEM_WARP_ITEM_LEFT 3
#define UI_COMP_SETTINGVOLUME_ITEM_WARP_ITEM_LEFT_SLIDERVOLUME 4
#define UI_COMP_SETTINGVOLUME_ITEM_WARP_ITEM_RIGHT 5
#define UI_COMP_SETTINGVOLUME_ITEM_WARP_ITEM_RIGHT_VOLUME_VAL 6
#define _UI_COMP_SETTINGVOLUME_NUM 7
lv_obj_t * ui_SettingVolume_create(lv_obj_t * comp_parent);
void ui_event_comp_SettingVolume_SliderVolume(lv_event_t * e);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
