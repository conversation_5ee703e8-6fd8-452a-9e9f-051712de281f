//////////////////////////////////////////////////// 
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT SidebarBtn

lv_obj_t * ui_SidebarBtn_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_SidebarBtn;
    cui_SidebarBtn = lv_btn_create(comp_parent);
    lv_obj_set_width(cui_SidebarBtn, 80);
    lv_obj_set_height(cui_SidebarBtn, 80);
    lv_obj_set_x(cui_SidebarBtn, 86);
    lv_obj_set_y(cui_SidebarBtn, 214);
    lv_obj_set_align(cui_SidebarBtn, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_SidebarBtn, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_SidebarBtn, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_flag(cui_SidebarBtn, LV_OBJ_FLAG_SCROLL_ON_FOCUS);     /// Flags
    lv_obj_clear_flag(cui_SidebarBtn, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(cui_SidebarBtn, 24, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SidebarBtn, lv_color_hex(0x1A1A1A), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_SidebarBtn, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(cui_SidebarBtn, lv_color_hex(0x021D40), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_opa(cui_SidebarBtn, 0, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_img_src(cui_SidebarBtn, &ui_img_dark_nav_btn_active_png, LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_set_style_bg_color(cui_SidebarBtn, lv_color_hex(0x021D40), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_opa(cui_SidebarBtn, 0, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(cui_SidebarBtn, &ui_img_dark_nav_btn_active_png, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_color(cui_SidebarBtn, lv_color_hex(0x1A1A1A), LV_PART_MAIN | LV_STATE_DISABLED);
    lv_obj_set_style_bg_opa(cui_SidebarBtn, 255, LV_PART_MAIN | LV_STATE_DISABLED);

    lv_obj_t * cui_BigNavBtnIcon;
    cui_BigNavBtnIcon = lv_img_create(cui_SidebarBtn);
    lv_img_set_src(cui_BigNavBtnIcon, &ui_img_dark_ic_home_png);
    lv_obj_set_width(cui_BigNavBtnIcon, 30);
    lv_obj_set_height(cui_BigNavBtnIcon, 30);
    lv_obj_set_align(cui_BigNavBtnIcon, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_BigNavBtnIcon, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_BigNavBtnIcon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_BigNavBtnLabel;
    cui_BigNavBtnLabel = lv_label_create(cui_SidebarBtn);
    lv_obj_set_width(cui_BigNavBtnLabel, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_BigNavBtnLabel, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_BigNavBtnLabel, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(cui_BigNavBtnLabel, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_BigNavBtnLabel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_BigNavBtnLabel, &ui_font_AlibabaPuHui15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_BigNavBtnLabel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_BigNavBtnLabel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_BigNavBtnLabel, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_BigNavBtnLabel, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_SIDEBARBTN_NUM);
    children[UI_COMP_SIDEBARBTN_SIDEBARBTN] = cui_SidebarBtn;
    children[UI_COMP_SIDEBARBTN_BIGNAVBTNICON] = cui_BigNavBtnIcon;
    children[UI_COMP_SIDEBARBTN_BIGNAVBTNLABEL] = cui_BigNavBtnLabel;
    lv_obj_add_event_cb(cui_SidebarBtn, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_SidebarBtn, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_SidebarBtn_create_hook(cui_SidebarBtn);
    return cui_SidebarBtn;
}

