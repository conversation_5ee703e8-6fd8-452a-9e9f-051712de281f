////////////////////////////////////////////////////
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#include "../ui.h"

// COMPONENT ListEmpty

lv_obj_t * ui_ListEmpty_create(lv_obj_t * comp_parent)
{

    lv_obj_t * cui_ListEmpty;
    cui_ListEmpty = lv_obj_create(comp_parent);
    lv_obj_set_width(cui_ListEmpty, lv_pct(100));
    lv_obj_set_height(cui_ListEmpty, lv_pct(100));
    lv_obj_set_align(cui_ListEmpty, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_ListEmpty, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(cui_ListEmpty, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_ListEmpty, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_ListEmpty, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_ListEmpty, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_icon;
    cui_icon = lv_obj_create(cui_ListEmpty);
    lv_obj_set_width(cui_icon, 75);
    lv_obj_set_height(cui_icon, 75);
    lv_obj_set_align(cui_icon, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_icon, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(cui_icon, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_icon, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_icon, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_icon, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_image;
    cui_image = lv_img_create(cui_icon);
    lv_obj_set_width(cui_image, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_image, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_image, LV_ALIGN_CENTER);
    lv_obj_add_flag(cui_image, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(cui_image, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    lv_obj_t * cui_label;
    cui_label = lv_obj_create(cui_ListEmpty);
    lv_obj_set_height(cui_label, 36);
    lv_obj_set_width(cui_label, lv_pct(100));
    lv_obj_set_align(cui_label, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_label, LV_FLEX_FLOW_COLUMN_WRAP);
    lv_obj_set_flex_align(cui_label, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_label, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_label, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_label, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_label, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_label, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_label, 20, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_label, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_label, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_label, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_label, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_label_val;
    cui_label_val = lv_label_create(cui_label);
    lv_obj_set_width(cui_label_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_label_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_label_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_label_val, "#BLUETOOTH_CONNECT_SUCCESS");
    lv_obj_set_style_text_color(cui_label_val, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_label_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(cui_label_val, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t * cui_sub;
    cui_sub = lv_obj_create(cui_ListEmpty);
    lv_obj_set_height(cui_sub, 28);
    lv_obj_set_width(cui_sub, lv_pct(100));
    lv_obj_set_align(cui_sub, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(cui_sub, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(cui_sub, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(cui_sub, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(cui_sub, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(cui_sub, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(cui_sub, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_sub, 25, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_sub, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_sub, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_pad_left(cui_sub, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(cui_sub, 0, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(cui_sub, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(cui_sub, 10, LV_PART_SCROLLBAR | LV_STATE_DEFAULT);

    lv_obj_t * cui_sub_val;
    cui_sub_val = lv_label_create(cui_sub);
    lv_obj_set_width(cui_sub_val, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(cui_sub_val, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(cui_sub_val, LV_ALIGN_CENTER);
    lv_label_set_text(cui_sub_val, "#RETURN");
    lv_obj_set_style_text_color(cui_sub_val, lv_color_hex(0xC3C3C3), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(cui_sub_val, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(cui_sub_val, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(cui_sub_val, &lv_font_montserrat_12, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_t ** children = lv_mem_alloc(sizeof(lv_obj_t *) * _UI_COMP_LISTEMPTY_NUM);
    children[UI_COMP_LISTEMPTY_LISTEMPTY] = cui_ListEmpty;
    children[UI_COMP_LISTEMPTY_ICON] = cui_icon;
    children[UI_COMP_LISTEMPTY_ICON_IMAGE] = cui_image;
    children[UI_COMP_LISTEMPTY_LABEL] = cui_label;
    children[UI_COMP_LISTEMPTY_LABEL_LABEL_VAL] = cui_label_val;
    children[UI_COMP_LISTEMPTY_SUB] = cui_sub;
    children[UI_COMP_LISTEMPTY_SUB_SUB_VAL] = cui_sub_val;
    lv_obj_add_event_cb(cui_ListEmpty, get_component_child_event_cb, LV_EVENT_GET_COMP_CHILD, children);
    lv_obj_add_event_cb(cui_ListEmpty, del_component_child_event_cb, LV_EVENT_DELETE, children);
    ui_comp_ListEmpty_create_hook(cui_ListEmpty);
    return cui_ListEmpty;
}

