////////////////////////////////////////////////////
// LVGL version: 8.3.11
// Project name: prj_ebike_x1

#ifndef _UI_COMP_DEVICEITEM_H
#define _UI_COMP_DEVICEITEM_H

#include "../ui.h"

#ifdef __cplusplus
extern "C" {
#endif

// COMPONENT DeviceItem
#define UI_COMP_DEVICEITEM_DEVICEITEM 0
#define UI_COMP_DEVICEITEM_LEFT 1
#define UI_COMP_DEVICEITEM_LEFT_ICON 2
#define UI_COMP_DEVICEITEM_LEFT_ICON_IMAGE 3
#define UI_COMP_DEVICEITEM_LEFT_LABEL 4
#define UI_COMP_DEVICEITEM_RIGHT 5
#define UI_COMP_DEVICEITEM_RIGHT_GROUP 6
#define UI_COMP_DEVICEITEM_RIGHT_GROUP_DESC 7
#define UI_COMP_DEVICEITEM_RIGHT_GROUP_ICON1 8
#define UI_COMP_DEVICEITEM_RIGHT_GROUP_ICON1_IMAGE1 9
#define UI_COMP_DEVICEITEM_RIGHT_GROUP_ICON2 10
#define UI_COMP_DEVICEITEM_RIGHT_GROUP_ICON2_IMAGE2 11
#define _UI_COMP_DEVICEITEM_NUM 12
lv_obj_t * ui_DeviceItem_create(lv_obj_t * comp_parent);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
