/**
 * @file app_adapter.c
 * @brief 适配器管理实现
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include "../include/app_adapter.h"
#include "../include/app_event.h"

// 适配器列表
static app_adapter_t *s_adapters[16];
static int s_adapter_count = 0;

// 初始化适配器管理
void app_adapter_manager_init(void) {
    s_adapter_count = 0;
    memset(s_adapters, 0, sizeof(s_adapters));
}

// 注册适配器
int app_adapter_register(app_adapter_t *adapter) {
    if (!adapter || s_adapter_count >= sizeof(s_adapters)/sizeof(s_adapters[0])) {
        return -1;
    }
    
    // 检查是否已注册
    for (int i = 0; i < s_adapter_count; i++) {
        if (s_adapters[i] == adapter) {
            return 0;  // 已注册
        }
    }
    
    // 添加到列表
    s_adapters[s_adapter_count++] = adapter;
    
    // 初始化适配器
    if (adapter->init) {
        adapter->init();
    }
    
    return 0;
}

// 初始化所有适配器
int app_adapter_init_all(void) {
    for (int i = 0; i < s_adapter_count; i++) {
        app_adapter_t *adapter = s_adapters[i];
        if (adapter->init && adapter->init() != 0) {
            // 初始化失败，回滚已初始化的适配器
            for (int j = 0; j < i; j++) {
                if (s_adapters[j]->deinit) {
                    s_adapters[j]->deinit();
                }
            }
            return -1;
        }
    }
    return 0;
}

// 注销适配器
int app_adapter_unregister(app_adapter_t *adapter) {
    if (!adapter) {
        return -1;
    }
    
    // 查找适配器
    for (int i = 0; i < s_adapter_count; i++) {
        if (s_adapters[i] == adapter) {
            // 清理适配器
            if (adapter->deinit) {
                adapter->deinit();
            }
            
            // 从列表中移除
            if (i < s_adapter_count - 1) {
                memmove(&s_adapters[i], &s_adapters[i+1], 
                        (s_adapter_count - i - 1) * sizeof(app_adapter_t*));
            }
            s_adapter_count--;
            return 0;
        }
    }
    
    return -1;
}

// 处理所有适配器
void app_adapter_process_all(void) {
    for (int i = 0; i < s_adapter_count; i++) {
        if (s_adapters[i]->process) {
            s_adapters[i]->process();
        }
    }
}

// 清理所有适配器
void app_adapter_deinit_all(void) {
    for (int i = 0; i < s_adapter_count; i++) {
        if (s_adapters[i]->deinit) {
            s_adapters[i]->deinit();
        }
    }
    s_adapter_count = 0;
} 