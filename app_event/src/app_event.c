/**
 * @file app_event.c
 * @brief 事件处理系统实现
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <sys/time.h>
#include "../include/app_event.h"
#include "../include/app_system.h"

// 事件队列大小
#define EVENT_QUEUE_SIZE 16

// 事件处理器结构体
typedef struct {
    app_event_type_t type;         // 事件类型
    app_event_handler_t handler;   // 处理函数
    void *user_data;               // 用户数据
} event_handler_t;

// 事件队列结构体
typedef struct {
    app_event_t events[EVENT_QUEUE_SIZE];  // 事件数组
    int head;                              // 队列头
    int tail;                              // 队列尾
    int size;                              // 队列大小
} event_queue_t;

// 事件处理器数组
static event_handler_t s_handlers[32];
static int s_handler_count = 0;

// 事件队列
static event_queue_t s_event_queue;

// 初始化事件系统
int app_event_init(void) {
    // 初始化事件处理器数组
    memset(s_handlers, 0, sizeof(s_handlers));
    s_handler_count = 0;
    
    // 初始化事件队列
    memset(&s_event_queue, 0, sizeof(s_event_queue));
    s_event_queue.head = 0;
    s_event_queue.tail = 0;
    s_event_queue.size = 0;
    
    return 0;
}

// 注册事件处理器
int app_event_register(app_event_type_t type, app_event_handler_t handler, void *user_data) {
    if (!handler || s_handler_count >= sizeof(s_handlers)/sizeof(s_handlers[0])) {
        return -1;
    }
    
    // 检查是否已经注册
    for (int i = 0; i < s_handler_count; i++) {
        if (s_handlers[i].type == type && s_handlers[i].handler == handler) {
            // 更新用户数据
            s_handlers[i].user_data = user_data;
            return 0;
        }
    }
    
    // 添加新的处理器
    s_handlers[s_handler_count].type = type;
    s_handlers[s_handler_count].handler = handler;
    s_handlers[s_handler_count].user_data = user_data;
    s_handler_count++;
    
    return 0;
}

// app_event_register_handler作为app_event_register的别名
int app_event_register_handler(app_event_type_t type, app_event_handler_t handler, void *user_data) {
    return app_event_register(type, handler, user_data);
}

// 取消注册事件处理器
int app_event_unregister(app_event_type_t type, app_event_handler_t handler) {
    for (int i = 0; i < s_handler_count; i++) {
        if (s_handlers[i].type == type && s_handlers[i].handler == handler) {
            // 移除处理器
            if (i < s_handler_count - 1) {
                memmove(&s_handlers[i], &s_handlers[i+1], 
                        (s_handler_count - i - 1) * sizeof(event_handler_t));
            }
            s_handler_count--;
            return 0;
        }
    }
    
    return -1;
}

// 发布事件
int app_event_post(app_event_t *event) {
    if (!event) {
        return -1;
    }
    
    // 检查队列是否已满
    if (s_event_queue.size >= EVENT_QUEUE_SIZE) {
        return -1;
    }
    
    // 添加事件到队列
    memcpy(&s_event_queue.events[s_event_queue.tail], event, sizeof(app_event_t));
    s_event_queue.tail = (s_event_queue.tail + 1) % EVENT_QUEUE_SIZE;
    s_event_queue.size++;
    
    return 0;
}

// 同步发布事件
int app_event_post_sync(app_event_t *event) {
    if (!event) {
        return -1;
    }
    
    // 直接处理事件
    for (int i = 0; i < s_handler_count; i++) {
        if (s_handlers[i].type == event->type) {
            s_handlers[i].handler(event, s_handlers[i].user_data);
        }
    }
    
    return 0;
}

// 处理事件队列
void app_event_process(void) {
    // 处理队列中的所有事件
    while (s_event_queue.size > 0) {
        // 获取队列头部事件
        app_event_t *event = &s_event_queue.events[s_event_queue.head];
        
        // 调用对应的处理器
        for (int i = 0; i < s_handler_count; i++) {
            if (s_handlers[i].type == event->type) {
                s_handlers[i].handler(event, s_handlers[i].user_data);
            }
        }
        
        // 移除已处理的事件
        s_event_queue.head = (s_event_queue.head + 1) % EVENT_QUEUE_SIZE;
        s_event_queue.size--;
    }
}

// 清理事件系统资源
void app_event_deinit(void) {
    // 清空事件处理器
    memset(s_handlers, 0, sizeof(s_handlers));
    s_handler_count = 0;
    
    // 清空事件队列
    memset(&s_event_queue, 0, sizeof(s_event_queue));
} 