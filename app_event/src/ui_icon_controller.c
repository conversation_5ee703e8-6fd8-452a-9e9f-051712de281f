/**
 * @file ui_icon_controller.c
 * @brief UI图标控制器实现，用于管理图标闪烁、定时显示等功能
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../include/ui_icon_controller.h"
#include "../include/app_system.h"

// 最大图标控制器数量
#define MAX_ICON_CONTROLLERS 32

// 全局图标控制器列表
static icon_controller_t *g_icon_controllers[MAX_ICON_CONTROLLERS] = {0};
static uint8_t g_icon_controller_count = 0;

// 预定义闪烁模式
const icon_blink_pattern_t BLINK_PATTERN_SLOW = {
    .on_time = 500,        // 500ms亮
    .off_time = 500,       // 500ms灭
    .blink_count = 0,      // 持续闪烁
    .group_interval = 0,   // 无组间隔
    .total_time = 0        // 无限时间
};

const icon_blink_pattern_t BLINK_PATTERN_FAST = {
    .on_time = 250,        // 250ms亮
    .off_time = 250,       // 250ms灭
    .blink_count = 0,      // 持续闪烁
    .group_interval = 0,   // 无组间隔
    .total_time = 0        // 无限时间
};

const icon_blink_pattern_t BLINK_PATTERN_DOUBLE = {
    .on_time = 300,        // 300ms亮
    .off_time = 300,       // 300ms灭
    .blink_count = 2,      // 闪烁两次
    .group_interval = 1000,// 1000ms组间隔
    .total_time = 0        // 无限时间
};

const icon_blink_pattern_t BLINK_PATTERN_WARNING = {
    .on_time = 100,        // 100ms亮
    .off_time = 100,       // 100ms灭
    .blink_count = 3,      // 闪烁3次
    .group_interval = 2000,// 2000ms组间隔
    .total_time = 0        // 无限时间
};

/**
 * @brief 初始化图标控制器系统
 */
int icon_controller_system_init(void) {
    // 初始化图标控制器列表
    memset(g_icon_controllers, 0, sizeof(g_icon_controllers));
    g_icon_controller_count = 0;
    
    return 0;
}

/**
 * @brief 创建图标控制器
 */
icon_controller_t* icon_controller_create(lv_obj_t *icon) {
    if (!icon || g_icon_controller_count >= MAX_ICON_CONTROLLERS) {
        return NULL;
    }
    
    // 分配内存
    icon_controller_t *controller = (icon_controller_t *)malloc(sizeof(icon_controller_t));
    if (!controller) {
        return NULL;
    }
    
    // 初始化控制器
    memset(controller, 0, sizeof(icon_controller_t));
    controller->icon = icon;
    controller->state = ICON_STATE_OFF;
    controller->is_visible = true;
    controller->start_time = app_get_time();
    controller->last_toggle = controller->start_time;
    
    // 默认闪烁模式 (快速闪烁)
    controller->pattern.on_time = 250;
    controller->pattern.off_time = 250;
    controller->pattern.blink_count = 0;
    controller->pattern.group_interval = 0;
    controller->pattern.total_time = 0;
    
    // 添加到控制器列表
    g_icon_controllers[g_icon_controller_count++] = controller;
    
    return controller;
}

/**
 * @brief 删除图标控制器
 */
void icon_controller_delete(icon_controller_t *controller) {
    if (!controller) {
        return;
    }
    
    // 从控制器列表中移除
    for (int i = 0; i < g_icon_controller_count; i++) {
        if (g_icon_controllers[i] == controller) {
            // 移除控制器
            if (i < g_icon_controller_count - 1) {
                memmove(&g_icon_controllers[i], &g_icon_controllers[i+1], 
                        (g_icon_controller_count - i - 1) * sizeof(icon_controller_t*));
            }
            g_icon_controller_count--;
            break;
        }
    }
    
    // 释放内存
    free(controller);
}

/**
 * @brief 设置图标状态
 */
int icon_set_state(icon_controller_t *controller, icon_state_t state) {
    if (!controller) {
        return -1;
    }
    
    controller->state = state;
    controller->start_time = app_get_time();
    controller->last_toggle = controller->start_time;
    controller->blinks_done = 0;
    
    // 根据状态设置预定义闪烁模式
    switch (state) {
        case ICON_STATE_OFF:
            // 关闭图标
            lv_obj_add_flag(controller->icon, LV_OBJ_FLAG_HIDDEN);
            controller->is_visible = false;
            break;
            
        case ICON_STATE_ON:
            // 显示图标
            lv_obj_clear_flag(controller->icon, LV_OBJ_FLAG_HIDDEN);
            controller->is_visible = true;
            break;
            
        case ICON_STATE_BLINK_SLOW:
            // 慢闪
            memcpy(&controller->pattern, &BLINK_PATTERN_SLOW, sizeof(icon_blink_pattern_t));
            lv_obj_clear_flag(controller->icon, LV_OBJ_FLAG_HIDDEN);
            controller->is_visible = true;
            break;
            
        case ICON_STATE_BLINK_FAST:
            // 快闪
            memcpy(&controller->pattern, &BLINK_PATTERN_FAST, sizeof(icon_blink_pattern_t));
            lv_obj_clear_flag(controller->icon, LV_OBJ_FLAG_HIDDEN);
            controller->is_visible = true;
            break;
            
        case ICON_STATE_DOUBLE_BLINK:
            // 双闪
            memcpy(&controller->pattern, &BLINK_PATTERN_DOUBLE, sizeof(icon_blink_pattern_t));
            lv_obj_clear_flag(controller->icon, LV_OBJ_FLAG_HIDDEN);
            controller->is_visible = true;
            break;
            
        case ICON_STATE_PULSE:
            // 脉冲闪烁
            memcpy(&controller->pattern, &BLINK_PATTERN_WARNING, sizeof(icon_blink_pattern_t));
            lv_obj_clear_flag(controller->icon, LV_OBJ_FLAG_HIDDEN);
            controller->is_visible = true;
            break;
            
        case ICON_STATE_CUSTOM:
            // 自定义模式，不做任何改变
            break;
            
        default:
            return -1;
    }
    
    return 0;
}

/**
 * @brief 设置自定义闪烁模式
 */
int icon_set_custom_pattern(icon_controller_t *controller, icon_blink_pattern_t *pattern) {
    if (!controller || !pattern) {
        return -1;
    }
    
    // 拷贝闪烁模式
    memcpy(&controller->pattern, pattern, sizeof(icon_blink_pattern_t));
    controller->state = ICON_STATE_CUSTOM;
    controller->start_time = app_get_time();
    controller->last_toggle = controller->start_time;
    controller->blinks_done = 0;
    
    // 确保图标可见
    lv_obj_clear_flag(controller->icon, LV_OBJ_FLAG_HIDDEN);
    controller->is_visible = true;
    
    return 0;
}

/**
 * @brief 设置图标定时关闭
 */
int icon_set_timeout(icon_controller_t *controller, uint32_t timeout_ms) {
    if (!controller) {
        return -1;
    }
    
    // 设置超时时间
    controller->timeout = timeout_ms;
    controller->start_time = app_get_time(); // 重置开始时间
    
    return 0;
}

/**
 * @brief 图标控制器更新函数
 */
void icon_controller_update(void) {
    uint32_t current_time = app_get_time();
    uint32_t elapsed;
    
    for (int i = 0; i < g_icon_controller_count; i++) {
        icon_controller_t *ctrl = g_icon_controllers[i];
        if (!ctrl) continue;
        
        // 检查超时
        if (ctrl->timeout > 0 && 
            (current_time - ctrl->start_time) >= ctrl->timeout) {
            // 超时自动关闭
            lv_obj_add_flag(ctrl->icon, LV_OBJ_FLAG_HIDDEN);
            ctrl->is_visible = false;
            ctrl->state = ICON_STATE_OFF;
            ctrl->timeout = 0; // 清除超时设置
            continue;
        }
        
        // 检查总时间是否已到
        if (ctrl->pattern.total_time > 0 && 
            (current_time - ctrl->start_time) >= ctrl->pattern.total_time) {
            // 总时间已到，关闭图标
            lv_obj_add_flag(ctrl->icon, LV_OBJ_FLAG_HIDDEN);
            ctrl->is_visible = false;
            ctrl->state = ICON_STATE_OFF;
            continue;
        }
        
        // 根据不同状态处理
        switch (ctrl->state) {
            case ICON_STATE_OFF:
                if (ctrl->is_visible) {
                    lv_obj_add_flag(ctrl->icon, LV_OBJ_FLAG_HIDDEN);
                    ctrl->is_visible = false;
                }
                break;
                
            case ICON_STATE_ON:
                if (!ctrl->is_visible) {
                    lv_obj_clear_flag(ctrl->icon, LV_OBJ_FLAG_HIDDEN);
                    ctrl->is_visible = true;
                }
                break;
                
            case ICON_STATE_BLINK_SLOW:
            case ICON_STATE_BLINK_FAST:
            case ICON_STATE_DOUBLE_BLINK:
            case ICON_STATE_PULSE:
            case ICON_STATE_CUSTOM:
                // 闪烁处理逻辑
                elapsed = current_time - ctrl->last_toggle;
                
                // 当前应该处于ON还是OFF状态
                if (ctrl->is_visible) {
                    // 当前可见，检查是否需要隐藏
                    if (elapsed >= ctrl->pattern.on_time) {
                        lv_obj_add_flag(ctrl->icon, LV_OBJ_FLAG_HIDDEN);
                        ctrl->is_visible = false;
                        ctrl->last_toggle = current_time;
                        
                        if (ctrl->pattern.blink_count > 0) {
                            ctrl->blinks_done++;
                            // 检查闪烁组是否完成
                            if (ctrl->blinks_done >= ctrl->pattern.blink_count) {
                                // 增加组间隔延时
                                ctrl->last_toggle += ctrl->pattern.group_interval;
                                ctrl->blinks_done = 0;
                            }
                        }
                    }
                } else {
                    // 当前隐藏，检查是否需要显示
                    if (elapsed >= ctrl->pattern.off_time) {
                        lv_obj_clear_flag(ctrl->icon, LV_OBJ_FLAG_HIDDEN);
                        ctrl->is_visible = true;
                        ctrl->last_toggle = current_time;
                    }
                }
                break;
                
            default:
                break;
        }
    }
}

/**
 * @brief 清理图标控制器系统资源
 */
void icon_controller_system_deinit(void) {
    // 释放所有控制器
    for (int i = 0; i < g_icon_controller_count; i++) {
        if (g_icon_controllers[i]) {
            free(g_icon_controllers[i]);
            g_icon_controllers[i] = NULL;
        }
    }
    
    g_icon_controller_count = 0;
} 