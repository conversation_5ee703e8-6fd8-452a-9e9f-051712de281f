/**
 * @file uart_hicar_bridge.c
 * @brief UART与HiCar之间的桥接模块实现
 * @version 1.0.0
 * @date 2025-06-18 19:32
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../include/uart_hicar_bridge.h"
#include "../include/app_uart_adapter.h"
#include "../include/ui_icon_controller.h"
#include "app/api/uart_api.h"
#include "app/utils/screen_utils.h"

// 外部变量声明
extern int g_uart_simulate_mode;

// 命令映射结构体
typedef struct {
    uint8_t uart_id;       // UART命令ID
    uint8_t uart_param;    // UART命令参数
    e_voice_ctrl_command hicar_cmd;  // 对应的HiCar命令
} cmd_mapping_t;

// UART命令到HiCar命令的映射表
static const cmd_mapping_t uart_to_hicar_map[] = {
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_LEFT_TURN_ON, VC_CMD_LTS_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_RIGHT_TURN_ON, VC_CMD_RTS_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_TURN_OFF, VC_CMD_TS_OFF},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_HEADLIGHT_ON, VC_CMD_LIGHT_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_TURN_OFF, VC_CMD_LIGHT_OFF},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_DOUBLE_FLASH_ON, VC_CMD_FLASH_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_DOUBLE_FLASH_OFF, VC_CMD_FLASH_OFF},
    {UART_MSG_LIGHT_SCREEN_CTRL, SCREEN_CTRL_ON, VC_CMD_SCREEN_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, SCREEN_CTRL_OFF, VC_CMD_SCREEN_OFF},
    {0xFF, 0xFF, 0xFF}  // 使用特殊值作为结束标记
};

// HiCar命令到UART命令的映射表
static const cmd_mapping_t hicar_to_uart_map[] = {
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_LEFT_TURN_ON, VC_CMD_LTS_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_RIGHT_TURN_ON, VC_CMD_RTS_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_TURN_OFF, VC_CMD_TS_OFF},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_HEADLIGHT_ON, VC_CMD_LIGHT_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_TURN_OFF, VC_CMD_LIGHT_OFF},  // 使用通用的关闭命令来关闭大灯
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_DOUBLE_FLASH_ON, VC_CMD_FLASH_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_DOUBLE_FLASH_OFF, VC_CMD_FLASH_OFF},
    {UART_MSG_LIGHT_SCREEN_CTRL, SCREEN_CTRL_ON, VC_CMD_SCREEN_ON},
    {UART_MSG_LIGHT_SCREEN_CTRL, SCREEN_CTRL_OFF, VC_CMD_SCREEN_OFF},
    {0xFF, 0xFF, 0xFF}  // 使用特殊值作为结束标记
};

// UI图标对象
static lv_obj_t *g_left_turn_icon = NULL;
static lv_obj_t *g_right_turn_icon = NULL;
static lv_obj_t *g_headlight_icon = NULL;
static lv_obj_t *g_screen_icon = NULL;
static lv_obj_t *g_flash_icon = NULL;  // 双闪图标

// 图标控制器
static icon_controller_t *g_left_turn_controller = NULL;
static icon_controller_t *g_right_turn_controller = NULL;
static icon_controller_t *g_headlight_controller = NULL;
static icon_controller_t *g_screen_controller = NULL;
static icon_controller_t *g_flash_controller = NULL;  // 双闪图标控制器

// 状态变量
static uint8_t g_left_turn_state = 0;
static uint8_t g_right_turn_state = 0;
static uint8_t g_headlight_state = 0;
static uint8_t g_screen_state = 0;
static uint8_t g_flash_state = 0;  // 双闪状态

/**
 * @brief 更新UI图标状态
 * @param cmd HiCar命令
 */
static void update_ui_icons(e_voice_ctrl_command cmd) {
    switch (cmd) {
        case VC_CMD_LTS_ON:
            if (g_left_turn_controller) {
                icon_set_state(g_left_turn_controller, ICON_STATE_BLINK_FAST);
                g_left_turn_state = 1;
            }
            break;
            
        case VC_CMD_RTS_ON:
            if (g_right_turn_controller) {
                icon_set_state(g_right_turn_controller, ICON_STATE_BLINK_FAST);
                g_right_turn_state = 1;
            }
            break;
            
        case VC_CMD_TS_OFF:
            if (g_left_turn_controller) {
                icon_set_state(g_left_turn_controller, ICON_STATE_OFF);
                g_left_turn_state = 0;
            }
            if (g_right_turn_controller) {
                icon_set_state(g_right_turn_controller, ICON_STATE_OFF);
                g_right_turn_state = 0;
            }
            break;
            
        case VC_CMD_LIGHT_ON:
            if (g_headlight_controller) {
                icon_set_state(g_headlight_controller, ICON_STATE_ON);
                g_headlight_state = 1;
            }
            break;
            
        case VC_CMD_LIGHT_OFF:
            if (g_headlight_controller) {
                icon_set_state(g_headlight_controller, ICON_STATE_OFF);
                g_headlight_state = 0;
            }
            break;
            
        case VC_CMD_SCREEN_ON:
            if (g_screen_controller) {
                icon_set_state(g_screen_controller, ICON_STATE_ON);
                g_screen_state = 1;
            }
            break;
            
        case VC_CMD_SCREEN_OFF:
            if (g_screen_controller) {
                icon_set_state(g_screen_controller, ICON_STATE_OFF);
                g_screen_state = 0;
            }
            break;
            
        case VC_CMD_FLASH_ON:
            if (g_flash_controller) {
                icon_set_state(g_flash_controller, ICON_STATE_BLINK_FAST);
                g_flash_state = 1;
            }
            // 双闪开启时，左右转向灯也同时闪烁
            if (g_left_turn_controller && g_right_turn_controller) {
                icon_set_state(g_left_turn_controller, ICON_STATE_BLINK_FAST);
                icon_set_state(g_right_turn_controller, ICON_STATE_BLINK_FAST);
                g_left_turn_state = g_right_turn_state = 1;
            }
            break;
            
        case VC_CMD_FLASH_OFF:
            if (g_flash_controller) {
                icon_set_state(g_flash_controller, ICON_STATE_OFF);
                g_flash_state = 0;
            }
            // 双闪关闭时，左右转向灯也同时关闭
            if (g_left_turn_controller && g_right_turn_controller) {
                icon_set_state(g_left_turn_controller, ICON_STATE_OFF);
                icon_set_state(g_right_turn_controller, ICON_STATE_OFF);
                g_left_turn_state = g_right_turn_state = 0;
            }
            break;
            
        default:
            break;
    }
}

/**
 * @brief 查找HiCar命令对应的UART命令
 * @param hicar_cmd HiCar命令
 * @param uart_id 输出UART命令ID
 * @param uart_param 输出UART命令参数
 * @return 0表示找到，-1表示未找到
 */
static int find_uart_cmd_by_hicar(e_voice_ctrl_command hicar_cmd, uint8_t *uart_id, uint8_t *uart_param) {
    for (int i = 0; hicar_to_uart_map[i].uart_id != 0xFF; i++) {
        if (hicar_to_uart_map[i].hicar_cmd == hicar_cmd) {
            *uart_id = hicar_to_uart_map[i].uart_id;
            *uart_param = hicar_to_uart_map[i].uart_param;
            return 0;
        }
    }
    return -1;
}

/**
 * @brief 查找UART命令对应的HiCar命令
 * @param uart_id UART命令ID
 * @param uart_param UART命令参数
 * @param hicar_cmd 输出HiCar命令
 * @return 0表示找到，-1表示未找到
 */
static int find_hicar_cmd_by_uart(uint8_t uart_id, uint8_t uart_param, e_voice_ctrl_command *hicar_cmd) {
    for (int i = 0; uart_to_hicar_map[i].uart_id != 0xFF; i++) {
        if (uart_to_hicar_map[i].uart_id == uart_id && 
            uart_to_hicar_map[i].uart_param == uart_param) {
            *hicar_cmd = uart_to_hicar_map[i].hicar_cmd;
            return 0;
        }
    }
    return -1;
}

/**
 * @brief UART事件处理函数
 */
static void uart_event_handler(app_event_t *event, void *user_data) {
    if (!event || !event->data) return;
    
    // 处理UART数据接收事件
    if (event->type == APP_EVENT_COMM_DATA_RECEIVED) {
        // 解析UART数据
        typedef struct {
            uint8_t id;
            uint8_t data[256];
            uint16_t data_len;
        } uart_frame_data_t;
        
        uart_frame_data_t *frame = (uart_frame_data_t *)event->data;
        if (!frame || frame->data_len < 1) return;
        
        printf("UART桥接：收到UART数据，ID=0x%02X，长度=%d\n", frame->id, frame->data_len);
        
        // 处理ACK消息
        if (frame->id == UART_MSG_ACK) {
            printf("UART桥接：收到ACK应答\n");
            return;
        }
        
        // 检查是否是灯光和屏幕控制命令(ID_LIGHT_SCREEN_CTRL = 0x56)
        if (frame->id == UART_MSG_LIGHT_SCREEN_CTRL && frame->data_len >= 1) {
            uint8_t light_ctrl = frame->data[0];
            uint8_t screen_ctrl = (frame->data_len >= 2) ? frame->data[1] : 0;
            
            // 查找对应的HiCar命令
            e_voice_ctrl_command hicar_cmd;
            
            // 先尝试匹配灯光控制命令
            if (find_hicar_cmd_by_uart(UART_MSG_LIGHT_SCREEN_CTRL, light_ctrl, &hicar_cmd) == 0) {
                printf("UART桥接：映射到HiCar命令 %d\n", hicar_cmd);
                
                // 更新UI图标
                update_ui_icons(hicar_cmd);
                
                // 触发HiCar回调
                report_car_state(hicar_cmd);
            }
            
            // 如果有屏幕控制命令，也进行处理
            if (screen_ctrl != 0) {
                if (find_hicar_cmd_by_uart(UART_MSG_LIGHT_SCREEN_CTRL, screen_ctrl, &hicar_cmd) == 0) {
                    printf("UART桥接：映射到HiCar屏幕命令 %d\n", hicar_cmd);
                    
                    // 更新UI图标
                    update_ui_icons(hicar_cmd);
                    
                    // 触发HiCar回调
                    report_car_state(hicar_cmd);
                    
                    // 执行硬件控制命令
                    if (hicar_cmd == VC_CMD_SCREEN_ON) {
                        screen_turn_on();
                        printf("[硬件控制] -> 打开屏幕\n");
                    } else if (hicar_cmd == VC_CMD_SCREEN_OFF) {
                        screen_turn_off();
                        printf("[硬件控制] -> 关闭屏幕\n");
                    }
                }
            }
        }
    }
}

/**
 * @brief HiCar事件处理函数
 */
static void hicar_event_handler(app_event_t *event, void *user_data) {
    if (!event || !event->data) return;
    
    // 处理HiCar命令事件
    if (event->type == APP_EVENT_BIZ_HICAR_COMMAND) {
        e_voice_ctrl_command *cmd = (e_voice_ctrl_command *)event->data;
        printf("UART桥接：收到HiCar命令 %d\n", *cmd);
        
        // 打印命令名称，便于调试
        const char *cmd_name = "未知";
        switch (*cmd) {
            case VC_CMD_SCREEN_ON: cmd_name = "屏幕开启"; break;
            case VC_CMD_SCREEN_OFF: cmd_name = "屏幕关闭"; break;
            case VC_CMD_LIGHT_ON: cmd_name = "大灯开启"; break;
            case VC_CMD_LIGHT_OFF: cmd_name = "大灯关闭"; break;
            case VC_CMD_FLASH_ON: cmd_name = "双闪开启"; break;
            case VC_CMD_FLASH_OFF: cmd_name = "双闪关闭"; break;
            case VC_CMD_LTS_ON: cmd_name = "左转向开启"; break;
            case VC_CMD_RTS_ON: cmd_name = "右转向开启"; break;
            case VC_CMD_TS_OFF: cmd_name = "转向关闭"; break;
        }
        printf("UART桥接：HiCar命令类型：%s\n", cmd_name);
        
        // 更新UI图标
        update_ui_icons(*cmd);
        
        // 查找对应的UART命令
        uint8_t uart_id, uart_param;
        if (find_uart_cmd_by_hicar(*cmd, &uart_id, &uart_param) == 0) {
            printf("UART桥接：映射到UART命令 ID=0x%02X, 参数=0x%02X\n", uart_id, uart_param);
            
            // 发送UART命令
            if (uart_id == UART_MSG_LIGHT_SCREEN_CTRL) {
                // 对于灯光和屏幕控制命令，需要发送一个或两个参数
                uint8_t data[2] = {uart_param, 0};
                uint8_t data_len = 1;  // 默认只发送灯光控制参数
                
                // 对于屏幕控制命令，设置第二个参数
                if (*cmd == VC_CMD_SCREEN_ON) {
                    printf("UART桥接：处理屏幕开启命令\n");
                    data[0] = 0; // 灯光控制参数为0
                    data[1] = SCREEN_CTRL_ON;
                    data_len = 2;
                } else if (*cmd == VC_CMD_SCREEN_OFF) {
                    printf("UART桥接：处理屏幕关闭命令\n");
                    data[0] = 0; // 灯光控制参数为0
                    data[1] = SCREEN_CTRL_OFF;
                    data_len = 2;
                }
                
                // 发送UART命令
                printf("发送UART命令: ID=0x%02X, 数据=[0x%02X, 0x%02X], 长度=%d\n", 
                       uart_id, data[0], data[1], data_len);
                
                int ret = app_uart_send(uart_id, 0, data, data_len);

                // 执行硬件控制命令
                printf("执行硬件控制命令\n");
                if (*cmd == VC_CMD_SCREEN_ON) {
                    screen_turn_on();
                    printf("[硬件控制] -> 打开屏幕\n");
                } else if (*cmd == VC_CMD_SCREEN_OFF) {
                    screen_turn_off();
                    printf("[硬件控制] -> 关闭屏幕\n");
                }


                if (ret < 0) {
                    printf("UART命令发送失败: %d\n", ret);
                } else {
                    printf("UART命令发送成功: %d字节\n", ret);
                }
            } else {
                // 其他类型的命令
                uint8_t data[1] = {uart_param};
                
                printf("发送UART命令: ID=0x%02X, 数据=[0x%02X], 长度=1\n", uart_id, data[0]);
                
                int ret = app_uart_send(uart_id, 0, data, 1);
                if (ret < 0) {
                    printf("UART命令发送失败: %d\n", ret);
                } else {
                    printf("UART命令发送成功: %d字节\n", ret);
                }
            }
        } else {
            printf("UART桥接：未找到对应的UART命令，HiCar命令=%d (%s)\n", *cmd, cmd_name);
        }
    }
}

/**
 * @brief 初始化UART-HiCar桥接模块
 */
int uart_hicar_bridge_init(void) {
    printf("初始化UART-HiCar桥接模块...\n");
    
    // 注册事件处理器
    if (app_event_register(APP_EVENT_COMM_DATA_RECEIVED, uart_event_handler, NULL) != 0) {
        printf("注册UART事件处理器失败\n");
        return -1;
    }
    
    if (app_event_register(APP_EVENT_BIZ_HICAR_COMMAND, hicar_event_handler, NULL) != 0) {
        printf("注册HiCar事件处理器失败\n");
        app_event_unregister(APP_EVENT_COMM_DATA_RECEIVED, uart_event_handler);
        return -1;
    }
    
    printf("UART-HiCar桥接模块初始化成功\n");
    return 0;
}

/**
 * @brief 处理UART-HiCar桥接模块的事件和逻辑
 */
void uart_hicar_bridge_process(void) {
    // 更新图标控制器
    if (g_left_turn_controller || g_right_turn_controller || 
        g_headlight_controller || g_screen_controller || g_flash_controller) {
        icon_controller_update();
    }
}

/**
 * @brief 清理UART-HiCar桥接模块的资源
 */
void uart_hicar_bridge_deinit(void) {
    printf("清理UART-HiCar桥接模块资源...\n");
    
    // 取消注册事件处理器
    app_event_unregister(APP_EVENT_COMM_DATA_RECEIVED, uart_event_handler);
    app_event_unregister(APP_EVENT_BIZ_HICAR_COMMAND, hicar_event_handler);
    
    // 清理图标控制器
    if (g_left_turn_controller) {
        icon_controller_delete(g_left_turn_controller);
        g_left_turn_controller = NULL;
    }
    
    if (g_right_turn_controller) {
        icon_controller_delete(g_right_turn_controller);
        g_right_turn_controller = NULL;
    }
    
    if (g_headlight_controller) {
        icon_controller_delete(g_headlight_controller);
        g_headlight_controller = NULL;
    }
    
    if (g_screen_controller) {
        icon_controller_delete(g_screen_controller);
        g_screen_controller = NULL;
    }
    
    if (g_flash_controller) {
        icon_controller_delete(g_flash_controller);
        g_flash_controller = NULL;
    }
    
    printf("UART-HiCar桥接模块资源已清理\n");
}

/**
 * @brief 注册UI图标，用于状态显示
 */
void uart_hicar_bridge_register_icons(lv_obj_t *left_turn_icon, 
                                     lv_obj_t *right_turn_icon,
                                     lv_obj_t *headlight_icon,
                                     lv_obj_t *screen_icon,
                                     lv_obj_t *flash_icon) {
    // 保存图标对象
    g_left_turn_icon = left_turn_icon;
    g_right_turn_icon = right_turn_icon;
    g_headlight_icon = headlight_icon;
    g_screen_icon = screen_icon;
    g_flash_icon = flash_icon;
    
    // 创建图标控制器
    if (g_left_turn_icon) {
        g_left_turn_controller = icon_controller_create(g_left_turn_icon);
    }
    
    if (g_right_turn_icon) {
        g_right_turn_controller = icon_controller_create(g_right_turn_icon);
    }
    
    if (g_headlight_icon) {
        g_headlight_controller = icon_controller_create(g_headlight_icon);
    }
    
    if (g_screen_icon) {
        g_screen_controller = icon_controller_create(g_screen_icon);
    }
    
    if (g_flash_icon) {
        g_flash_controller = icon_controller_create(g_flash_icon);
    }
    
    printf("UART-HiCar桥接模块：已注册UI图标\n");
} 