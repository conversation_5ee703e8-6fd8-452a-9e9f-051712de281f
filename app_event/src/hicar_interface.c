/**
 * @file hicar_interface.c
 * @brief 仪表UI与HiCar交互的接口实现
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../include/hicar_interface.h"
#include "../include/app_event.h"
#include "../include/ui_icon_controller.h"

// HiCar命令与事件类型映射表
typedef struct {
    e_voice_ctrl_command cmd;
    app_event_type_t event_type;
} cmd_event_map_t;

// 命令与事件映射关系
static const cmd_event_map_t cmd_event_map[] = {
    {VC_CMD_SCREEN_ON,  APP_EVENT_SYS_START},
    {VC_CMD_SCREEN_OFF, APP_EVENT_SYS_SHUTDOWN},
    {VC_CMD_LIGHT_ON,   APP_EVENT_BIZ_LIGHT_CHANGED},
    {VC_CMD_LIGHT_OFF,  APP_EVENT_BIZ_LIGHT_CHANGED},
    {VC_CMD_FLASH_ON,   APP_EVENT_BIZ_LIGHT_CHANGED},
    {VC_CMD_FLASH_OFF,  APP_EVENT_BIZ_LIGHT_CHANGED},
    {VC_CMD_LTS_ON,     APP_EVENT_BIZ_LIGHT_CHANGED},
    {VC_CMD_RTS_ON,     APP_EVENT_BIZ_LIGHT_CHANGED},
    {VC_CMD_TS_OFF,     APP_EVENT_BIZ_LIGHT_CHANGED}
};

// 车机状态报告回调函数
static car_state_report_callback_t g_car_state_callback = NULL;

// HiCar事件处理器
static void hicar_event_handler(app_event_t *event, void *user_data) {
    if (!event || !event->data) return;
    
    printf("收到HiCar相关事件: 类型=0x%04X\n", event->type);
    
    // 根据事件类型处理
    if (event->type == APP_EVENT_BIZ_LIGHT_CHANGED) {
        // 从事件数据中获取灯光状态
        e_voice_ctrl_command *cmd = (e_voice_ctrl_command *)event->data;
        
        // 如果有注册回调函数，则调用回调函数
        if (g_car_state_callback) {
            g_car_state_callback(*cmd);
            printf("已调用车机状态报告回调函数，状态=%d\n", *cmd);
        }
    }
}

/**
 * @brief 初始化HiCar接口
 */
int hicar_interface_init(void) {
    // 注册事件处理器
    if (app_event_register(APP_EVENT_BIZ_LIGHT_CHANGED, hicar_event_handler, NULL) != 0) {
        printf("注册HiCar事件处理器失败\n");
        return -1;
    }
    
    printf("HiCar接口初始化成功\n");
    return 0;
}

/**
 * @brief 清理HiCar接口资源
 */
void hicar_interface_deinit(void) {
    // 取消注册事件处理器
    app_event_unregister(APP_EVENT_BIZ_LIGHT_CHANGED, hicar_event_handler);
    
    // 清除回调函数
    g_car_state_callback = NULL;
    
    printf("HiCar接口资源已清理\n");
}

/**
 * @brief HiCar调用此接口去控制车机
 */
void voice_ctrl_cmd(e_voice_ctrl_command cmd) {
    printf("收到HiCar控制命令: %d\n", cmd);
    
    // 创建事件数据
    e_voice_ctrl_command *cmd_data = (e_voice_ctrl_command *)malloc(sizeof(e_voice_ctrl_command));
    if (!cmd_data) {
        printf("内存分配失败\n");
        return;
    }
    *cmd_data = cmd;
    
    // 查找对应的事件类型
    app_event_type_t event_type = APP_EVENT_BIZ_LIGHT_CHANGED; // 默认事件类型
    for (size_t i = 0; i < sizeof(cmd_event_map) / sizeof(cmd_event_map[0]); i++) {
        if (cmd_event_map[i].cmd == cmd) {
            event_type = cmd_event_map[i].event_type;
            break;
        }
    }
    
    // 创建并发布事件
    app_event_t event;
    event.type = event_type;
    event.timestamp = app_get_time();
    event.sender = NULL;
    event.data = cmd_data;
    event.data_size = sizeof(e_voice_ctrl_command);
    
    if (app_event_post(&event) != 0) {
        printf("发布事件失败\n");
        free(cmd_data);
        return;
    }
    
    printf("已发布HiCar控制事件，类型=0x%04X\n", event_type);
}

/**
 * @brief HiCar调用此接口向仪表UI系统注册回调函数
 */
void reg_car_state_report_callback(car_state_report_callback_t callback) {
    g_car_state_callback = callback;
    printf("已注册车机状态报告回调函数\n");
}

/**
 * @brief 报告车机状态变化
 */
void report_car_state(e_voice_ctrl_command state) {
    printf("报告车机状态变化: %d\n", state);
    
    // 如果有注册回调函数，则调用回调函数
    if (g_car_state_callback) {
        g_car_state_callback(state);
        printf("已调用车机状态报告回调函数\n");
    } else {
        printf("未注册车机状态报告回调函数\n");
    }
} 