/**
 * @file controller_status_ui_handler.c
 * @brief 中控控制器状态UI处理器 - 将0x90数据映射到UI图标控制
 * @version 1.0.0
 * @date 2025-07-22
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../include/app_event.h"
#include "../include/ui_icon_controller.h"

// 直接定义需要的宏，避免头文件冲突
#define UART_MSG_CONTROLLER_STATUS 0x90
#define CTRL_STATUS1_TCS_STATUS        (1 << 1)
#define CTRL_STATUS1_BRAKE_STATUS      (1 << 4)
#define CTRL_STATUS1_P_GEAR_SIGNAL     (1 << 5)
#define CTRL_STATUS1_WORK_MODE_MASK    (0x03 << 6)
#define CTRL_STATUS2_CRUISE_STATUS     (1 << 0)
#define CTRL_STATUS2_PUSH_STATUS       (1 << 2)
#define CTRL_STATUS2_REVERSE_STATUS    (1 << 3)
#define VEHICLE_STATUS_CHARGING        (1 << 0)
#define INVALID_VALUE_FE               0xFE
#define INVALID_VALUE_FF               0xFF

// 前向声明
static void controller_status_event_handler(app_event_t *event, void *user_data);

// 全局图标控制器指针
static icon_controller_t *g_icon_tcs = NULL;           // TCS状态图标
static icon_controller_t *g_icon_cruise = NULL;       // 巡航状态图标
static icon_controller_t *g_icon_eco_mode = NULL;     // ECO模式图标
static icon_controller_t *g_icon_power_mode = NULL;   // POWER模式图标
static icon_controller_t *g_icon_cruise_mode = NULL;  // CRUISE模式图标
static icon_controller_t *g_icon_brake = NULL;        // 制动状态图标
static icon_controller_t *g_icon_p_gear = NULL;       // P挡信号图标
static icon_controller_t *g_icon_charging = NULL;     // 充电状态图标
static icon_controller_t *g_icon_fault = NULL;        // 故障指示图标
static icon_controller_t *g_icon_battery_low = NULL;  // 低电量图标
static icon_controller_t *g_icon_reverse = NULL;      // 倒车状态图标
static icon_controller_t *g_icon_push = NULL;         // 推车状态图标

/**
 * @brief 初始化图标控制器
 * @param icon_objects 包含各种图标LVGL对象的结构体指针
 * @return 0成功，负值失败
 */
int controller_status_ui_init(void *icon_objects) {
    // 这里应该根据实际的UI设计创建图标控制器
    // 示例代码，实际使用时需要传入真实的LVGL图标对象
    
    printf("初始化中控控制器状态UI处理器\n");
    
    // 注册事件处理器
    int ret = app_event_register(APP_EVENT_BIZ_VEHICLE_STATUS, controller_status_event_handler, NULL);
    if (ret != 0) {
        printf("注册中控控制器状态事件处理器失败\n");
        return -1;
    }
    
    printf("中控控制器状态UI处理器初始化成功\n");
    return 0;
}

/**
 * @brief 设置图标控制器指针
 * @param icon_type 图标类型
 * @param controller 图标控制器指针
 */
void controller_status_ui_set_icon(const char *icon_type, icon_controller_t *controller) {
    if (!icon_type || !controller) return;
    
    if (strcmp(icon_type, "tcs") == 0) {
        g_icon_tcs = controller;
    } else if (strcmp(icon_type, "cruise") == 0) {
        g_icon_cruise = controller;
    } else if (strcmp(icon_type, "eco_mode") == 0) {
        g_icon_eco_mode = controller;
    } else if (strcmp(icon_type, "power_mode") == 0) {
        g_icon_power_mode = controller;
    } else if (strcmp(icon_type, "cruise_mode") == 0) {
        g_icon_cruise_mode = controller;
    } else if (strcmp(icon_type, "brake") == 0) {
        g_icon_brake = controller;
    } else if (strcmp(icon_type, "p_gear") == 0) {
        g_icon_p_gear = controller;
    } else if (strcmp(icon_type, "charging") == 0) {
        g_icon_charging = controller;
    } else if (strcmp(icon_type, "fault") == 0) {
        g_icon_fault = controller;
    } else if (strcmp(icon_type, "battery_low") == 0) {
        g_icon_battery_low = controller;
    } else if (strcmp(icon_type, "reverse") == 0) {
        g_icon_reverse = controller;
    } else if (strcmp(icon_type, "push") == 0) {
        g_icon_push = controller;
    }
    
    printf("设置图标控制器: %s\n", icon_type);
}

/**
 * @brief 解析中控控制器状态数据并更新UI图标
 * @param data 18字节的中控控制器状态数据
 */
static void update_ui_from_controller_status(const uint8_t *data) {
    if (!data) return;
    
    printf("根据中控控制器状态更新UI图标\n");
    
    // 解析控制器状态1 (Data0)
    uint8_t status1 = data[0];
    
    // TCS状态图标
    if (g_icon_tcs) {
        if (status1 & CTRL_STATUS1_TCS_STATUS) {
            icon_set_state(g_icon_tcs, ICON_STATE_ON);
            printf("TCS图标: 开启\n");
        } else {
            icon_set_state(g_icon_tcs, ICON_STATE_OFF);
            printf("TCS图标: 关闭\n");
        }
    }
    
    // 制动状态图标
    if (g_icon_brake) {
        if (status1 & CTRL_STATUS1_BRAKE_STATUS) {
            icon_set_state(g_icon_brake, ICON_STATE_ON);
            printf("制动图标: 制动中\n");
        } else {
            icon_set_state(g_icon_brake, ICON_STATE_OFF);
            printf("制动图标: 未制动\n");
        }
    }
    
    // P挡信号图标
    if (g_icon_p_gear) {
        if (status1 & CTRL_STATUS1_P_GEAR_SIGNAL) {
            icon_set_state(g_icon_p_gear, ICON_STATE_ON);
            printf("P挡图标: P挡\n");
        } else {
            icon_set_state(g_icon_p_gear, ICON_STATE_OFF);
            printf("P挡图标: 非P挡\n");
        }
    }
    
    // 工作模式图标
    uint8_t work_mode = (status1 & CTRL_STATUS1_WORK_MODE_MASK) >> 6;
    // 先关闭所有模式图标
    if (g_icon_eco_mode) icon_set_state(g_icon_eco_mode, ICON_STATE_OFF);
    if (g_icon_power_mode) icon_set_state(g_icon_power_mode, ICON_STATE_OFF);
    if (g_icon_cruise_mode) icon_set_state(g_icon_cruise_mode, ICON_STATE_OFF);
    
    switch (work_mode) {
        case 1: // ECO模式
            if (g_icon_eco_mode) {
                icon_set_state(g_icon_eco_mode, ICON_STATE_ON);
                printf("模式图标: ECO模式\n");
            }
            break;
        case 2: // POWER模式
            if (g_icon_power_mode) {
                icon_set_state(g_icon_power_mode, ICON_STATE_ON);
                printf("模式图标: POWER模式\n");
            }
            break;
        case 3: // CRUISE模式
            if (g_icon_cruise_mode) {
                icon_set_state(g_icon_cruise_mode, ICON_STATE_ON);
                printf("模式图标: CRUISE模式\n");
            }
            break;
        default:
            printf("模式图标: 标准模式\n");
            break;
    }
    
    // 解析控制器状态2 (Data1)
    uint8_t status2 = data[1];
    
    // 巡航状态图标
    if (g_icon_cruise) {
        if (status2 & CTRL_STATUS2_CRUISE_STATUS) {
            icon_set_state(g_icon_cruise, ICON_STATE_ON);
            printf("巡航图标: 开启\n");
        } else {
            icon_set_state(g_icon_cruise, ICON_STATE_OFF);
            printf("巡航图标: 关闭\n");
        }
    }
    
    // 倒车状态图标
    if (g_icon_reverse) {
        if (status2 & CTRL_STATUS2_REVERSE_STATUS) {
            icon_set_state(g_icon_reverse, ICON_STATE_BLINK_FAST);  // 倒车时快闪
            printf("倒车图标: 倒车中(快闪)\n");
        } else {
            icon_set_state(g_icon_reverse, ICON_STATE_OFF);
            printf("倒车图标: 正常\n");
        }
    }
    
    // 推车状态图标
    if (g_icon_push) {
        if (status2 & CTRL_STATUS2_PUSH_STATUS) {
            icon_set_state(g_icon_push, ICON_STATE_BLINK_SLOW);  // 推车时慢闪
            printf("推车图标: 推车中(慢闪)\n");
        } else {
            icon_set_state(g_icon_push, ICON_STATE_OFF);
            printf("推车图标: 正常\n");
        }
    }
    
    // 解析故障代码1 (Data8)
    uint8_t fault1 = data[8];
    if (g_icon_fault) {
        if (fault1 != 0) {
            icon_set_state(g_icon_fault, ICON_STATE_BLINK_FAST);  // 故障时快闪
            printf("故障图标: 有故障(快闪)\n");
        } else {
            icon_set_state(g_icon_fault, ICON_STATE_OFF);
            printf("故障图标: 无故障\n");
        }
    }
    
    // 解析整车状态 (Data15)
    uint8_t vehicle_status = data[15];
    
    // 充电状态图标
    if (g_icon_charging) {
        if (vehicle_status & VEHICLE_STATUS_CHARGING) {
            icon_set_state(g_icon_charging, ICON_STATE_BLINK_SLOW);  // 充电时慢闪
            printf("充电图标: 充电中(慢闪)\n");
        } else {
            icon_set_state(g_icon_charging, ICON_STATE_OFF);
            printf("充电图标: 未充电\n");
        }
    }
    
    // 解析铅酸SOC (Data10) - 低电量提示
    uint8_t soc = data[10];
    if (g_icon_battery_low) {
        if (soc != INVALID_VALUE_FF && soc != INVALID_VALUE_FE && soc < 20) {
            icon_set_state(g_icon_battery_low, ICON_STATE_BLINK_FAST);  // 低电量快闪
            printf("低电量图标: 电量低(快闪) - %d%%\n", soc);
        } else {
            icon_set_state(g_icon_battery_low, ICON_STATE_OFF);
            printf("低电量图标: 电量正常\n");
        }
    }
}

/**
 * @brief 中控控制器状态事件处理器
 * @param event 事件指针
 * @param user_data 用户数据
 */
static void controller_status_event_handler(app_event_t *event, void *user_data) {
    if (!event || !event->data) return;
    
    printf("收到中控控制器状态事件\n");
    
    // 检查事件类型
    if (event->type != APP_EVENT_BIZ_VEHICLE_STATUS) {
        return;
    }
    
    // 解析事件数据
    typedef struct {
        uint8_t id;
        uint8_t data[256];
        uint16_t data_len;
    } uart_frame_data_t;
    
    uart_frame_data_t *frame_data = (uart_frame_data_t *)event->data;
    
    // 检查是否是0x90中控控制器状态消息
    if (frame_data->id == UART_MSG_CONTROLLER_STATUS && frame_data->data_len >= 18) {
        printf("处理0x90中控控制器状态数据，更新UI图标\n");
        update_ui_from_controller_status(frame_data->data);
    }
}

/**
 * @brief 清理资源
 */
void controller_status_ui_deinit(void) {
    // 取消注册事件处理器
    app_event_unregister(APP_EVENT_BIZ_VEHICLE_STATUS, controller_status_event_handler);
    
    // 清空图标控制器指针
    g_icon_tcs = NULL;
    g_icon_cruise = NULL;
    g_icon_eco_mode = NULL;
    g_icon_power_mode = NULL;
    g_icon_cruise_mode = NULL;
    g_icon_brake = NULL;
    g_icon_p_gear = NULL;
    g_icon_charging = NULL;
    g_icon_fault = NULL;
    g_icon_battery_low = NULL;
    g_icon_reverse = NULL;
    g_icon_push = NULL;
    
    printf("中控控制器状态UI处理器已清理\n");
}
