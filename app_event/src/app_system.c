/**
 * @file app_system.c
 * @brief 系统通用接口实现
 */

#include "../include/app_system.h"
#include <stdlib.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/time.h>

/**
 * @brief 创建互斥锁
 * @return 互斥锁句柄
 */
void *app_mutex_create(void) {
    pthread_mutex_t *mutex = (pthread_mutex_t *)malloc(sizeof(pthread_mutex_t));
    if (mutex) {
        if (pthread_mutex_init(mutex, NULL) != 0) {
            free(mutex);
            return NULL;
        }
    }
    return mutex;
}

/**
 * @brief 销毁互斥锁
 * @param mutex 互斥锁句柄
 */
void app_mutex_destroy(void *mutex) {
    if (mutex) {
        pthread_mutex_destroy((pthread_mutex_t *)mutex);
        free(mutex);
    }
}

/**
 * @brief 锁定互斥锁
 * @param mutex 互斥锁句柄
 */
void app_mutex_lock(void *mutex) {
    if (mutex) {
        pthread_mutex_lock((pthread_mutex_t *)mutex);
    }
}

/**
 * @brief 解锁互斥锁
 * @param mutex 互斥锁句柄
 */
void app_mutex_unlock(void *mutex) {
    if (mutex) {
        pthread_mutex_unlock((pthread_mutex_t *)mutex);
    }
}

/**
 * @brief 延时函数(毫秒)
 * @param ms 延时毫秒数
 */
void app_delay_ms(uint32_t ms) {
    usleep(ms * 1000);
}

/**
 * @brief 获取系统时间戳(毫秒)
 * @return 时间戳
 */
uint32_t app_get_time(void) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint32_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
} 