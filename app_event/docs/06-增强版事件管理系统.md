# 增强版事件管理系统

```c
/******************************************************************
 * 增强版事件系统 API 总览（16 个）
 * 1~8  事件总线核心
 * 9~11 插件管理
 * 12~16 HAL 抽象层（Linux/RTOS/裸机适配）
 ******************************************************************/

/*--------- 1~8：事件总线核心 ---------*/
bool eb_init(void);
/* 初始化事件总线：创建队列、互斥量、内存池 */

void eb_deinit(void);
/* 释放所有资源（反注册插件、释放队列、内存池） */

bool eb_subscribe(uint16_t id, event_cb_t cb, void *ctx);
/* 订阅事件：id=事件号，cb=回调，ctx=用户上下文 */

bool eb_unsubscribe(uint16_t id, event_cb_t cb);
/* 精确取消订阅：需匹配 id 与 cb 指针 */

bool eb_publish(uint16_t id, void *data);
/* 任务级发布：线程/主循环安全，立即派发 */

bool eb_publish_isr(uint16_t id, void *data);
/* 中断级发布：内部写入无锁环形队列 */

void eb_dispatch(void);
/* 后台任务/主循环调用，从队列取出事件并派发 */

uint32_t eb_stats_lost(void);
/* 获取当前丢包计数（队列溢出次数） */

/*--------- 9~11：插件管理 ---------*/
bool eb_plug_register(const char *name,
                      void (*init)(void),
                      void (*deinit)(void));
/* 注册插件：init/deinit 会在 start/stop 时被调用 */

bool eb_plug_unregister(const char *name);
/* 按名称卸载插件；若仍有订阅则返回 false */

void eb_plug_start_all(void);
/* 一次性调用所有已注册插件的 init() */

/*--------- 12~16：HAL 抽象层 ---------*/
bool eb_hal_init(void);
/* 平台初始化：时钟、串口、定时器、任务/线程 */

void eb_hal_uart_send(const uint8_t *buf, uint16_t len);
/* 发送一串字节（阻塞或中断 DMA） */

bool eb_hal_timer_start(uint32_t ms, bool repeat, void (*cb)(void));
/* 启动硬件/软件定时器：ms=周期，repeat=true=周期模式 */

void eb_hal_timer_stop(void);
/* 停止定时器 */

bool eb_hal_post_from_isr(uint16_t id, void *data);
/* 由 HAL 内部调用，把事件从中断安全地投递到事件队列 */
```