# API接口文档

**文档创建时间**: 2025-07-29 16:30  
**编写人员**: <PERSON> (全栈开发者)  
**文档版本**: v1.0  
**项目**: HiCar Lite 精简版语音控制系统  

## 1. 文档概述

本文档详细描述了HiCar Lite系统的所有API接口，包括HAL层、服务层和应用层的接口定义和使用方法。

## 2. HAL层接口

### 2.1 平台HAL接口

#### 2.1.1 初始化和清理
```c
bool platform_hal_init(void);
void platform_hal_deinit(void);
bool platform_hal_is_ready(void);
```

#### 2.1.2 时间和延时
```c
uint32_t platform_hal_get_tick(void);
void platform_hal_delay_ms(uint32_t ms);
```

#### 2.1.3 临界区控制
```c
void platform_hal_critical_enter(void);
void platform_hal_critical_exit(void);
```

### 2.2 UART HAL接口

#### 2.2.1 初始化和配置
```c
bool uart_hal_init(const char *device, uint32_t baudrate);
void uart_hal_deinit(void);
bool uart_hal_is_ready(void);
```

#### 2.2.2 数据收发
```c
bool uart_hal_send(const uint8_t *data, uint16_t len);
bool uart_hal_receive_start(void);
void uart_hal_process(void);
```

#### 2.2.3 回调注册
```c
bool uart_hal_register_callback(uart_data_callback_t callback);
```

### 2.3 定时器HAL接口

#### 2.3.1 初始化和控制
```c
bool timer_hal_init(void);
void timer_hal_deinit(void);
bool timer_hal_is_running(void);
```

#### 2.3.2 定时器操作
```c
bool timer_hal_start(uint32_t ms, bool repeat, timer_callback_t callback);
void timer_hal_stop(void);
void timer_hal_process(void);
```

## 3. 服务层接口

### 3.1 事件服务接口

#### 3.1.1 初始化和管理
```c
app_result_t event_service_init(void);
void event_service_deinit(void);
void event_service_register_modules(void);
```

#### 3.1.2 事件操作
```c
app_result_t event_service_subscribe(app_event_type_t type, event_cb_t callback);
app_result_t event_service_publish(app_event_type_t type, void *data);
app_result_t event_service_publish_isr(app_event_type_t type, void *data);
void event_service_dispatch(void);
```

#### 3.1.3 统计信息
```c
uint32_t event_service_get_lost_count(void);
```

### 3.2 通信服务接口

#### 3.2.1 初始化和状态
```c
app_result_t comm_service_init(void);
void comm_service_deinit(void);
bool comm_service_is_connected(void);
```

#### 3.2.2 消息处理
```c
app_result_t comm_service_send(uint8_t msg_id, const uint8_t *data, uint16_t len);
void comm_service_process(void);
app_result_t comm_service_register_callback(comm_message_callback_t callback);
```

#### 3.2.3 统计信息
```c
void comm_service_get_stats(char *buffer, size_t size);
```

### 3.3 配置管理接口

#### 3.3.1 配置操作
```c
app_result_t config_init(void);
void config_deinit(void);
const app_config_t* config_get(void);
bool config_validate(void);
void config_print(void);
```

## 4. 应用层接口

### 4.1 HiCar业务接口

#### 4.1.1 初始化和状态
```c
app_result_t hicar_init(void);
void hicar_deinit(void);
hicar_state_t hicar_get_state(void);
void hicar_set_callback(hicar_state_callback_t callback);
```

#### 4.1.2 语音命令处理
```c
app_result_t hicar_handle_voice_cmd(e_voice_ctrl_command cmd);
```

### 4.2 UI控制接口

#### 4.2.1 初始化和管理
```c
app_result_t ui_init(void);
void ui_deinit(void);
void ui_update(void);
void ui_process_events(void);
```

#### 4.2.2 图标控制
```c
app_result_t ui_register_icon(uint8_t icon_id, void *icon);
app_result_t ui_set_icon_state(uint8_t icon_id, icon_state_t state);
```

## 5. 数据类型定义

### 5.1 错误码
```c
typedef enum {
    APP_OK = 0,
    APP_ERROR_INIT_FAILED,
    APP_ERROR_INVALID_PARAM,
    APP_ERROR_NO_MEMORY,
    APP_ERROR_TIMEOUT,
    APP_ERROR_NOT_READY,
    APP_ERROR_BUSY,
    APP_ERROR_NOT_SUPPORTED,
    APP_ERROR_ALREADY_INITIALIZED
} app_result_t;
```

### 5.2 事件类型
```c
typedef enum {
    EVENT_HICAR_VOICE_CMD = 0x1000,
    EVENT_HICAR_STATE_CHANGE,
    EVENT_UI_ICON_UPDATE,
    EVENT_UART_DATA_RECEIVED,
    EVENT_TIMER_EXPIRED,
    // ...
} app_event_type_t;
```

### 5.3 语音命令
```c
typedef enum {
    VOICE_CMD_TURN_ON_LIGHT,
    VOICE_CMD_TURN_OFF_LIGHT,
    VOICE_CMD_TURN_ON_HORN,
    VOICE_CMD_TURN_OFF_HORN,
    VOICE_CMD_LOCK_VEHICLE,
    VOICE_CMD_UNLOCK_VEHICLE,
    VOICE_CMD_START_ENGINE,
    VOICE_CMD_STOP_ENGINE,
    // ...
} e_voice_ctrl_command;
```

### 5.4 状态定义
```c
typedef enum {
    HICAR_STATE_IDLE,
    HICAR_STATE_CONNECTED,
    HICAR_STATE_VOICE_ACTIVE,
    HICAR_STATE_ERROR
} hicar_state_t;

typedef enum {
    ICON_STATE_OFF,
    ICON_STATE_ON,
    ICON_STATE_BLINK_FAST,
    ICON_STATE_BLINK_SLOW
} icon_state_t;
```

## 6. 回调函数类型

### 6.1 事件回调
```c
typedef void (*event_cb_t)(uint16_t id, void *data, void *ctx);
```

### 6.2 定时器回调
```c
typedef void (*timer_callback_t)(void);
```

### 6.3 状态回调
```c
typedef void (*hicar_state_callback_t)(hicar_state_t state);
```

### 6.4 通信回调
```c
typedef void (*comm_message_callback_t)(const comm_message_t *msg);
typedef void (*uart_data_callback_t)(const uart_rx_data_t *data);
```

## 7. 使用示例

### 7.1 初始化系统
```c
// 初始化HAL层
if (hal_init_all() != APP_OK) {
    return -1;
}

// 初始化事件总线
if (!eb_init()) {
    return -1;
}

// 初始化服务层
if (services_init_all() != APP_OK) {
    return -1;
}

// 初始化应用层
if (application_init_all() != APP_OK) {
    return -1;
}
```

### 7.2 处理语音命令
```c
// 处理语音命令
app_result_t result = hicar_handle_voice_cmd(VOICE_CMD_TURN_ON_LIGHT);
if (result != APP_OK) {
    printf("Command failed: %d\n", result);
}
```

### 7.3 设置图标状态
```c
// 设置图标状态
ui_set_icon_state(0, ICON_STATE_BLINK_FAST);
```

### 7.4 发送通信消息
```c
// 发送控制命令
uint8_t cmd_data[] = {0x01, 0x01};  // 开灯命令
comm_service_send(0x10, cmd_data, sizeof(cmd_data));
```

## 8. 注意事项

### 8.1 线程安全
- 所有HAL层接口都是线程安全的
- 事件发布支持中断安全版本 (`*_isr`)
- 临界区保护使用递归互斥量

### 8.2 错误处理
- 所有函数都有明确的返回值
- 使用统一的错误码体系
- 支持错误统计和调试信息

### 8.3 内存管理
- 系统使用标准malloc/free
- 事件数据需要调用者管理生命周期
- 配置数据为静态分配

### 8.4 性能考虑
- 主循环间隔可配置 (默认10ms)
- 事件分发采用优先级队列
- UART采用非阻塞I/O

---
**文档完成时间**: 2025-07-29 16:30  
**下一步**: 编写模块实现说明
