# 新架构设计方案

**文档创建时间**: 2025-07-28 14:55  
**设计人员**: <PERSON> (全栈开发者)  
**设计版本**: v2.0  

## 1. 架构设计原则

### 1.1 核心设计原则
- **单一职责原则**: 每个模块只负责一个明确的功能域
- **开闭原则**: 对扩展开放，对修改封闭
- **依赖倒置原则**: 高层模块不依赖低层模块，都依赖抽象
- **接口隔离原则**: 客户端不应依赖它不需要的接口
- **最小知识原则**: 模块间通过最小的接口进行交互

### 1.2 架构质量属性
- **可维护性**: 模块化设计，便于维护和修改
- **可扩展性**: 插件化架构，支持功能扩展
- **可测试性**: 依赖注入，便于单元测试
- **可重用性**: 标准化接口，便于模块复用
- **高性能**: 内存池、事件驱动等优化机制

## 2. 新架构总体设计

### 2.1 分层架构
```
┌─────────────────────────────────────────────────────────┐
│                    Application Layer                    │
│                     (应用层)                            │
├─────────────────────────────────────────────────────────┤
│                    Business Layer                       │
│                     (业务层)                            │
├─────────────────────────────────────────────────────────┤
│                    Service Layer                        │
│                     (服务层)                            │
├─────────────────────────────────────────────────────────┤
│                   Framework Layer                       │
│                     (框架层)                            │
├─────────────────────────────────────────────────────────┤
│                  Infrastructure Layer                   │
│                     (基础设施层)                         │
└─────────────────────────────────────────────────────────┘
```

### 2.2 模块划分

#### 2.2.1 应用层 (Application Layer)
- **主程序模块**: 系统启动、主循环控制
- **配置管理模块**: 配置文件加载、参数管理
- **日志管理模块**: 统一日志记录和管理

#### 2.2.2 业务层 (Business Layer)
- **HiCar业务模块**: HiCar语音控制业务逻辑
- **车辆控制模块**: 车辆状态管理和控制
- **UI业务模块**: UI交互业务逻辑

#### 2.2.3 服务层 (Service Layer)
- **事件服务**: 事件队列、分发、处理
- **通信服务**: UART通信、协议处理
- **UI服务**: 图标控制、动画管理
- **桥接服务**: 协议转换、命令映射

#### 2.2.4 框架层 (Framework Layer)
- **事件框架**: 事件系统核心实现
- **插件框架**: 插件加载、管理机制
- **资源管理框架**: 内存池、资源自动管理
- **错误处理框架**: 统一错误码、异常处理

#### 2.2.5 基础设施层 (Infrastructure Layer)
- **UART HAL**: 硬件抽象层
- **LVGL适配**: LVGL图形库适配
- **系统工具**: 时间、线程、同步等工具

## 3. 详细模块设计

### 3.1 事件系统重设计

#### 3.1.1 优先级事件队列
```c
typedef enum {
    EVENT_PRIORITY_CRITICAL = 0,    // 关键事件
    EVENT_PRIORITY_HIGH = 1,        // 高优先级
    EVENT_PRIORITY_NORMAL = 2,      // 普通优先级
    EVENT_PRIORITY_LOW = 3,         // 低优先级
    EVENT_PRIORITY_MAX = 4
} event_priority_t;

typedef struct {
    event_priority_t priority;
    app_event_type_t type;
    uint32_t timestamp;
    void *data;
    size_t data_size;
    void (*cleanup)(void *data);    // 资源清理函数
} event_v2_t;
```

#### 3.1.2 事件队列管理
```c
typedef struct {
    event_v2_t *events[EVENT_PRIORITY_MAX][EVENT_QUEUE_SIZE];
    uint16_t head[EVENT_PRIORITY_MAX];
    uint16_t tail[EVENT_PRIORITY_MAX];
    uint16_t size[EVENT_PRIORITY_MAX];
    pthread_mutex_t mutex;          // 线程安全
    pthread_cond_t cond;           // 条件变量
} priority_event_queue_t;
```

### 3.2 内存管理重设计

#### 3.2.1 内存池设计
```c
typedef struct {
    void *pool_start;               // 内存池起始地址
    size_t pool_size;              // 内存池总大小
    size_t block_size;             // 块大小
    uint32_t *free_bitmap;         // 空闲块位图
    uint32_t total_blocks;         // 总块数
    uint32_t free_blocks;          // 空闲块数
    pthread_mutex_t mutex;         // 线程安全
} memory_pool_t;

// 内存池操作接口
memory_pool_t* memory_pool_create(size_t pool_size, size_t block_size);
void* memory_pool_alloc(memory_pool_t *pool);
void memory_pool_free(memory_pool_t *pool, void *ptr);
void memory_pool_destroy(memory_pool_t *pool);
```

#### 3.2.2 RAII资源管理
```c
typedef struct {
    void *resource;                 // 资源指针
    void (*cleanup)(void *);        // 清理函数
} auto_resource_t;

#define AUTO_CLEANUP(cleanup_func) __attribute__((cleanup(cleanup_func)))
#define DECLARE_AUTO_RESOURCE(type, cleanup_func) \
    typedef type AUTO_CLEANUP(cleanup_func) auto_##type##_t
```

### 3.3 配置管理设计

#### 3.3.1 配置结构定义
```c
typedef struct {
    // UART配置
    char uart_device[256];
    int uart_baudrate;
    int uart_timeout;
    
    // 显示配置
    int display_width;
    int display_height;
    int display_buffer_size;
    
    // 事件配置
    int event_queue_size;
    int event_thread_count;
    
    // 图标配置
    int icon_blink_fast_interval;
    int icon_blink_slow_interval;
    int icon_default_timeout;
    
    // 日志配置
    char log_level[32];
    char log_file[256];
    int log_max_size;
} app_config_t;
```

#### 3.3.2 配置文件格式 (TOML)
```toml
[uart]
device = "/dev/ttyS0"
baudrate = 115200
timeout = 1000

[display]
width = 1024
height = 600
buffer_size = 614400

[event]
queue_size = 128
thread_count = 2

[icon]
blink_fast_interval = 250
blink_slow_interval = 500
default_timeout = 5000

[log]
level = "INFO"
file = "/var/log/hicar_demo.log"
max_size = 10485760
```

### 3.4 错误处理设计

#### 3.4.1 统一错误码
```c
typedef enum {
    // 成功
    APP_SUCCESS = 0,
    
    // 通用错误 (1-99)
    APP_ERROR_INVALID_PARAM = 1,
    APP_ERROR_OUT_OF_MEMORY = 2,
    APP_ERROR_TIMEOUT = 3,
    APP_ERROR_NOT_INITIALIZED = 4,
    APP_ERROR_ALREADY_INITIALIZED = 5,
    
    // 事件系统错误 (100-199)
    APP_ERROR_EVENT_QUEUE_FULL = 100,
    APP_ERROR_EVENT_HANDLER_NOT_FOUND = 101,
    APP_ERROR_EVENT_INVALID_TYPE = 102,
    
    // 通信错误 (200-299)
    APP_ERROR_UART_OPEN_FAILED = 200,
    APP_ERROR_UART_SEND_FAILED = 201,
    APP_ERROR_UART_RECEIVE_FAILED = 202,
    APP_ERROR_PROTOCOL_INVALID_FRAME = 203,
    
    // UI错误 (300-399)
    APP_ERROR_UI_INIT_FAILED = 300,
    APP_ERROR_ICON_CREATE_FAILED = 301,
    APP_ERROR_LVGL_ERROR = 302,
    
    // 配置错误 (400-499)
    APP_ERROR_CONFIG_FILE_NOT_FOUND = 400,
    APP_ERROR_CONFIG_PARSE_FAILED = 401,
    APP_ERROR_CONFIG_INVALID_VALUE = 402,
} app_error_code_t;
```

#### 3.4.2 错误处理机制
```c
typedef struct {
    app_error_code_t code;
    char message[256];
    char file[128];
    int line;
    uint32_t timestamp;
} app_error_t;

// 错误处理宏
#define APP_RETURN_ERROR(code, msg) \
    do { \
        app_error_set(code, msg, __FILE__, __LINE__); \
        return code; \
    } while(0)

#define APP_CHECK_ERROR(expr, code, msg) \
    do { \
        if (!(expr)) { \
            APP_RETURN_ERROR(code, msg); \
        } \
    } while(0)
```

### 3.5 插件架构设计

#### 3.5.1 插件接口定义
```c
typedef struct {
    const char *name;               // 插件名称
    const char *version;            // 插件版本
    const char *description;        // 插件描述
    
    // 生命周期函数
    app_error_code_t (*init)(const void *config);
    app_error_code_t (*start)(void);
    void (*stop)(void);
    void (*deinit)(void);
    
    // 事件处理
    app_error_code_t (*handle_event)(const event_v2_t *event);
    
    // 配置更新
    app_error_code_t (*update_config)(const void *config);
} plugin_interface_t;
```

#### 3.5.2 插件管理器
```c
typedef struct {
    plugin_interface_t *interface;
    void *handle;                   // 动态库句柄
    bool is_loaded;
    bool is_started;
} plugin_instance_t;

typedef struct {
    plugin_instance_t plugins[MAX_PLUGINS];
    int plugin_count;
    pthread_mutex_t mutex;
} plugin_manager_t;
```

## 4. 接口设计

### 4.1 核心服务接口

#### 4.1.1 事件服务接口
```c
// 事件服务初始化
app_error_code_t event_service_init(const app_config_t *config);

// 事件发布
app_error_code_t event_service_publish(const event_v2_t *event);

// 事件订阅
app_error_code_t event_service_subscribe(app_event_type_t type, 
                                        event_handler_t handler, 
                                        void *user_data);

// 事件处理
void event_service_process(void);

// 事件服务清理
void event_service_deinit(void);
```

#### 4.1.2 通信服务接口
```c
// 通信服务初始化
app_error_code_t comm_service_init(const app_config_t *config);

// 发送数据
app_error_code_t comm_service_send(uint8_t msg_id, 
                                  const uint8_t *data, 
                                  uint16_t data_len);

// 注册接收回调
app_error_code_t comm_service_register_callback(comm_callback_t callback);

// 通信服务清理
void comm_service_deinit(void);
```

#### 4.1.3 UI服务接口
```c
// UI服务初始化
app_error_code_t ui_service_init(const app_config_t *config);

// 创建图标控制器
icon_controller_t* ui_service_create_icon(lv_obj_t *icon);

// 设置图标状态
app_error_code_t ui_service_set_icon_state(icon_controller_t *ctrl, 
                                          icon_state_t state);

// UI服务更新
void ui_service_update(void);

// UI服务清理
void ui_service_deinit(void);
```

### 4.2 业务模块接口

#### 4.2.1 HiCar业务接口
```c
// HiCar业务初始化
app_error_code_t hicar_business_init(const app_config_t *config);

// 处理语音命令
app_error_code_t hicar_business_handle_voice_cmd(e_voice_ctrl_command cmd);

// 注册状态回调
app_error_code_t hicar_business_register_callback(hicar_callback_t callback);

// HiCar业务清理
void hicar_business_deinit(void);
```

## 5. 线程模型设计

### 5.1 多线程架构
```c
typedef enum {
    THREAD_MAIN = 0,        // 主线程 (UI + 主循环)
    THREAD_EVENT,           // 事件处理线程
    THREAD_COMM,            // 通信处理线程
    THREAD_WORKER,          // 工作线程池
    THREAD_MAX
} thread_type_t;

typedef struct {
    pthread_t thread_id;
    thread_type_t type;
    bool is_running;
    void *context;
} thread_info_t;
```

### 5.2 线程间通信
```c
// 使用消息队列进行线程间通信
typedef struct {
    uint32_t msg_type;
    void *data;
    size_t data_size;
    void (*cleanup)(void *data);
} thread_message_t;

// 线程安全的消息队列
typedef struct {
    thread_message_t *messages[MSG_QUEUE_SIZE];
    uint16_t head;
    uint16_t tail;
    uint16_t size;
    pthread_mutex_t mutex;
    pthread_cond_t not_empty;
    pthread_cond_t not_full;
} thread_msg_queue_t;
```

## 6. 性能优化设计

### 6.1 事件驱动主循环
```c
// 使用epoll替代轮询
typedef struct {
    int epoll_fd;
    struct epoll_event events[MAX_EVENTS];
    int event_count;
} event_loop_t;

// 主循环实现
void main_event_loop(void) {
    event_loop_t loop;
    init_event_loop(&loop);
    
    while (g_running) {
        int ready = epoll_wait(loop.epoll_fd, loop.events, 
                              MAX_EVENTS, EPOLL_TIMEOUT);
        
        for (int i = 0; i < ready; i++) {
            handle_epoll_event(&loop.events[i]);
        }
        
        // 处理定时任务
        process_timer_tasks();
        
        // 处理LVGL任务
        lv_task_handler();
    }
}
```

### 6.2 零拷贝数据传输
```c
// 使用引用计数避免数据拷贝
typedef struct {
    void *data;
    size_t size;
    atomic_int ref_count;
    void (*destructor)(void *data);
} shared_buffer_t;

shared_buffer_t* shared_buffer_create(size_t size);
shared_buffer_t* shared_buffer_ref(shared_buffer_t *buffer);
void shared_buffer_unref(shared_buffer_t *buffer);
```

## 7. 总结

新架构设计的主要特点：

1. **清晰的分层架构** - 5层架构，职责明确
2. **模块化设计** - 高内聚、低耦合的模块划分
3. **插件化扩展** - 支持动态插件加载
4. **性能优化** - 内存池、事件驱动、零拷贝
5. **可靠性增强** - 统一错误处理、RAII资源管理
6. **配置化管理** - 外部配置文件支持
7. **完整的测试支持** - 依赖注入、Mock框架

这个新架构将显著提升系统的可维护性、可扩展性和性能表现。

---
**设计完成时间**: 2025-07-28 14:55  
**下一步**: 设计优化的目录结构
