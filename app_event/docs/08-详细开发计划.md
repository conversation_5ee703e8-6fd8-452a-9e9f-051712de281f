# 详细开发计划

**文档创建时间**: 2025-07-29 15:42  
**制定人员**: <PERSON> (全栈开发者)  
**计划版本**: v1.0  
**预计开发周期**: 5个阶段  

## 1. 项目概述

### 1.1 开发目标
基于精简版架构设计方案，在项目根目录实现完整的HiCar语音控制系统，代码量控制在1000行左右。

### 1.2 技术栈
- **核心**: 增强版事件管理系统 (16个API)
- **UI**: LVGL图形库
- **通信**: UART串口通信
- **平台**: Linux/嵌入式系统
- **语言**: C语言

### 1.3 架构层次
```
应用层 (~200行) - HiCar业务 + UI控制
服务层 (~400行) - 事件总线 + 通信 + 配置
HAL层 (~400行) - 平台适配 + UART + 定时器
```

## 2. 项目目录结构设计

### 2.1 新增目录结构
```
hicar_lite/                    # 新的精简版项目目录
├── include/                   # 公共头文件
│   ├── app_types.h           # 应用类型定义
│   ├── app_config.h          # 配置定义
│   └── app_events.h          # 事件类型定义
├── src/                      # 源代码目录
│   ├── hal/                  # HAL层 (~400行)
│   │   ├── platform_hal.c    # 平台适配 (~150行)
│   │   ├── uart_hal.c        # UART HAL (~150行)
│   │   └── timer_hal.c       # 定时器HAL (~100行)
│   ├── services/             # 服务层 (~400行)
│   │   ├── event_service.c   # 事件总线核心 (~150行)
│   │   ├── comm_service.c    # 通信服务 (~150行)
│   │   └── config_manager.c  # 配置管理 (~100行)
│   ├── application/          # 应用层 (~200行)
│   │   ├── hicar_business.c  # HiCar业务 (~100行)
│   │   └── ui_controller.c   # UI控制 (~100行)
│   └── main.c               # 主程序 (~50行)
├── tests/                    # 测试代码
│   ├── unit/                # 单元测试
│   └── integration/         # 集成测试
├── docs/                    # 项目文档
├── Makefile                 # 构建文件
└── README.md               # 项目说明
```

## 3. 开发阶段规划

### 3.1 阶段1: 项目基础设施 (预计1小时)
**任务**: 创建项目目录结构和基础文件
- [x] 制定开发计划
- [ ] 创建目录结构
- [ ] 创建基础头文件
- [ ] 创建Makefile构建系统
- [ ] 编写项目README

**输出文件**:
- `hicar_lite/` 完整目录结构
- 基础头文件和构建系统

### 3.2 阶段2: HAL层实现 (预计2小时)
**任务**: 实现硬件抽象层，为上层提供统一接口
- [ ] 实现平台适配模块 (platform_hal.c ~150行)
- [ ] 实现UART HAL模块 (uart_hal.c ~150行)  
- [ ] 实现定时器HAL模块 (timer_hal.c ~100行)
- [ ] HAL层单元测试

**核心接口**:
```c
// 平台适配 (6个函数)
bool platform_hal_init(void);
void platform_hal_deinit(void);
uint32_t platform_hal_get_tick(void);
void platform_hal_delay_ms(uint32_t ms);
void platform_hal_critical_enter(void);
void platform_hal_critical_exit(void);

// UART HAL (7个函数)
bool uart_hal_init(const char *device, uint32_t baudrate);
void uart_hal_deinit(void);
bool uart_hal_send(const uint8_t *data, uint16_t len);
bool uart_hal_receive_start(void);
void uart_hal_process(void);
bool uart_hal_register_callback(void (*cb)(const uart_rx_data_t *data));
bool uart_hal_is_ready(void);

// 定时器HAL (5个函数)
bool timer_hal_init(void);
void timer_hal_deinit(void);
bool timer_hal_start(uint32_t ms, bool repeat, timer_callback_t callback);
void timer_hal_stop(void);
void timer_hal_process(void);
```

### 3.3 阶段3: 服务层实现 (预计2小时)
**任务**: 实现核心服务层，基于增强版事件系统
- [ ] 实现事件总线核心 (event_service.c ~150行)
- [ ] 实现通信服务 (comm_service.c ~150行)
- [ ] 实现配置管理 (config_manager.c ~100行)
- [ ] 服务层集成测试

**核心接口**:
```c
// 事件服务 (8个函数) - 基于16个增强版API
bool event_service_init(void);
void event_service_deinit(void);
bool event_service_subscribe(app_event_type_t type, event_cb_t callback);
bool event_service_publish(app_event_type_t type, void *data);
bool event_service_publish_isr(app_event_type_t type, void *data);
void event_service_dispatch(void);
uint32_t event_service_get_lost_count(void);
void event_service_register_modules(void);

// 通信服务 (6个函数)
bool comm_service_init(void);
void comm_service_deinit(void);
bool comm_service_send(uint8_t msg_id, const uint8_t *data, uint16_t len);
void comm_service_process(void);
bool comm_service_register_callback(void (*cb)(const comm_message_t *msg));
bool comm_service_is_connected(void);

// 配置管理 (4个函数)
const app_config_t* config_get(void);
bool config_init(void);
void config_deinit(void);
bool config_validate(void);
```

### 3.4 阶段4: 应用层实现 (预计1.5小时)
**任务**: 实现业务逻辑层
- [ ] 实现HiCar业务模块 (hicar_business.c ~100行)
- [ ] 实现UI控制模块 (ui_controller.c ~100行)
- [ ] 应用层功能测试

**核心接口**:
```c
// HiCar业务 (5个函数)
bool hicar_init(void);
void hicar_deinit(void);
bool hicar_handle_voice_cmd(e_voice_ctrl_command cmd);
hicar_state_t hicar_get_state(void);
void hicar_set_callback(void (*cb)(hicar_state_t state));

// UI控制 (6个函数)
bool ui_init(void);
void ui_deinit(void);
bool ui_set_icon_state(uint8_t icon_id, icon_state_t state);
void ui_update(void);
bool ui_register_icon(uint8_t icon_id, lv_obj_t *icon);
void ui_process_events(void);
```

### 3.5 阶段5: 主程序和集成 (预计1小时)
**任务**: 完成主程序和系统集成
- [ ] 实现主程序入口 (main.c ~50行)
- [ ] 系统集成测试
- [ ] 性能优化
- [ ] 代码审查

**主程序结构**:
```c
int main(void) {
    // 1. 初始化HAL层
    // 2. 初始化事件总线
    // 3. 注册所有模块
    // 4. 初始化各服务
    // 5. 主循环 (事件驱动)
    return 0;
}
```

## 4. 代码量控制策略

### 4.1 分模块代码量目标
| 模块 | 目标行数 | 核心功能 |
|------|----------|----------|
| platform_hal.c | ~150行 | 平台抽象、时间、同步 |
| uart_hal.c | ~150行 | 串口收发、协议解析 |
| timer_hal.c | ~100行 | 定时器管理 |
| event_service.c | ~150行 | 事件总线封装 |
| comm_service.c | ~150行 | 通信协议处理 |
| config_manager.c | ~100行 | 配置管理 |
| hicar_business.c | ~100行 | 语音命令处理 |
| ui_controller.c | ~100行 | 图标状态管理 |
| main.c | ~50行 | 主循环和初始化 |
| **总计** | **~1000行** | **完整功能** |

### 4.2 代码质量要求
- **函数长度**: 单个函数不超过50行
- **文件长度**: 单个文件不超过200行
- **注释率**: 关键函数必须有注释
- **错误处理**: 统一的错误码和处理机制

## 5. 测试策略

### 5.1 单元测试
- HAL层接口测试
- 服务层功能测试
- 应用层业务逻辑测试

### 5.2 集成测试
- 模块间接口测试
- 事件流测试
- 端到端功能测试

### 5.3 性能测试
- 事件响应时间
- 内存使用情况
- CPU占用率

## 6. 文档输出计划

### 6.1 开发文档
- [x] 08-详细开发计划.md
- [ ] 09-API接口文档.md
- [ ] 10-模块实现说明.md
- [ ] 11-测试报告.md

### 6.2 用户文档
- [ ] 12-使用说明.md
- [ ] 13-配置指南.md
- [ ] 14-故障排除.md

## 7. 风险控制

### 7.1 技术风险
- **依赖风险**: 增强版事件系统API可能需要适配
- **集成风险**: LVGL集成可能遇到兼容性问题
- **性能风险**: 单线程模型可能影响响应性能

### 7.2 进度风险
- **代码量风险**: 可能超出1000行限制
- **功能风险**: 某些功能可能需要简化
- **测试风险**: 测试时间可能不足

### 7.3 应对措施
- 严格控制每个模块的代码量
- 优先实现核心功能
- 预留缓冲时间用于调试

## 8. 下一步行动

### 8.1 立即执行
1. 创建项目目录结构
2. 编写基础头文件
3. 创建构建系统

### 8.2 开发顺序
1. HAL层 → 服务层 → 应用层 → 主程序
2. 每个阶段完成后进行测试
3. 持续集成和代码审查

---
**计划制定完成时间**: 2025-07-29 15:42  
**下一步**: 开始创建项目目录结构
