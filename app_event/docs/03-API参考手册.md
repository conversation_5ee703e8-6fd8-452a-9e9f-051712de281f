# API参考手册

**文档创建时间**: 2025-07-28 14:40  
**分析人员**: <PERSON> (全栈开发者)  

## 1. 事件系统API

### 1.1 初始化和清理

#### app_event_init()
```c
int app_event_init(void);
```
**功能**: 初始化事件系统  
**返回值**: 0成功，-1失败  
**说明**: 必须在使用其他事件API之前调用

#### app_event_deinit()
```c
void app_event_deinit(void);
```
**功能**: 清理事件系统资源  
**说明**: 程序退出前调用，释放所有资源

### 1.2 事件处理器管理

#### app_event_register()
```c
int app_event_register(app_event_type_t type, app_event_handler_t handler, void *user_data);
```
**功能**: 注册事件处理器  
**参数**:
- `type`: 事件类型
- `handler`: 处理函数指针
- `user_data`: 用户数据指针

**返回值**: 0成功，-1失败  
**示例**:
```c
app_event_register(APP_EVENT_COMM_DATA_RECEIVED, uart_event_handler, NULL);
```

#### app_event_unregister()
```c
int app_event_unregister(app_event_type_t type, app_event_handler_t handler);
```
**功能**: 取消注册事件处理器  
**参数**:
- `type`: 事件类型
- `handler`: 处理函数指针

**返回值**: 0成功，-1失败

### 1.3 事件发布和处理

#### app_event_post()
```c
int app_event_post(app_event_t *event);
```
**功能**: 发布事件到队列  
**参数**: `event`: 事件指针  
**返回值**: 0成功，-1失败  
**说明**: 异步发布，事件会被加入队列等待处理

#### app_event_post_sync()
```c
int app_event_post_sync(app_event_t *event);
```
**功能**: 同步发布事件  
**参数**: `event`: 事件指针  
**返回值**: 0成功，-1失败  
**说明**: 立即调用所有注册的处理器

#### app_event_process()
```c
void app_event_process(void);
```
**功能**: 处理事件队列  
**说明**: 在主循环中调用，处理队列中的所有事件

## 2. UART适配器API

### 2.1 初始化和配置

#### app_uart_init()
```c
int app_uart_init(const char *device, int baud_rate);
```
**功能**: 初始化UART适配器  
**参数**:
- `device`: 设备文件路径，如"/dev/ttyS0"
- `baud_rate`: 波特率，如115200

**返回值**: 0成功，-1失败  
**示例**:
```c
app_uart_init("/dev/ttyS0", 115200);
```

#### app_uart_deinit()
```c
void app_uart_deinit(void);
```
**功能**: 清理UART适配器资源

### 2.2 数据发送

#### app_uart_send()
```c
int app_uart_send(uint8_t id, uint8_t need_ack, const uint8_t *data, uint16_t data_len);
```
**功能**: 发送UART命令  
**参数**:
- `id`: 命令ID
- `need_ack`: 是否需要应答 (0/1)
- `data`: 数据指针
- `data_len`: 数据长度

**返回值**: 发送字节数，-1失败  
**示例**:
```c
uint8_t data[] = {0x01};
app_uart_send(0x56, 0, data, 1);
```

## 3. HiCar接口API

### 3.1 初始化和清理

#### hicar_interface_init()
```c
int hicar_interface_init(void);
```
**功能**: 初始化HiCar接口  
**返回值**: 0成功，-1失败

#### hicar_interface_deinit()
```c
void hicar_interface_deinit(void);
```
**功能**: 清理HiCar接口资源

### 3.2 命令处理

#### voice_ctrl_cmd()
```c
void voice_ctrl_cmd(e_voice_ctrl_command cmd);
```
**功能**: 接收HiCar语音控制命令  
**参数**: `cmd`: HiCar命令枚举值  
**说明**: 由HiCar系统调用，触发相应的车机控制

#### reg_car_state_report_callback()
```c
void reg_car_state_report_callback(car_state_report_callback_t callback);
```
**功能**: 注册车机状态报告回调函数  
**参数**: `callback`: 回调函数指针  
**说明**: HiCar系统通过此接口注册状态回调

#### report_car_state()
```c
void report_car_state(e_voice_ctrl_command state);
```
**功能**: 报告车机状态变化  
**参数**: `state`: 状态值  
**说明**: 向HiCar系统报告当前车机状态

## 4. 图标控制器API

### 4.1 系统管理

#### icon_controller_system_init()
```c
int icon_controller_system_init(void);
```
**功能**: 初始化图标控制器系统  
**返回值**: 0成功，-1失败

#### icon_controller_system_deinit()
```c
void icon_controller_system_deinit(void);
```
**功能**: 清理图标控制器系统资源

### 4.2 控制器管理

#### icon_controller_create()
```c
icon_controller_t* icon_controller_create(lv_obj_t *icon);
```
**功能**: 创建图标控制器  
**参数**: `icon`: LVGL图标对象  
**返回值**: 控制器指针，NULL失败

#### icon_controller_delete()
```c
void icon_controller_delete(icon_controller_t *controller);
```
**功能**: 删除图标控制器  
**参数**: `controller`: 控制器指针

### 4.3 状态控制

#### icon_set_state()
```c
int icon_set_state(icon_controller_t *controller, icon_state_t state);
```
**功能**: 设置图标状态  
**参数**:
- `controller`: 控制器指针
- `state`: 图标状态枚举

**返回值**: 0成功，-1失败  
**状态值**:
- `ICON_STATE_OFF`: 关闭
- `ICON_STATE_ON`: 常亮
- `ICON_STATE_BLINK_SLOW`: 慢闪
- `ICON_STATE_BLINK_FAST`: 快闪
- `ICON_STATE_DOUBLE_BLINK`: 双闪
- `ICON_STATE_PULSE`: 脉冲

#### icon_set_custom_pattern()
```c
int icon_set_custom_pattern(icon_controller_t *controller, icon_blink_pattern_t *pattern);
```
**功能**: 设置自定义闪烁模式  
**参数**:
- `controller`: 控制器指针
- `pattern`: 闪烁模式结构体指针

**返回值**: 0成功，-1失败

#### icon_set_timeout()
```c
int icon_set_timeout(icon_controller_t *controller, uint32_t timeout_ms);
```
**功能**: 设置图标定时关闭  
**参数**:
- `controller`: 控制器指针
- `timeout_ms`: 超时时间(毫秒)

**返回值**: 0成功，-1失败

#### icon_controller_update()
```c
void icon_controller_update(void);
```
**功能**: 更新所有图标控制器  
**说明**: 在主循环中调用，处理图标动画

## 5. 桥接模块API

### 5.1 初始化和清理

#### uart_hicar_bridge_init()
```c
int uart_hicar_bridge_init(void);
```
**功能**: 初始化UART-HiCar桥接模块  
**返回值**: 0成功，-1失败

#### uart_hicar_bridge_deinit()
```c
void uart_hicar_bridge_deinit(void);
```
**功能**: 清理桥接模块资源

### 5.2 处理函数

#### uart_hicar_bridge_process()
```c
void uart_hicar_bridge_process(void);
```
**功能**: 处理桥接模块逻辑  
**说明**: 在主循环中调用

### 5.3 图标注册

#### uart_hicar_bridge_register_icons()
```c
void uart_hicar_bridge_register_icons(lv_obj_t *left_turn, lv_obj_t *right_turn, 
                                     lv_obj_t *headlight, lv_obj_t *screen, 
                                     lv_obj_t *flash);
```
**功能**: 注册UI图标到桥接模块  
**参数**: 各种图标的LVGL对象指针  
**说明**: 桥接模块通过这些图标对象控制UI显示

## 6. 数据结构定义

### 6.1 事件结构

#### app_event_t
```c
typedef struct {
    app_event_type_t type;      // 事件类型
    uint32_t timestamp;         // 时间戳
    void *sender;              // 发送者
    void *data;                // 事件数据
    size_t data_size;          // 数据大小
} app_event_t;
```

#### app_event_type_t
```c
typedef enum {
    APP_EVENT_COMM_DATA_RECEIVED = 0x1001,  // UART数据接收
    APP_EVENT_BIZ_HICAR_COMMAND = 0x2001,   // HiCar命令
    APP_EVENT_BIZ_LIGHT_CHANGED = 0x2002,   // 灯光状态变化
    APP_EVENT_BIZ_VEHICLE_STATUS = 0x2003,  // 车辆状态
    APP_EVENT_BIZ_SPEED_INFO = 0x2004,      // 速度信息
    APP_EVENT_SYS_START = 0x3001,           // 系统启动
} app_event_type_t;
```

### 6.2 图标控制结构

#### icon_blink_pattern_t
```c
typedef struct {
    uint32_t on_time;         // 亮起时间(ms)
    uint32_t off_time;        // 熄灭时间(ms)
    uint8_t blink_count;      // 闪烁次数(0=无限)
    uint32_t group_interval;  // 组间隔时间(ms)
    uint32_t total_time;      // 总持续时间(ms)
} icon_blink_pattern_t;
```

### 6.3 HiCar命令枚举

#### e_voice_ctrl_command
```c
typedef enum {
    VC_CMD_LEFT_TURN_ON = 1,    // 左转灯开
    VC_CMD_LEFT_TURN_OFF = 2,   // 左转灯关
    VC_CMD_RIGHT_TURN_ON = 3,   // 右转灯开
    VC_CMD_RIGHT_TURN_OFF = 4,  // 右转灯关
    VC_CMD_HEADLIGHT_ON = 5,    // 大灯开
    VC_CMD_HEADLIGHT_OFF = 6,   // 大灯关
    VC_CMD_SCREEN_ON = 7,       // 屏幕开
    VC_CMD_SCREEN_OFF = 8,      // 屏幕关
    VC_CMD_FLASH_ON = 9,        // 双闪开
    VC_CMD_FLASH_OFF = 10,      // 双闪关
} e_voice_ctrl_command;
```

## 7. 使用示例

### 7.1 基本初始化流程
```c
int main() {
    // 1. 初始化LVGL
    lv_init();
    
    // 2. 初始化事件系统
    app_event_init();
    
    // 3. 初始化图标控制器
    icon_controller_system_init();
    
    // 4. 初始化UART适配器
    app_uart_init("/dev/ttyS0", 115200);
    
    // 5. 初始化HiCar接口
    hicar_interface_init();
    
    // 6. 初始化桥接模块
    uart_hicar_bridge_init();
    
    // 7. 主循环
    while (running) {
        lv_task_handler();
        app_event_process();
        uart_hicar_bridge_process();
        usleep(5000);
    }
    
    // 8. 清理资源
    cleanup_all();
    return 0;
}
```

### 7.2 发送UART命令
```c
// 发送左转灯开启命令
uint8_t data[] = {0x01};
int ret = app_uart_send(0x56, 0, data, 1);
if (ret < 0) {
    printf("发送失败\n");
}
```

### 7.3 处理HiCar命令
```c
// HiCar语音控制回调
void on_hicar_voice_command(e_voice_ctrl_command cmd) {
    voice_ctrl_cmd(cmd);  // 转发给系统处理
}
```

### 7.4 创建和控制图标
```c
// 创建图标控制器
lv_obj_t *icon = lv_img_create(parent);
icon_controller_t *ctrl = icon_controller_create(icon);

// 设置快闪状态
icon_set_state(ctrl, ICON_STATE_BLINK_FAST);

// 设置5秒后自动关闭
icon_set_timeout(ctrl, 5000);
```

---
**文档完成时间**: 2025-07-28 14:40  
**文档版本**: v1.0
