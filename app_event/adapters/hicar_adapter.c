/**
 * @file hicar_adapter.c
 * @brief HiCar适配器，连接HiCar接口与事件系统
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "../include/app_event.h"
#include "../include/app_adapter.h"
#include "../include/hicar_interface.h"
#include "../include/ui_icon_controller.h"

// 声明外部图标控制器变量
extern icon_controller_t *g_icon_left_turn;
extern icon_controller_t *g_icon_right_turn;
extern icon_controller_t *g_icon_warning;

// HiCar事件处理器
static void hicar_adapter_event_handler(app_event_t *event, void *user_data) {
    if (!event || !event->data) return;
    
    printf("HiCar适配器收到事件: 类型=0x%04X\n", event->type);
    
    switch (event->type) {
        case APP_EVENT_BIZ_LIGHT_CHANGED: {
            e_voice_ctrl_command *cmd = (e_voice_ctrl_command *)event->data;
            
            // 根据命令类型控制相应的图标
            switch (*cmd) {
                case VC_CMD_LIGHT_ON:
                    // 处理灯光开启
                    printf("HiCar适配器: 处理灯光开启命令\n");
                    break;
                    
                case VC_CMD_LIGHT_OFF:
                    // 处理灯光关闭
                    printf("HiCar适配器: 处理灯光关闭命令\n");
                    break;
                    
                case VC_CMD_FLASH_ON:
                    // 处理闪光灯开启
                    if (g_icon_warning) {
                        icon_set_state(g_icon_warning, ICON_STATE_BLINK_FAST);
                        printf("HiCar适配器: 闪光灯开启\n");
                    }
                    break;
                    
                case VC_CMD_FLASH_OFF:
                    // 处理闪光灯关闭
                    if (g_icon_warning) {
                        icon_set_state(g_icon_warning, ICON_STATE_OFF);
                        printf("HiCar适配器: 闪光灯关闭\n");
                    }
                    break;
                    
                case VC_CMD_LTS_ON:
                    // 处理左转向灯开启
                    if (g_icon_left_turn) {
                        icon_set_state(g_icon_left_turn, ICON_STATE_DOUBLE_BLINK);
                        printf("HiCar适配器: 左转向灯开启\n");
                        
                        // 报告车机状态变化
                        report_car_state(VC_CMD_LTS_ON);
                    }
                    break;
                    
                case VC_CMD_RTS_ON:
                    // 处理右转向灯开启
                    if (g_icon_right_turn) {
                        icon_set_state(g_icon_right_turn, ICON_STATE_DOUBLE_BLINK);
                        printf("HiCar适配器: 右转向灯开启\n");
                        
                        // 报告车机状态变化
                        report_car_state(VC_CMD_RTS_ON);
                    }
                    break;
                    
                case VC_CMD_TS_OFF:
                    // 处理转向灯关闭
                    if (g_icon_left_turn) {
                        icon_set_state(g_icon_left_turn, ICON_STATE_OFF);
                    }
                    if (g_icon_right_turn) {
                        icon_set_state(g_icon_right_turn, ICON_STATE_OFF);
                    }
                    printf("HiCar适配器: 转向灯关闭\n");
                    
                    // 报告车机状态变化
                    report_car_state(VC_CMD_TS_OFF);
                    break;
                    
                default:
                    printf("HiCar适配器: 未知的灯光命令: %d\n", *cmd);
                    break;
            }
            break;
        }
            
        case APP_EVENT_BIZ_SCREEN_CONTROL: {
            e_voice_ctrl_command *cmd = (e_voice_ctrl_command *)event->data;
            
            // 根据命令类型控制屏幕
            switch (*cmd) {
                case VC_CMD_SCREEN_ON:
                    printf("HiCar适配器: 处理屏幕开启命令\n");
                    // 这里可以添加屏幕开启的代码
                    break;
                    
                case VC_CMD_SCREEN_OFF:
                    printf("HiCar适配器: 处理屏幕关闭命令\n");
                    // 这里可以添加屏幕关闭的代码
                    break;
                    
                default:
                    printf("HiCar适配器: 未知的屏幕控制命令: %d\n", *cmd);
                    break;
            }
            break;
        }
            
        default:
            break;
    }
}

// 初始化HiCar适配器
static int hicar_adapter_init(void) {
    // 初始化HiCar接口
    if (hicar_interface_init() != 0) {
        printf("HiCar接口初始化失败\n");
        return -1;
    }
    
    // 注册事件处理器
    if (app_event_register(APP_EVENT_BIZ_LIGHT_CHANGED, hicar_adapter_event_handler, NULL) != 0 ||
        app_event_register(APP_EVENT_BIZ_SCREEN_CONTROL, hicar_adapter_event_handler, NULL) != 0) {
        printf("注册HiCar适配器事件处理器失败\n");
        hicar_interface_deinit();
        return -1;
    }
    
    printf("HiCar适配器初始化成功\n");
    return 0;
}

// 清理HiCar适配器
static void hicar_adapter_deinit(void) {
    // 取消注册事件处理器
    app_event_unregister(APP_EVENT_BIZ_LIGHT_CHANGED, hicar_adapter_event_handler);
    app_event_unregister(APP_EVENT_BIZ_SCREEN_CONTROL, hicar_adapter_event_handler);
    
    // 清理HiCar接口
    hicar_interface_deinit();
    
    printf("HiCar适配器资源已清理\n");
}

// 定义HiCar适配器
app_adapter_t hicar_adapter = {
    .name = "hicar",
    .init = hicar_adapter_init,
    .deinit = hicar_adapter_deinit
};

// 测试HiCar功能的回调函数
static void test_car_state_callback(e_voice_ctrl_command state) {
    printf("测试回调函数收到车机状态: %d\n", state);
}

// 测试HiCar功能
void test_hicar_functions(void) {
    // 注册车机状态报告回调函数
    reg_car_state_report_callback(test_car_state_callback);
    
    // 测试各种命令
    voice_ctrl_cmd(VC_CMD_LIGHT_ON);
    voice_ctrl_cmd(VC_CMD_LTS_ON);
    voice_ctrl_cmd(VC_CMD_RTS_ON);
    voice_ctrl_cmd(VC_CMD_TS_OFF);
    voice_ctrl_cmd(VC_CMD_FLASH_ON);
    voice_ctrl_cmd(VC_CMD_FLASH_OFF);
    voice_ctrl_cmd(VC_CMD_SCREEN_ON);
    voice_ctrl_cmd(VC_CMD_SCREEN_OFF);
} 