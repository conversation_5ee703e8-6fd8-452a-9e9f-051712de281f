/**
 * @file app_lvgl_adapter.c
 * @brief LVGL适配器实现
 * @version 1.0.0
 * @date 2025-06-18 19:32
 */

#include "../include/app_lvgl_adapter.h"
#include "../include/app_event.h"
#include "../include/app_system.h"
#include <stdlib.h>
#include <string.h>

// LVGL事件到app_event的映射
static void lvgl_event_cb(lv_event_t *e) {
    app_event_t event;
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *obj = lv_event_get_target(e);
    
    // 设置事件类型
    switch (code) {
        case LV_EVENT_CLICKED:
            event.type = APP_EVENT_UI_CLICK;
            break;
        case LV_EVENT_LONG_PRESSED:
            event.type = APP_EVENT_UI_LONG_PRESS;
            break;
        case LV_EVENT_VALUE_CHANGED:
            event.type = APP_EVENT_UI_VALUE_CHANGED;
            break;
        default:
            return; // 不处理的事件类型
    }
    
    // 设置事件数据
    event.timestamp = app_get_time();
    event.sender = obj;
    
    // 创建UI事件数据
    ui_event_data_t *data = malloc(sizeof(ui_event_data_t));
    if (data) {
        data->obj = obj;
        data->event_code = code;
        data->user_data = lv_obj_get_user_data(obj);
        
        event.data = data;
        event.data_size = sizeof(ui_event_data_t);
        
        // 发布事件
        app_event_post(&event);
    }
}

// 初始化LVGL适配器
static int lvgl_adapter_init(void) {
    // 注册LVGL事件处理器
    // 这里可以注册全局事件处理器或特定组件的处理器
    return 0;
}

// 清理LVGL适配器
static void lvgl_adapter_deinit(void) {
    // 清理资源
}

// LVGL适配器定义
app_adapter_t lvgl_adapter = {
    .name = "lvgl",
    .init = lvgl_adapter_init,
    .deinit = lvgl_adapter_deinit
};

// 为LVGL对象注册事件处理
void app_ui_register_events(lv_obj_t *obj) {
    lv_obj_add_event_cb(obj, lvgl_event_cb, LV_EVENT_ALL, NULL);
} 