/**
 * @file uart_adapter.c
 * @brief UART适配器实现，基于uart_api封装
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <setjmp.h>

#include "app/api/uart_api.h"
#include "app/utils/screen_utils.h"
#include "../include/app_uart_adapter.h"
#include "../include/app_event.h"
#include "include/uart_protocol.h"
#include "../include/app_system.h"

// 用于标记是否运行在模拟模式的全局变量 - 完全移除模拟模式
int g_uart_simulate_mode = 0;

// UART适配器私有数据
typedef struct {
    uart_protocol_handle_t handle;  // UART协议句柄
    int initialized;                // 初始化标志
} uart_adapter_private_t;

static uart_adapter_private_t g_uart_adapter = {0};

// UART消息回调函数
static void uart_message_callback(uint8_t id, const uint8_t *data, uint16_t data_len, void *user_data) 
{
    // 检查参数有效性
    if (data_len > 0 && !data) {
        printf("UART消息回调：无效参数，数据为NULL但长度为%d\n", data_len);
        return;
    }

    printf("UART消息回调：ID=0x%02X，长度=%d\n", id, data_len);

    // 创建帧接收事件
    app_event_t event;
    
    // 根据不同的命令ID设置不同的事件类型
    switch (id) {
        case UART_MSG_POWER_SYNC:
            event.type = APP_EVENT_SYS_START;
            break;
        case UART_MSG_HEARTBEAT:
            // 心跳包，可以不创建事件
            return;
        case UART_MSG_GEAR:
            event.type = APP_EVENT_BIZ_VEHICLE_STATUS;
            break;
        case UART_MSG_SPEED:
            event.type = APP_EVENT_BIZ_SPEED_INFO;
            break;
        case UART_MSG_RPM:
            event.type = APP_EVENT_BIZ_SPEED_INFO;  // 转速也归类为速度信息
            break;
        case UART_MSG_CONTROLLER_STATUS:
            event.type = APP_EVENT_BIZ_VEHICLE_STATUS;  // 中控控制器状态归类为车辆状态
            break;
        case UART_MSG_ACK:
            // 修改：ACK消息也创建COMM_DATA_RECEIVED事件
            event.type = APP_EVENT_COMM_DATA_RECEIVED;
            break;
        default:
            event.type = APP_EVENT_COMM_DATA_RECEIVED;
            break;
    }

    event.timestamp = app_get_time();
    event.sender = &g_uart_adapter;

    // 创建事件数据结构体
    typedef struct {
        uint8_t id;
        uint8_t data[256];
        uint16_t data_len;
    } uart_frame_data_t;

    uart_frame_data_t *frame_data = malloc(sizeof(uart_frame_data_t));
    if (!frame_data) {
        printf("UART消息回调：内存分配失败\n");
        return;
    }
    
    // 初始化结构体，避免未初始化的内存
    memset(frame_data, 0, sizeof(uart_frame_data_t));
    
    frame_data->id = id;
    frame_data->data_len = data_len;
    
    if (data && data_len > 0) {
        // 确保不会越界
        if (data_len > sizeof(frame_data->data)) {
            data_len = sizeof(frame_data->data);
            frame_data->data_len = data_len;
            printf("UART消息回调：数据长度过长，已截断至%d字节\n", data_len);
        }
        memcpy(frame_data->data, data, data_len);

        // 专门解析0x67中控控制器状态数据
        if (id == UART_MSG_CONTROLLER_STATUS && data_len >= 18) {
            printf("=== 中控控制器状态数据解析 ===\n");

            // 解析控制器状态1 (Data0)
            uint8_t status1 = data[0];
            printf("控制器状态1: 0x%02X\n", status1);
            printf("  - 一键修复: %s\n", (status1 & CTRL_STATUS1_ONE_KEY_REPAIR) ? "开启" : "关闭");
            printf("  - TCS状态: %s\n", (status1 & CTRL_STATUS1_TCS_STATUS) ? "开启" : "关闭");
            printf("  - 低电量延长续航: %s\n", (status1 & CTRL_STATUS1_LOW_POWER_EXTEND) ? "开启" : "关闭");
            printf("  - DSR感应: %s\n", (status1 & CTRL_STATUS1_DSR_SENSE) ? "开启" : "关闭");
            printf("  - 制动状态: %s\n", (status1 & CTRL_STATUS1_BRAKE_STATUS) ? "制动中" : "未制动");
            printf("  - P挡信号: %s\n", (status1 & CTRL_STATUS1_P_GEAR_SIGNAL) ? "P挡" : "非P挡");

            uint8_t work_mode = (status1 & CTRL_STATUS1_WORK_MODE_MASK) >> 6;
            const char *mode_str = "未知";
            switch (work_mode) {
                case 0: mode_str = "标准模式"; break;
                case 1: mode_str = "ECO模式"; break;
                case 2: mode_str = "POWER模式"; break;
                case 3: mode_str = "CRUISE模式"; break;
            }
            printf("  - 工作模式: %s\n", mode_str);

            // 解析控制器状态2 (Data1)
            uint8_t status2 = data[1];
            printf("控制器状态2: 0x%02X\n", status2);
            printf("  - 巡航状态: %s\n", (status2 & CTRL_STATUS2_CRUISE_STATUS) ? "开启" : "关闭");
            printf("  - 功能1状态: %s\n", (status2 & CTRL_STATUS2_FUNCTION1_STATUS) ? "开启" : "关闭");
            printf("  - 推车状态: %s\n", (status2 & CTRL_STATUS2_PUSH_STATUS) ? "推车中" : "正常");
            printf("  - 倒车状态: %s\n", (status2 & CTRL_STATUS2_REVERSE_STATUS) ? "倒车中" : "正常");
            printf("  - 超车: %s\n", (status2 & CTRL_STATUS2_OVERTAKE) ? "超车中" : "正常");
            printf("  - 边撑: %s\n", (status2 & CTRL_STATUS2_SIDE_STAND) ? "支起" : "收起");
            printf("  - 长按P挡: %s\n", (status2 & CTRL_STATUS2_LONG_PRESS_P) ? "是" : "否");
            printf("  - 刹车故障: %s\n", (status2 & CTRL_STATUS2_BRAKE_FAULT) ? "故障" : "正常");

            // 解析电池电压状态 (Data2)
            uint8_t battery_status = data[2];
            uint8_t voltage_type = battery_status & BATTERY_VOLTAGE_MASK;
            const char *voltage_str = "未知";
            switch (voltage_type) {
                case BATTERY_VOLTAGE_36V: voltage_str = "36V"; break;
                case BATTERY_VOLTAGE_48V: voltage_str = "48V"; break;
                case BATTERY_VOLTAGE_60V: voltage_str = "60V"; break;
                case BATTERY_VOLTAGE_72V: voltage_str = "72V"; break;
                case BATTERY_VOLTAGE_84V: voltage_str = "84V"; break;
                case BATTERY_VOLTAGE_96V: voltage_str = "96V"; break;
            }
            printf("电池电压类型: %s\n", voltage_str);
            printf("双欠压选择: %s\n", (battery_status & BATTERY_DOUBLE_UNDERVOLT) ? "开启" : "关闭");
            printf("车型: %s\n", (battery_status & BATTERY_VEHICLE_TYPE) ? "电摩" : "电自");

            // 解析温度 (Data3)
            uint8_t temp_raw = data[3];
            int16_t temperature = temp_raw - TEMP_OFFSET;
            printf("控制器温度: %d°C (原始值: %d)\n", temperature, temp_raw);

            // 解析母线电压 (Data4-5)
            uint16_t bus_voltage = (data[4] << 8) | data[5];
            printf("母线电压: %.1fV (原始值: %d)\n", bus_voltage / 10.0f, bus_voltage);

            // 解析母线电流 (Data6-7)
            uint16_t bus_current_raw = (data[6] << 8) | data[7];
            int16_t bus_current = bus_current_raw - CURRENT_OFFSET;
            printf("母线电流: %.1fA (原始值: %d)\n", bus_current / 10.0f, bus_current_raw);

            // 解析故障代码1 (Data8)
            uint8_t fault1 = data[8];
            printf("故障代码1: 0x%02X\n", fault1);
            if (fault1 != 0) {
                printf("  故障详情:\n");
                if (fault1 & FAULT_CODE1_HIGH_TEMP) printf("    - 高温保护\n");
                if (fault1 & FAULT_CODE1_STALL_PROTECT) printf("    - 堵转保护\n");
                if (fault1 & FAULT_CODE1_OVERCURRENT) printf("    - 过流保护\n");
                if (fault1 & FAULT_CODE1_UNDERVOLT) printf("    - 欠压保护\n");
                if (fault1 & FAULT_CODE1_THROTTLE_FAULT) printf("    - 转把故障\n");
                if (fault1 & FAULT_CODE1_MOTOR_HALL_FAULT) printf("    - 电机霍尔故障\n");
                if (fault1 & FAULT_CODE1_MOTOR_PHASE_LOSS) printf("    - 电机缺相\n");
                if (fault1 & FAULT_CODE1_CONTROLLER_FAULT) printf("    - 控制器故障\n");
            } else {
                printf("  无故障\n");
            }

            // 解析故障代码2 (Data9)
            uint8_t fault2 = data[9];
            printf("故障代码2: 0x%02X (预留)\n", fault2);

            // 解析铅酸SOC (Data10)
            uint8_t soc = data[10];
            if (soc == INVALID_VALUE_FF) {
                printf("铅酸SOC: 不支持\n");
            } else if (soc == INVALID_VALUE_FE) {
                printf("铅酸SOC: 异常值\n");
            } else {
                printf("铅酸SOC: %d%%\n", soc);
            }

            // 解析转把电压 (Data11-12)
            uint16_t throttle_voltage = (data[11] << 8) | data[12];
            if (throttle_voltage == INVALID_VALUE_FFFF) {
                printf("转把电压: 无效值\n");
            } else if (throttle_voltage == INVALID_VALUE_FFFE) {
                printf("转把电压: 异常值\n");
            } else {
                printf("转把电压: %dmV\n", throttle_voltage);
            }

            printf("================================\n");
        }
    }

    event.data = frame_data;
    event.data_size = sizeof(uart_frame_data_t);

    // 发布事件
    int ret = app_event_post(&event);
    if (ret != 0) {
        printf("事件发布失败：类型=%d, ID=0x%02X\n", event.type, id);
        free(frame_data);
    } else {
        printf("事件已发布：类型=%d, ID=0x%02X\n", event.type, id);
    }
}

// UART适配器初始化函数
static int uart_adapter_init(void) {
    // 这里不做实际初始化，由app_uart_init函数完成
    return 0;
}

// UART适配器定时处理函数
static void uart_adapter_process(void) {
    // 这里可以添加定期执行的任务，如发送心跳包等
    static uint32_t last_heartbeat_time = 0;
    uint32_t current_time = app_get_time();

    // 每3秒发送一次心跳包
    if (g_uart_adapter.initialized && g_uart_adapter.handle && 
        (current_time - last_heartbeat_time >= 3000)) {
        
        printf("发送心跳包...\n");
        uart_error_t ret = uart_protocol_send_heartbeat_msg(g_uart_adapter.handle);
        printf("心跳包发送结果: %d\n", ret);
        last_heartbeat_time = current_time;
    }
}

// UART适配器清理函数
static void uart_adapter_deinit(void) {
    app_uart_deinit();
}

// UART适配器定义
static app_adapter_t uart_adapter = {
    .name = "uart",
    .init = uart_adapter_init,
    .process = uart_adapter_process,
    .deinit = uart_adapter_deinit
};

// 获取UART适配器实例
app_adapter_t* app_uart_get_adapter(void) {
    return &uart_adapter;
}

// 初始化UART适配器
int app_uart_init(const char *device, int baud_rate) {
    printf("UART适配器初始化开始，设备: %s, 波特率: %d\n", device, baud_rate);
    
    if (!device || baud_rate <= 0) {
        printf("无效参数: 设备: %s, 波特率: %d\n", device ? device : "NULL", baud_rate);
        return -1;
    }

    // 检查设备文件是否存在
    if (access(device, F_OK) != 0) {
        printf("设备文件不存在: %s\n", device);
        printf("UART初始化失败\n");
        return -1;
    }

    // 如果已经初始化，先清理
    if (g_uart_adapter.initialized) {
        printf("UART适配器已初始化，先清理\n");
        app_uart_deinit();
    }

    // 初始化UART协议栈
    printf("正在初始化UART协议栈...\n");
    g_uart_adapter.handle = uart_protocol_init(device, baud_rate, uart_message_callback, NULL);
    if (!g_uart_adapter.handle) {
        printf("UART协议栈初始化失败\n");
        return -1;
    }
    printf("UART协议栈初始化成功\n");

    g_uart_adapter.initialized = 1;
    g_uart_simulate_mode = 0;  // 确保模拟模式关闭

    // 发送上电同步信息
    printf("发送上电同步信息\n");
    uart_error_t ret = uart_protocol_send_power_sync_msg(g_uart_adapter.handle, 0);
    if (ret != UART_ERROR_NONE) {
        printf("上电同步信息发送失败: %d\n", ret);
    } else {
        printf("上电同步信息发送成功\n");
    }

    printf("UART适配器初始化完成\n");
    return 0;
}

// 发送UART数据
int app_uart_send(uint8_t id, uint8_t need_ack, const uint8_t *data, uint8_t data_len) {
    printf("UART发送数据: ID=0x%02X, 长度=%d\n", id, data_len);
    fflush(stdout);
    
    if (!g_uart_adapter.initialized) {
        printf("UART适配器未初始化\n");
        fflush(stdout);
        return -1;
    }

    if (!g_uart_adapter.handle) {
        printf("UART句柄无效，设备可能不存在或初始化失败\n");
        fflush(stdout);
        return -1;
    }
    
    // 检查数据有效性
    if (data_len > 0 && !data) {
        printf("无效参数: 数据为NULL但长度为%d\n", data_len);
        fflush(stdout);
        return -1;
    }

    // 根据不同的ID调用不同的API函数
    uart_error_t ret = UART_ERROR_NONE;
    
    // 使用try-catch风格的错误处理
    jmp_buf jmpbuf;
    if (setjmp(jmpbuf) != 0) {
        // 捕获到错误
        printf("UART发送数据时发生异常\n");
        fflush(stdout);
        return -1;
    }
    
    // 尝试发送数据
    switch (id) {
        case UART_MSG_POWER_SYNC:
            printf("发送上电同步消息\n");
            fflush(stdout);
            ret = uart_protocol_send_power_sync_msg(g_uart_adapter.handle, need_ack);
            break;
        case UART_MSG_HEARTBEAT:
            printf("发送心跳包\n");
            fflush(stdout);
            ret = uart_protocol_send_heartbeat_msg(g_uart_adapter.handle);
            break;
        case UART_MSG_GEAR:
            if (data && data_len > 0) {
                printf("发送挡位消息: %d\n", data[0]);
                fflush(stdout);
                ret = uart_protocol_send_gear_msg(g_uart_adapter.handle, data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_SPEED:
            if (data && data_len > 0) {
                printf("发送车速消息: %d km/h\n", data[0]);
                fflush(stdout);
                ret = uart_protocol_send_speed_msg(g_uart_adapter.handle, data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_RPM:
            if (data && data_len >= 2) {
                uint16_t rpm = (data[0] << 8) | data[1];  // 高字节在前
                printf("发送转速消息: %d RPM\n", rpm);
                fflush(stdout);
                ret = uart_protocol_send_rpm_msg(g_uart_adapter.handle, rpm);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_LIGHT:
            if (data && data_len > 0) {
                printf("发送灯光控制消息: %d\n", data[0]);
                fflush(stdout);
                ret = uart_protocol_send_light_ctrl_msg(g_uart_adapter.handle, need_ack, (light_control_cmd_t)data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_LIGHT_SCREEN_CTRL:
            if (data && data_len > 0) {
                // 灯光控制参数必须存在
                light_control_cmd_t light_ctrl = (data_len >= 1) ? (light_control_cmd_t)data[0] : 0;
                // 屏幕控制参数可选
                screen_control_cmd_t screen_ctrl = (data_len >= 2) ? (screen_control_cmd_t)data[1] : 0;

                // 屏幕控制
                if (screen_ctrl == SCREEN_CTRL_OFF) {
                    printf("[硬件控制] -> 关闭屏幕\n");
                    screen_turn_off();
                }
                if (screen_ctrl == SCREEN_CTRL_ON) {
                    printf("[硬件控制] -> 打开屏幕\n");
                    screen_turn_on();
                }
                
                printf("发送灯光和屏幕控制消息: 灯光=%d, 屏幕=%d\n", light_ctrl, screen_ctrl);
                fflush(stdout);
                
                // 直接发送命令，不再使用模拟模式
                ret = uart_protocol_send_light_screen_ctrl_msg(g_uart_adapter.handle, need_ack, 
                                                             light_ctrl, screen_ctrl);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_REMAIN_RANGE:
            if (data && data_len > 0) {
                printf("发送剩余续航里程消息: %d km\n", data[0]);
                fflush(stdout);
                ret = uart_protocol_send_remain_range_msg(g_uart_adapter.handle, data[0]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_MILEAGE:
            if (data && data_len >= 8) {
                uint32_t total_mileage = (data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3];
                uint32_t trip_mileage = (data[4] << 24) | (data[5] << 16) | (data[6] << 8) | data[7];
                printf("发送里程消息: 总里程=%u, 小计=%u\n", total_mileage, trip_mileage);
                fflush(stdout);
                ret = uart_protocol_send_mileage_msg(g_uart_adapter.handle, total_mileage, trip_mileage);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_FACTORY_RESET:
            printf("发送工厂复位消息\n");
            fflush(stdout);
            ret = uart_protocol_send_factory_reset_msg(g_uart_adapter.handle, need_ack);
            break;
        case UART_MSG_MCU_VERSION:
            if (data && data_len > 0) {
                // 确保字符串以'\0'结尾
                char *version = (char *)malloc(data_len + 1);
                if (version) {
                    memcpy(version, data, data_len);
                    version[data_len] = '\0';
                    printf("发送MCU版本消息: %s\n", version);
                    fflush(stdout);
                    ret = uart_protocol_send_mcu_version_msg(g_uart_adapter.handle, version);
                    free(version);
                } else {
                    ret = UART_ERROR_MEMORY;
                }
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_TIME:
            if (data && data_len >= 6) {
                printf("发送时间消息: %02d-%02d-%02d %02d:%02d:%02d\n",
                       data[0], data[1], data[2], data[3], data[4], data[5]);
                fflush(stdout);
                ret = uart_protocol_send_time_msg(g_uart_adapter.handle, data[0], data[1], data[2],
                                                data[3], data[4], data[5]);
            } else {
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        case UART_MSG_CONTROLLER_STATUS:
            if (data && data_len >= 18) {
                printf("发送中控控制器状态消息: 18字节数据\n");
                fflush(stdout);
                ret = uart_protocol_send_controller_status_msg(g_uart_adapter.handle, data);
            } else {
                printf("中控控制器状态数据长度不足: 需要18字节，实际%d字节\n", data_len);
                fflush(stdout);
                ret = UART_ERROR_INVALID_PARAM;
            }
            break;
        default:
            printf("未支持的消息ID: %02X\n", id);
            fflush(stdout);
            return -1;
    }

    printf("UART发送结果: %d\n", ret);
    fflush(stdout);
    return (ret == UART_ERROR_NONE) ? data_len : -1;
}

// 停止UART适配器
int app_uart_deinit(void) {
    if (!g_uart_adapter.initialized) {
        return 0;
    }

    if (g_uart_adapter.handle) {
        uart_protocol_deinit(g_uart_adapter.handle);
        g_uart_adapter.handle = NULL;
    }

    g_uart_adapter.initialized = 0;
    g_uart_simulate_mode = 0;
    
    printf("UART适配器已停止\n");
    return 0;
}