/**
 * @file app_examples.h
 * @brief 示例头文件
 * @version 2.0.0
 * @date 2025-06-18 15:47
 */

#ifndef APP_EXAMPLES_H
#define APP_EXAMPLES_H

#include <stdint.h>

#include "lvgl/lvgl.h"
#include "../include/app_event.h"
#include "../include/app_adapter.h"
#include "../include/app_system.h"

// 全局字体
#include "app/fonts/PuHui.h"

#ifdef __cplusplus
extern "C" {
#endif

// 全局样式变量
extern lv_style_t g_style;

// 初始化全局样式
void app_examples_init_style(void);

//===============================================

  // 简单UI测试相关函数

  void simple_test_event_handler(app_event_t *event, void *user_data);

  // 按钮点击事件回调
  void simple_btn_event_cb(lv_event_t *e);

    // 模拟定时器事件发送
  void simple_send_timer_event(void);

  // 简单UI示例初始化
  void simple_test_init(void);
  

//===============================================

  // UART 测试相关函数
  
  // UART事件处理函数
  void uart_event_handler(app_event_t *event, void *user_data);

  // 测试各种帧发送
  void uart_frame_send(void);

  // UART示例测试入口
  void uart_example_init(void);

//===============================================

  // LVGL UART演示相关函数

  // LVGL UART示例初始化
  void lvgl_uart_demo_init(void);

  // LVGL UART示例处理函数
  void lvgl_uart_demo_process(void);

//===============================================

  // LVGL HiCar UART综合演示相关函数
  
  // UART左转向灯按钮事件回调
  void uart_left_event_cb(lv_event_t *e);
  
  // UART右转向灯按钮事件回调
  void uart_right_event_cb(lv_event_t *e);
  
  // UART大灯开启按钮事件回调
  void uart_light_on_event_cb(lv_event_t *e);
  
  // UART大灯关闭按钮事件回调
  void uart_light_off_event_cb(lv_event_t *e);
  
  // UART全部关闭按钮事件回调
  void uart_all_off_event_cb(lv_event_t *e);
  
  // HiCar左转向灯按钮事件回调
  void hicar_left_event_cb(lv_event_t *e);
  
  // HiCar右转向灯按钮事件回调
  void hicar_right_event_cb(lv_event_t *e);
  
  // HiCar大灯按钮事件回调
  void hicar_light_event_cb(lv_event_t *e);
  
  // HiCar关闭所有按钮事件回调
  void hicar_off_event_cb(lv_event_t *e);
  
  // HiCar事件处理函数
  void hicar_event_handler(app_event_t *event, void *user_data);
  
  // UART事件处理函数
  void uart_event_handler(app_event_t *event, void *user_data);
  
  // LVGL HiCar UART综合演示初始化
  void lvgl_hicar_uart_demo_init(void);
  
  // LVGL HiCar UART综合演示处理函数
  void lvgl_hicar_uart_demo_process(void);
  
  // LVGL HiCar UART综合演示清理函数
  void lvgl_hicar_uart_demo_deinit(void);
  
  // 注册LVGL HiCar UART综合演示
  void lvgl_hicar_uart_demo_register(void);
  
  // 获取LVGL HiCar UART综合演示名称
  const char* lvgl_hicar_uart_demo_get_name(void);

//===============================================

  // 图标控制器演示相关函数

  // 初始化图标控制器演示
  void icon_controller_demo_init(void);

  // 更新图标控制器演示(在主循环中调用)
  void icon_controller_demo_update(void);

//===============================================

  // HiCar演示相关函数

  // 初始化HiCar演示
  void hicar_demo_init(void);

  // 创建HiCar演示UI
  void hicar_demo_create_ui(void);

  // 显示HiCar演示界面
  void hicar_demo_show(void);

  // 更新HiCar演示(在主循环中调用)
  void hicar_demo_update(void);

  // 清理HiCar演示资源
  void hicar_demo_deinit(void);

//===============================================

  // UART-HiCar桥接 演示相关函数

  // 演示初始化
  void uart_hicar_demo_init(void);

  // 演示处理
  void uart_hicar_demo_process(void);

  // 演示清理
  void uart_hicar_demo_deinit(void);


#ifdef __cplusplus
}
#endif

#endif /* APP_EXAMPLES_H */ 