/**
 * @file app_examples.c
 * @brief 示例共享资源
 * @version 2.0.0
 * @date 2025-06-18 15:47
 */

#include "app_examples.h"
#include "../include/uart_hicar_bridge.h"
#include "../include/app_uart_adapter.h"
#include "../include/hicar_interface.h"
#include "../include/ui_icon_controller.h"

// 全局样式变量，用于示例程序
lv_style_t g_style;

// 初始化全局样式
void app_examples_init_style(void) {
    lv_style_init(&g_style);
    lv_style_set_text_font(&g_style, &PuHui16);
    lv_style_set_text_color(&g_style, lv_color_hex(0xFFFFFF));
    lv_obj_add_style(lv_scr_act(), &g_style, LV_PART_MAIN);
}

//===============================================
// UART-HiCar桥接 演示相关函数

// 声明来自uart_hicar_demo.c的外部函数
extern void uart_hicar_demo_init(void);
extern void uart_hicar_demo_process(void);
extern void uart_hicar_demo_deinit(void);