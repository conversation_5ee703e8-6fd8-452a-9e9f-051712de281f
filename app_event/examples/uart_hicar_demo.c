/**
 * @file uart_hicar_demo.c
 * @brief UART-HiCar桥接功能演示程序
 * @version 1.0.0
 * @date 2025-06-18 19:32
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <stdint.h>

// 在编译时通过模拟实现提供这些头文件的内容
#include "lvgl/lvgl.h"
#include "app/api/uart_api.h"
#include "../include/app_event.h"
#include "../include/app_uart_adapter.h"
#include "../include/hicar_interface.h"
#include "../include/uart_hicar_bridge.h"
#include "../include/ui_icon_controller.h"
#include "../include/controller_status_ui_handler.h"

// LVGL类型现在通过头文件包含获得，无需重复声明

// 所有类型定义现在通过头文件包含获得

// 函数声明现在通过头文件包含获得

// 函数原型声明
static void signal_handler(int sig);
static void mock_uart_command(uint8_t id, uint8_t param);
static void mock_hicar_command(e_voice_ctrl_command cmd);
static void mock_controller_status_command(void);  // 新增：模拟0x67数据
static void btn_event_cb(lv_event_t *e);
static void tab_changed_event_cb(lv_event_t *e);
static void create_ui(void);
static void create_controller_status_icons(lv_obj_t *parent);  // 新增：创建中控状态图标
static void init_controller_status_system(void);  // 新增：初始化中控状态系统
void uart_hicar_demo_init(void);
void uart_hicar_demo_process(void);
void uart_hicar_demo_deinit(void);

// 全局变量
static lv_obj_t *g_main_screen = NULL;
static lv_obj_t *g_left_turn_icon = NULL;
static lv_obj_t *g_right_turn_icon = NULL;
static lv_obj_t *g_headlight_icon = NULL;
static lv_obj_t *g_screen_icon = NULL;
static lv_obj_t *g_flash_icon = NULL;  // 新增双闪图标
static lv_obj_t *g_status_label = NULL;
static lv_obj_t *g_tabview = NULL;
static lv_obj_t *g_uart_panel = NULL;
static lv_obj_t *g_hicar_panel = NULL;

// 新增：中控控制器状态图标
static lv_obj_t *g_tcs_icon = NULL;        // TCS状态图标
static lv_obj_t *g_cruise_icon = NULL;     // 巡航状态图标
static lv_obj_t *g_eco_mode_icon = NULL;   // ECO模式图标
static lv_obj_t *g_power_mode_icon = NULL; // POWER模式图标
static lv_obj_t *g_brake_icon = NULL;      // 制动状态图标
static lv_obj_t *g_p_gear_icon = NULL;     // P挡信号图标
static lv_obj_t *g_charging_icon = NULL;   // 充电状态图标
static lv_obj_t *g_fault_icon = NULL;      // 故障指示图标
static lv_obj_t *g_battery_low_icon = NULL;// 低电量图标
static lv_obj_t *g_reverse_icon = NULL;    // 倒车状态图标
static lv_obj_t *g_push_icon = NULL;       // 推车状态图标

// 图标控制器
static icon_controller_t *g_ctrl_tcs = NULL;
static icon_controller_t *g_ctrl_cruise = NULL;
static icon_controller_t *g_ctrl_eco_mode = NULL;
static icon_controller_t *g_ctrl_power_mode = NULL;
static icon_controller_t *g_ctrl_brake = NULL;
static icon_controller_t *g_ctrl_p_gear = NULL;
static icon_controller_t *g_ctrl_charging = NULL;
static icon_controller_t *g_ctrl_fault = NULL;
static icon_controller_t *g_ctrl_battery_low = NULL;
static icon_controller_t *g_ctrl_reverse = NULL;
static icon_controller_t *g_ctrl_push = NULL;


// 模拟发送UART命令
static void mock_uart_command(uint8_t id, uint8_t param) {
    printf("发送UART协议帧: ID=0x%02X, 参数=0x%02X\n", id, param);
    
    // 准备数据
    uint8_t data[1] = {param};
    
    // 使用app_uart_send发送符合协议的数据
    int ret = app_uart_send(id, 0, data, 1);
    
    if (ret < 0) {
        printf("UART数据发送失败: %d\n", ret);
        lv_label_set_text(g_status_label, "UART发送失败，请检查设备连接");
    } else {
        printf("UART数据发送成功: ID=0x%02X, 参数=0x%02X\n", id, param);
    }
}

// 模拟发送UART命令（带屏幕控制参数）
static void mock_uart_light_screen_command(uint8_t light_param, uint8_t screen_param) {
    printf("发送UART协议帧: ID=0x56, 灯光参数=0x%02X, 屏幕参数=0x%02X\n", light_param, screen_param);
    
    // 准备数据
    uint8_t data[2] = {light_param, screen_param};
    
    // 使用app_uart_send发送符合协议的数据
    int ret = app_uart_send(UART_MSG_LIGHT_SCREEN_CTRL, 0, data, 2);
    
    if (ret < 0) {
        printf("UART数据发送失败: %d\n", ret);
        lv_label_set_text(g_status_label, "UART发送失败，请检查设备连接");
    } else {
        printf("UART数据发送成功: ID=0x56, 灯光控制=0x%02X, 屏幕控制=0x%02X\n", 
               light_param, screen_param);
    }
}

// 模拟发送HiCar命令
static void mock_hicar_command(e_voice_ctrl_command cmd) {
    printf("模拟发送HiCar命令: %d\n", cmd);
    
    // 创建HiCar命令事件
    app_event_t hicar_event;
    hicar_event.type = APP_EVENT_BIZ_HICAR_COMMAND;
    hicar_event.timestamp = app_get_time();
    hicar_event.sender = NULL;
    
    // 创建命令数据
    e_voice_ctrl_command *cmd_data = malloc(sizeof(e_voice_ctrl_command));
    if (!cmd_data) {
        printf("内存分配失败\n");
        return;
    }
    
    *cmd_data = cmd;
    hicar_event.data = cmd_data;
    hicar_event.data_size = sizeof(e_voice_ctrl_command);
    
    // 发布事件
    app_event_post(&hicar_event);
}

// 模拟发送0x67中控控制器状态数据
static void mock_controller_status_command(void) {
    printf("发送0x67中控控制器状态数据\n");

    // 创建模拟的中控控制器状态数据
    static int demo_state = 0;
    uint8_t controller_data[18];

    switch (demo_state % 4) {
        case 0: // 正常运行状态
            printf("模拟状态：正常运行 (TCS开启, ECO模式, 巡航开启, 充电中)\n");
            controller_data[0] = 0x42;   // TCS开启 + ECO模式
            controller_data[1] = 0x01;   // 巡航开启
            controller_data[2] = 0x02;   // 48V电池
            controller_data[3] = 60;     // 20°C
            controller_data[4] = 0x01;   // 母线电压高字节
            controller_data[5] = 0x90;   // 母线电压低字节 (40.0V)
            controller_data[6] = 0x03;   // 母线电流高字节
            controller_data[7] = 0xE8;   // 母线电流低字节 (10.0A)
            controller_data[8] = 0x00;   // 无故障
            controller_data[9] = 0x00;   // 预留
            controller_data[10] = 85;    // 85% SOC
            controller_data[11] = 0x13;  // 转把电压高字节
            controller_data[12] = 0x88;  // 转把电压低字节 (5000mV)
            controller_data[13] = 0x41;  // 坐垫感应+软启动
            controller_data[14] = 0x00;  // 查询命令
            controller_data[15] = 0x01;  // 充电中
            controller_data[16] = 70;    // 70kg
            controller_data[17] = 0x01;  // 平稳
            break;

        case 1: // 故障状态
            printf("模拟状态：故障状态 (制动中, POWER模式, 倒车, 故障报警, 低电量)\n");
            controller_data[0] = 0x90;   // 制动状态 + POWER模式
            controller_data[1] = 0x88;   // 倒车状态
            controller_data[2] = 0x03;   // 60V电池
            controller_data[3] = 95;     // 55°C
            controller_data[4] = 0x02;   // 母线电压高字节
            controller_data[5] = 0x58;   // 母线电压低字节 (60.0V)
            controller_data[6] = 0x05;   // 母线电流高字节
            controller_data[7] = 0xDC;   // 母线电流低字节 (50.0A)
            controller_data[8] = 0x01;   // 高温保护故障
            controller_data[9] = 0x00;   // 预留
            controller_data[10] = 15;    // 15% SOC (低电量)
            controller_data[11] = 0xFF;  // 转把电压无效
            controller_data[12] = 0xFF;  // 转把电压无效
            controller_data[13] = 0x00;  // 所有功能关闭
            controller_data[14] = 0x80;  // 设置命令
            controller_data[15] = 0x40;  // 飞车保护开启
            controller_data[16] = 0xFF;  // 体重无效
            controller_data[17] = 0x03;  // 严重颠簸
            break;

        case 2: // 推车模式
            printf("模拟状态：推车模式 (TCS开启, 推车状态)\n");
            controller_data[0] = 0x02;   // TCS开启
            controller_data[1] = 0x04;   // 推车状态
            controller_data[2] = 0x02;   // 48V电池
            controller_data[3] = 45;     // 5°C
            controller_data[4] = 0x01;   // 母线电压高字节
            controller_data[5] = 0x90;   // 母线电压低字节 (40.0V)
            controller_data[6] = 0x03;   // 母线电流高字节
            controller_data[7] = 0xE8;   // 母线电流低字节 (10.0A)
            controller_data[8] = 0x00;   // 无故障
            controller_data[9] = 0x00;   // 预留
            controller_data[10] = 60;    // 60% SOC
            controller_data[11] = 0x10;  // 转把电压高字节
            controller_data[12] = 0x00;  // 转把电压低字节 (4096mV)
            controller_data[13] = 0x10;  // 推车功能开启
            controller_data[14] = 0x00;  // 查询命令
            controller_data[15] = 0x00;  // 未充电
            controller_data[16] = 75;    // 75kg
            controller_data[17] = 0x02;  // 轻微颠簸
            break;

        case 3: // P挡状态
            printf("模拟状态：P挡状态 (P挡信号, CRUISE模式)\n");
            controller_data[0] = 0xE0;   // P挡信号 + CRUISE模式
            controller_data[1] = 0x00;   // 正常状态
            controller_data[2] = 0x02;   // 48V电池
            controller_data[3] = 50;     // 10°C
            controller_data[4] = 0x01;   // 母线电压高字节
            controller_data[5] = 0x90;   // 母线电压低字节 (40.0V)
            controller_data[6] = 0x03;   // 母线电流高字节
            controller_data[7] = 0xE8;   // 母线电流低字节 (10.0A)
            controller_data[8] = 0x00;   // 无故障
            controller_data[9] = 0x00;   // 预留
            controller_data[10] = 50;    // 50% SOC
            controller_data[11] = 0x0F;  // 转把电压高字节
            controller_data[12] = 0xA0;  // 转把电压低字节 (4000mV)
            controller_data[13] = 0x20;  // 边撑感应开启
            controller_data[14] = 0x00;  // 查询命令
            controller_data[15] = 0x00;  // 未充电
            controller_data[16] = 80;    // 80kg
            controller_data[17] = 0x01;  // 平稳
            break;
    }

    // 使用app_uart_send发送0x67数据
    int ret = app_uart_send(0x67, 0, controller_data, 18);

    if (ret < 0) {
        printf("0x67数据发送失败: %d\n", ret);
        lv_label_set_text(g_status_label, "0x67数据发送失败，请检查设备连接");
    } else {
        printf("0x67数据发送成功，图标系统将自动响应\n");
        lv_label_set_text(g_status_label, "已发送0x67中控控制器状态数据");
    }

    demo_state++;
}

// Tab切换事件回调
static void tab_changed_event_cb(lv_event_t *e) {
    lv_obj_t *tabview = lv_event_get_target(e);
    uint16_t tab_id = lv_tabview_get_tab_act(tabview);
    
    if (tab_id == 0) {
        lv_label_set_text(g_status_label, "UART控制面板");
    } else {
        lv_label_set_text(g_status_label, "HiCar控制面板");
    }
}

// 按钮事件回调函数
static void btn_event_cb(lv_event_t *e) {
    lv_obj_t *btn = lv_event_get_target(e);
    const char *btn_text = lv_obj_get_user_data(btn);
    
    if (strcmp(btn_text, "左转向开") == 0) {
        // 激活左转向图标
        lv_obj_clear_state(g_left_turn_icon, LV_STATE_DISABLED);
        lv_obj_set_style_bg_color(g_left_turn_icon, lv_color_hex(0x00FF00), LV_PART_MAIN);
        
        if (lv_obj_get_parent(btn) == g_uart_panel) {
            mock_uart_command(UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_LEFT_TURN_ON);
            lv_label_set_text(g_status_label, "发送UART命令: 左转向灯开启");
        } else {
            mock_hicar_command(VC_CMD_LTS_ON);
            lv_label_set_text(g_status_label, "发送HiCar命令: 左转向灯开启");
        }
    } else if (strcmp(btn_text, "右转向开") == 0) {
        // 激活右转向图标
        lv_obj_clear_state(g_right_turn_icon, LV_STATE_DISABLED);
        lv_obj_set_style_bg_color(g_right_turn_icon, lv_color_hex(0x00FF00), LV_PART_MAIN);
        
        if (lv_obj_get_parent(btn) == g_uart_panel) {
            mock_uart_command(UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_RIGHT_TURN_ON);
            lv_label_set_text(g_status_label, "发送UART命令: 右转向灯开启");
        } else {
            mock_hicar_command(VC_CMD_RTS_ON);
            lv_label_set_text(g_status_label, "发送HiCar命令: 右转向灯开启");
        }
    } else if (strcmp(btn_text, "转向关闭") == 0) {
        // 禁用左右转向图标
        lv_obj_add_state(g_left_turn_icon, LV_STATE_DISABLED);
        lv_obj_add_state(g_right_turn_icon, LV_STATE_DISABLED);
        
        if (lv_obj_get_parent(btn) == g_uart_panel) {
            mock_uart_command(UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_TURN_OFF);
            lv_label_set_text(g_status_label, "发送UART命令: 转向灯关闭");
        } else {
            mock_hicar_command(VC_CMD_TS_OFF);
            lv_label_set_text(g_status_label, "发送HiCar命令: 转向灯关闭");
        }
    } else if (strcmp(btn_text, "大灯开启") == 0) {
        // 激活大灯图标
        lv_obj_clear_state(g_headlight_icon, LV_STATE_DISABLED);
        lv_obj_set_style_bg_color(g_headlight_icon, lv_color_hex(0xFFFF00), LV_PART_MAIN);
        
        if (lv_obj_get_parent(btn) == g_uart_panel) {
            mock_uart_command(UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_HEADLIGHT_ON);
            lv_label_set_text(g_status_label, "发送UART命令: 大灯开启");
        } else {
            mock_hicar_command(VC_CMD_LIGHT_ON);
            lv_label_set_text(g_status_label, "发送HiCar命令: 大灯开启");
        }
    } else if (strcmp(btn_text, "大灯关闭") == 0) {
        // 禁用大灯图标
        lv_obj_add_state(g_headlight_icon, LV_STATE_DISABLED);
        
        if (lv_obj_get_parent(btn) == g_uart_panel) {
            // 大灯关闭使用转向灯关闭命令，因为UART协议没有专门的大灯关闭命令
            mock_uart_command(UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_TURN_OFF);
            lv_label_set_text(g_status_label, "发送UART命令: 大灯关闭");
        } else {
            mock_hicar_command(VC_CMD_LIGHT_OFF);
            lv_label_set_text(g_status_label, "发送HiCar命令: 大灯关闭");
        }
    } else if (strcmp(btn_text, "双闪开启") == 0) {
        // 激活双闪图标
        lv_obj_clear_state(g_flash_icon, LV_STATE_DISABLED);
        lv_obj_set_style_bg_color(g_flash_icon, lv_color_hex(0xFF0000), LV_PART_MAIN);
        
        if (lv_obj_get_parent(btn) == g_uart_panel) {
            mock_uart_command(UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_DOUBLE_FLASH_ON);
            lv_label_set_text(g_status_label, "发送UART命令: 双闪开启");
        } else {
            mock_hicar_command(VC_CMD_FLASH_ON);
            lv_label_set_text(g_status_label, "发送HiCar命令: 双闪开启");
        }
    } else if (strcmp(btn_text, "双闪关闭") == 0) {
        // 禁用双闪图标
        lv_obj_add_state(g_flash_icon, LV_STATE_DISABLED);
        
        if (lv_obj_get_parent(btn) == g_uart_panel) {
            mock_uart_command(UART_MSG_LIGHT_SCREEN_CTRL, LIGHT_CTRL_DOUBLE_FLASH_OFF);
            lv_label_set_text(g_status_label, "发送UART命令: 双闪关闭");
        } else {
            mock_hicar_command(VC_CMD_FLASH_OFF);
            lv_label_set_text(g_status_label, "发送HiCar命令: 双闪关闭");
        }
    } else if (strcmp(btn_text, "屏幕开启") == 0) {
        // 激活屏幕图标
        lv_obj_clear_state(g_screen_icon, LV_STATE_DISABLED);
        lv_obj_set_style_bg_color(g_screen_icon, lv_color_hex(0x0000FF), LV_PART_MAIN);
        
        if (lv_obj_get_parent(btn) == g_uart_panel) {
            mock_uart_light_screen_command(0, SCREEN_CTRL_ON);
            lv_label_set_text(g_status_label, "发送UART命令: 屏幕开启");
        } else {
            mock_hicar_command(VC_CMD_SCREEN_ON);
            lv_label_set_text(g_status_label, "发送HiCar命令: 屏幕开启");
        }
    } else if (strcmp(btn_text, "屏幕关闭") == 0) {
        // 禁用屏幕图标
        lv_obj_add_state(g_screen_icon, LV_STATE_DISABLED);
        
        if (lv_obj_get_parent(btn) == g_uart_panel) {
            mock_uart_light_screen_command(0, SCREEN_CTRL_OFF);
            lv_label_set_text(g_status_label, "发送UART命令: 屏幕关闭");
        } else {
            mock_hicar_command(VC_CMD_SCREEN_OFF);
            lv_label_set_text(g_status_label, "发送HiCar命令: 屏幕关闭");
        }
    } else if (strcmp(btn_text, "发送0x67数据") == 0) {
        // 发送0x67中控控制器状态数据
        mock_controller_status_command();
    }
}

// 创建按钮函数
static lv_obj_t* create_button(lv_obj_t *parent, const char *text, lv_coord_t x, lv_coord_t y) {
    lv_obj_t *btn = lv_btn_create(parent);
    lv_obj_set_size(btn, 140, 50);
    lv_obj_set_pos(btn, x, y);
    lv_obj_set_style_radius(btn, 10, LV_PART_MAIN);
    lv_obj_set_style_bg_color(btn, lv_color_hex(0x505050), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(btn, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_border_width(btn, 1, LV_PART_MAIN);
    lv_obj_set_style_border_color(btn, lv_color_hex(0x606060), LV_PART_MAIN);
    
    // 设置按钮用户数据为按钮文本，用于事件回调中识别
    char *btn_text = malloc(strlen(text) + 1);
    if (btn_text) {
        strcpy(btn_text, text);
        lv_obj_set_user_data(btn, btn_text);
    }
    
    lv_obj_t *label = lv_label_create(btn);
    lv_label_set_text(label, text);
    lv_obj_set_style_text_color(label, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_center(label);
    
    lv_obj_add_event_cb(btn, btn_event_cb, LV_EVENT_CLICKED, NULL);
    
    return btn;
}

static lv_obj_t* create_icon_frame(lv_obj_t *parent, int w, int h) {
  lv_obj_t *icon_frame = lv_obj_create(parent);
  if (w == 0 || h == 0) {
    lv_obj_set_size(icon_frame, LV_PCT(100), LV_PCT(100));
  } else {
    lv_obj_set_size(icon_frame, w, h);
  }
  lv_obj_set_style_bg_opa(icon_frame, LV_OPA_TRANSP, LV_PART_MAIN);
  // lv_obj_set_style_border_color(icon_frame, lv_color_hex(0xFF3E56), LV_PART_MAIN | LV_STATE_DEFAULT);
  // lv_obj_set_style_border_width(icon_frame, 1, LV_PART_MAIN | LV_STATE_DEFAULT);
  lv_obj_set_style_pad_all(icon_frame, 0, LV_PART_MAIN);
  lv_obj_clear_flag(icon_frame, LV_OBJ_FLAG_SCROLLABLE);
  
  // 使用flex布局
  lv_obj_set_layout(icon_frame, LV_LAYOUT_FLEX);
  lv_obj_set_flex_flow(icon_frame, LV_FLEX_FLOW_ROW);
  lv_obj_set_flex_align(icon_frame, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER); 
  
  return icon_frame;
}

// 创建中控控制器状态图标
static void create_controller_status_icons(lv_obj_t *parent) {
    // 创建中控状态图标面板
    lv_obj_t *status_panel = lv_obj_create(parent);
    lv_obj_set_size(status_panel, 780, 120);
    lv_obj_set_pos(status_panel, 10, 10);
    lv_obj_set_style_bg_color(status_panel, lv_color_hex(0x202020), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(status_panel, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_border_width(status_panel, 1, LV_PART_MAIN);
    lv_obj_set_style_border_color(status_panel, lv_color_hex(0x404040), LV_PART_MAIN);
    lv_obj_set_style_radius(status_panel, 5, LV_PART_MAIN);

    // 添加标题
    lv_obj_t *title_label = lv_label_create(status_panel);
    lv_label_set_text(title_label, "中控控制器状态图标 (0x67数据实时响应)");
    lv_obj_set_style_text_color(title_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_set_pos(title_label, 10, 5);

    // 创建图标容器
    lv_obj_t *icons_container = lv_obj_create(status_panel);
    lv_obj_set_size(icons_container, 760, 70);
    lv_obj_set_pos(icons_container, 10, 30);
    lv_obj_set_style_bg_opa(icons_container, LV_OPA_TRANSP, LV_PART_MAIN);
    lv_obj_set_style_border_width(icons_container, 0, LV_PART_MAIN);
    lv_obj_set_flex_flow(icons_container, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(icons_container, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);

    // 创建各个状态图标
    // TCS状态图标 (蓝色)
    lv_obj_t *tcs_frame = create_icon_frame(icons_container, 50, 50);
    g_tcs_icon = lv_obj_create(tcs_frame);
    lv_obj_set_size(g_tcs_icon, 48, 48);
    lv_obj_set_style_bg_color(g_tcs_icon, lv_color_hex(0x0080FF), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_tcs_icon, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(g_tcs_icon, 24, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_tcs_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_tcs_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_add_state(g_tcs_icon, LV_STATE_DISABLED);

    // 巡航状态图标 (绿色)
    lv_obj_t *cruise_frame = create_icon_frame(icons_container, 50, 50);
    g_cruise_icon = lv_obj_create(cruise_frame);
    lv_obj_set_size(g_cruise_icon, 48, 48);
    lv_obj_set_style_bg_color(g_cruise_icon, lv_color_hex(0x00FF80), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_cruise_icon, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(g_cruise_icon, 24, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_cruise_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_cruise_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_add_state(g_cruise_icon, LV_STATE_DISABLED);

    // ECO模式图标 (浅绿色)
    lv_obj_t *eco_frame = create_icon_frame(icons_container, 50, 50);
    g_eco_mode_icon = lv_obj_create(eco_frame);
    lv_obj_set_size(g_eco_mode_icon, 48, 48);
    lv_obj_set_style_bg_color(g_eco_mode_icon, lv_color_hex(0x80FF00), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_eco_mode_icon, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(g_eco_mode_icon, 24, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_eco_mode_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_eco_mode_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_add_state(g_eco_mode_icon, LV_STATE_DISABLED);

    // POWER模式图标 (红色)
    lv_obj_t *power_frame = create_icon_frame(icons_container, 50, 50);
    g_power_mode_icon = lv_obj_create(power_frame);
    lv_obj_set_size(g_power_mode_icon, 48, 48);
    lv_obj_set_style_bg_color(g_power_mode_icon, lv_color_hex(0xFF4000), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_power_mode_icon, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(g_power_mode_icon, 24, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_power_mode_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_power_mode_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_add_state(g_power_mode_icon, LV_STATE_DISABLED);

    // 制动状态图标 (橙色)
    lv_obj_t *brake_frame = create_icon_frame(icons_container, 50, 50);
    g_brake_icon = lv_obj_create(brake_frame);
    lv_obj_set_size(g_brake_icon, 48, 48);
    lv_obj_set_style_bg_color(g_brake_icon, lv_color_hex(0xFF8000), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_brake_icon, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(g_brake_icon, 24, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_brake_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_brake_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_add_state(g_brake_icon, LV_STATE_DISABLED);

    // P挡信号图标 (紫色)
    lv_obj_t *p_gear_frame = create_icon_frame(icons_container, 50, 50);
    g_p_gear_icon = lv_obj_create(p_gear_frame);
    lv_obj_set_size(g_p_gear_icon, 48, 48);
    lv_obj_set_style_bg_color(g_p_gear_icon, lv_color_hex(0x8000FF), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_p_gear_icon, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(g_p_gear_icon, 24, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_p_gear_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_p_gear_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_add_state(g_p_gear_icon, LV_STATE_DISABLED);

    // 充电状态图标 (青色)
    lv_obj_t *charging_frame = create_icon_frame(icons_container, 50, 50);
    g_charging_icon = lv_obj_create(charging_frame);
    lv_obj_set_size(g_charging_icon, 48, 48);
    lv_obj_set_style_bg_color(g_charging_icon, lv_color_hex(0x00FFFF), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_charging_icon, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(g_charging_icon, 24, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_charging_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_charging_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_add_state(g_charging_icon, LV_STATE_DISABLED);

    // 故障指示图标 (深红色)
    lv_obj_t *fault_frame = create_icon_frame(icons_container, 50, 50);
    g_fault_icon = lv_obj_create(fault_frame);
    lv_obj_set_size(g_fault_icon, 48, 48);
    lv_obj_set_style_bg_color(g_fault_icon, lv_color_hex(0xFF0040), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_fault_icon, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(g_fault_icon, 24, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_fault_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_fault_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_add_state(g_fault_icon, LV_STATE_DISABLED);

    // 添加图标说明
    lv_obj_t *icon_label = lv_label_create(status_panel);
    lv_label_set_text(icon_label, "TCS    巡航    ECO    POWER   制动    P挡    充电    故障");
    lv_obj_set_style_text_color(icon_label, lv_color_hex(0xC0C0C0), LV_PART_MAIN);
    lv_obj_set_pos(icon_label, 20, 100);
}

// 初始化中控控制器状态系统
static void init_controller_status_system(void) {
    printf("初始化中控控制器状态系统...\n");

    // 初始化中控控制器状态UI处理器
    if (controller_status_ui_init(NULL) != 0) {
        printf("中控控制器状态UI处理器初始化失败\n");
        return;
    }

    // 创建图标控制器
    g_ctrl_tcs = icon_controller_create(g_tcs_icon);
    g_ctrl_cruise = icon_controller_create(g_cruise_icon);
    g_ctrl_eco_mode = icon_controller_create(g_eco_mode_icon);
    g_ctrl_power_mode = icon_controller_create(g_power_mode_icon);
    g_ctrl_brake = icon_controller_create(g_brake_icon);
    g_ctrl_p_gear = icon_controller_create(g_p_gear_icon);
    g_ctrl_charging = icon_controller_create(g_charging_icon);
    g_ctrl_fault = icon_controller_create(g_fault_icon);
    g_ctrl_battery_low = icon_controller_create(g_battery_low_icon);
    g_ctrl_reverse = icon_controller_create(g_reverse_icon);
    g_ctrl_push = icon_controller_create(g_push_icon);

    // 检查创建结果
    if (!g_ctrl_tcs || !g_ctrl_cruise || !g_ctrl_eco_mode || !g_ctrl_power_mode ||
        !g_ctrl_brake || !g_ctrl_p_gear || !g_ctrl_charging || !g_ctrl_fault ||
        !g_ctrl_battery_low || !g_ctrl_reverse || !g_ctrl_push) {
        printf("图标控制器创建失败\n");
        return;
    }

    // 注册图标控制器到状态处理器
    controller_status_ui_set_icon("tcs", g_ctrl_tcs);
    controller_status_ui_set_icon("cruise", g_ctrl_cruise);
    controller_status_ui_set_icon("eco_mode", g_ctrl_eco_mode);
    controller_status_ui_set_icon("power_mode", g_ctrl_power_mode);
    controller_status_ui_set_icon("brake", g_ctrl_brake);
    controller_status_ui_set_icon("p_gear", g_ctrl_p_gear);
    controller_status_ui_set_icon("charging", g_ctrl_charging);
    controller_status_ui_set_icon("fault", g_ctrl_fault);
    controller_status_ui_set_icon("battery_low", g_ctrl_battery_low);
    controller_status_ui_set_icon("reverse", g_ctrl_reverse);
    controller_status_ui_set_icon("push", g_ctrl_push);

    printf("中控控制器状态系统初始化完成\n");
}

// 创建UI界面
static void create_ui(void) {
    // 创建主屏幕
    g_main_screen = lv_obj_create(lv_scr_act());
    lv_obj_set_size(g_main_screen, 1024, 600);
    lv_obj_set_pos(g_main_screen, 0, 0);
    lv_obj_set_style_bg_color(g_main_screen, lv_color_hex(0x303030), LV_PART_MAIN);
    lv_obj_clear_flag(g_main_screen, LV_OBJ_FLAG_SCROLLABLE);
    
    // 使用flex布局
    lv_obj_set_layout(g_main_screen, LV_LAYOUT_FLEX);
    lv_obj_set_flex_flow(g_main_screen, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(g_main_screen, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_pad_all(g_main_screen, 20, LV_PART_MAIN);
    lv_obj_set_style_pad_row(g_main_screen, 20, LV_PART_MAIN);
    
    // 创建标题容器
    lv_obj_t *title_container = lv_obj_create(g_main_screen);
    lv_obj_set_size(title_container, 900, 60);
    lv_obj_set_style_bg_color(title_container, lv_color_hex(0x404040), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(title_container, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(title_container, 10, LV_PART_MAIN);
    lv_obj_set_style_pad_all(title_container, 10, LV_PART_MAIN);
    lv_obj_clear_flag(title_container, LV_OBJ_FLAG_SCROLLABLE);
    
    // 使用flex布局
    lv_obj_set_layout(title_container, LV_LAYOUT_FLEX);
    lv_obj_set_flex_flow(title_container, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(title_container, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    
    // 创建标题
    lv_obj_t *title = lv_label_create(title_container);
    lv_label_set_text(title, "UART-HiCar桥接演示");
    lv_obj_set_style_text_color(title, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    
    // 创建状态标签
    g_status_label = lv_label_create(title_container);
    lv_label_set_text(g_status_label, "准备就绪");
    lv_obj_set_style_text_color(g_status_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN);

    // 创建中控控制器状态图标面板
    create_controller_status_icons(g_main_screen);
    
    // 创建TabView控件
    g_tabview = lv_tabview_create(g_main_screen, LV_DIR_TOP, 50);
    lv_obj_set_size(g_tabview, 900, 300);
    lv_obj_set_style_bg_color(g_tabview, lv_color_hex(0x404040), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_tabview, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(g_tabview, 10, LV_PART_MAIN);
    
    // 设置TabView的按钮样式
    lv_obj_t *tab_btns = lv_tabview_get_tab_btns(g_tabview);
    lv_obj_set_style_bg_color(tab_btns, lv_color_hex(0x505050), LV_PART_MAIN);
    lv_obj_set_style_text_color(tab_btns, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_set_style_border_width(tab_btns, 0, LV_PART_MAIN);
    
    // 添加UART和HiCar两个页面
    g_uart_panel = lv_tabview_add_tab(g_tabview, "UART控制");
    g_hicar_panel = lv_tabview_add_tab(g_tabview, "HiCar控制");
    
    // 设置两个面板的样式
    lv_obj_set_style_bg_color(g_uart_panel, lv_color_hex(0x404040), LV_PART_MAIN);
    lv_obj_set_style_bg_color(g_hicar_panel, lv_color_hex(0x404040), LV_PART_MAIN);
    lv_obj_clear_flag(g_uart_panel, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_clear_flag(g_hicar_panel, LV_OBJ_FLAG_SCROLLABLE);
    
    // 添加Tab切换事件回调
    lv_obj_add_event_cb(g_tabview, tab_changed_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    
    // 在UART面板上创建按钮
    create_button(g_uart_panel, "左转向开", 30, 20);
    create_button(g_uart_panel, "右转向开", 190, 20);
    create_button(g_uart_panel, "转向关闭", 350, 20);
    create_button(g_uart_panel, "大灯开启", 510, 20);
    create_button(g_uart_panel, "大灯关闭", 670, 20);
    
    create_button(g_uart_panel, "双闪开启", 30, 90);
    create_button(g_uart_panel, "双闪关闭", 190, 90);
    create_button(g_uart_panel, "屏幕开启", 350, 90);
    create_button(g_uart_panel, "屏幕关闭", 510, 90);

    // 添加0x67数据发送按钮
    create_button(g_uart_panel, "发送0x67数据", 670, 90);
    
    // 在HiCar面板上创建按钮
    create_button(g_hicar_panel, "左转向开", 30, 20);
    create_button(g_hicar_panel, "右转向开", 190, 20);
    create_button(g_hicar_panel, "转向关闭", 350, 20);
    create_button(g_hicar_panel, "大灯开启", 510, 20);
    create_button(g_hicar_panel, "大灯关闭", 670, 20);
    
    create_button(g_hicar_panel, "双闪开启", 30, 90);
    create_button(g_hicar_panel, "双闪关闭", 190, 90);
    create_button(g_hicar_panel, "屏幕开启", 350, 90);
    create_button(g_hicar_panel, "屏幕关闭", 510, 90);
    
    // 创建图标面板
    lv_obj_t *icon_panel = lv_obj_create(g_main_screen);
    lv_obj_set_size(icon_panel, 900, 160);
    lv_obj_set_style_bg_color(icon_panel, lv_color_hex(0x404040), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(icon_panel, LV_OPA_COVER, LV_PART_MAIN);
    lv_obj_set_style_radius(icon_panel, 10, LV_PART_MAIN);
    lv_obj_set_style_pad_all(icon_panel, 20, LV_PART_MAIN);
    lv_obj_clear_flag(icon_panel, LV_OBJ_FLAG_SCROLLABLE);
    
    // 使用flex布局
    lv_obj_set_layout(icon_panel, LV_LAYOUT_FLEX);
    lv_obj_set_flex_flow(icon_panel, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(icon_panel, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    
    // 创建图标容器
    lv_obj_t *icons_container = create_icon_frame(icon_panel, 860, 80);
    
    // 创建图标
    lv_obj_t *_left_turn_icon = create_icon_frame(icons_container, 62, 62);
    g_left_turn_icon = lv_obj_create(_left_turn_icon);
    lv_obj_set_size(g_left_turn_icon, 60, 60);
    lv_obj_set_style_bg_color(g_left_turn_icon, lv_color_hex(0x00FF00), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_left_turn_icon, LV_OPA_COVER, LV_PART_MAIN);  // 确保背景不透明
    lv_obj_set_style_radius(g_left_turn_icon, 30, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_left_turn_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_left_turn_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_clear_flag(g_left_turn_icon, LV_OBJ_FLAG_HIDDEN); // 确保图标可见
    
    lv_obj_t *_right_turn_icon = create_icon_frame(icons_container, 62, 62);
    g_right_turn_icon = lv_obj_create(_right_turn_icon);
    lv_obj_set_size(g_right_turn_icon, 60, 60);
    lv_obj_set_style_bg_color(g_right_turn_icon, lv_color_hex(0x00FF00), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_right_turn_icon, LV_OPA_COVER, LV_PART_MAIN);  // 确保背景不透明
    lv_obj_set_style_radius(g_right_turn_icon, 30, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_right_turn_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_right_turn_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_clear_flag(g_right_turn_icon, LV_OBJ_FLAG_HIDDEN); // 确保图标可见
    
    lv_obj_t *_headlight_icon = create_icon_frame(icons_container, 62, 62);
    g_headlight_icon = lv_obj_create(_headlight_icon);
    lv_obj_set_size(g_headlight_icon, 60, 60);
    lv_obj_set_style_bg_color(g_headlight_icon, lv_color_hex(0xFFFF00), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_headlight_icon, LV_OPA_COVER, LV_PART_MAIN);  // 确保背景不透明
    lv_obj_set_style_radius(g_headlight_icon, 30, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_headlight_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_headlight_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_clear_flag(g_headlight_icon, LV_OBJ_FLAG_HIDDEN); // 确保图标可见
    
    lv_obj_t *_screen_icon = create_icon_frame(icons_container, 62, 62);
    g_screen_icon = lv_obj_create(_screen_icon);
    lv_obj_set_size(g_screen_icon, 60, 60);
    lv_obj_set_style_bg_color(g_screen_icon, lv_color_hex(0x0000FF), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_screen_icon, LV_OPA_COVER, LV_PART_MAIN);  // 确保背景不透明
    lv_obj_set_style_radius(g_screen_icon, 30, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_screen_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_screen_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_clear_flag(g_screen_icon, LV_OBJ_FLAG_HIDDEN); // 确保图标可见
    
    lv_obj_t *_flash_icon = create_icon_frame(icons_container, 62, 62);
    g_flash_icon = lv_obj_create(_flash_icon);
    lv_obj_set_size(g_flash_icon, 60, 60);
    lv_obj_set_style_bg_color(g_flash_icon, lv_color_hex(0xFF0000), LV_PART_MAIN);
    lv_obj_set_style_bg_opa(g_flash_icon, LV_OPA_COVER, LV_PART_MAIN);  // 确保背景不透明
    lv_obj_set_style_radius(g_flash_icon, 30, LV_PART_MAIN);
    lv_obj_set_style_border_width(g_flash_icon, 2, LV_PART_MAIN);
    lv_obj_set_style_border_color(g_flash_icon, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    lv_obj_clear_flag(g_flash_icon, LV_OBJ_FLAG_HIDDEN); // 确保图标可见
    
    // 添加图标说明
    lv_obj_t *icon_label = lv_label_create(icon_panel);
    lv_label_set_text(icon_label, "左转灯                            右转灯                            大灯                               屏幕                              双闪  ");
    lv_obj_set_style_text_color(icon_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN);
    
    // 注册图标到桥接模块 - 添加双闪图标
    uart_hicar_bridge_register_icons(g_left_turn_icon, g_right_turn_icon, g_headlight_icon, g_screen_icon, g_flash_icon);
    
    // 初始状态下，所有图标都是关闭状态，但可见
    lv_obj_add_state(g_left_turn_icon, LV_STATE_DISABLED);
    lv_obj_add_state(g_right_turn_icon, LV_STATE_DISABLED);
    lv_obj_add_state(g_headlight_icon, LV_STATE_DISABLED);
    lv_obj_add_state(g_screen_icon, LV_STATE_DISABLED);
    lv_obj_add_state(g_flash_icon, LV_STATE_DISABLED);
}

// 演示初始化
void uart_hicar_demo_init(void) {
    
    // 初始化事件系统
    app_event_init();
    
    // 初始化图标控制器系统
    icon_controller_system_init();
    
    // 初始化UART适配器
    app_uart_init("/dev/ttyS0", 115200);
    
    // 初始化HiCar接口
    hicar_interface_init();
    
    // 初始化UART-HiCar桥接模块
    uart_hicar_bridge_init();
    
    // 创建UI界面
    create_ui();

    // 初始化中控控制器状态系统
    init_controller_status_system();

    printf("UART-HiCar桥接演示初始化完成\n");
}

// 演示处理
void uart_hicar_demo_process(void) {
    // 处理事件队列
    app_event_process();
    
    // 处理UART-HiCar桥接模块
    uart_hicar_bridge_process();
}

// 演示清理
void uart_hicar_demo_deinit(void) {
    // 清理UART-HiCar桥接模块
    uart_hicar_bridge_deinit();
    
    // 清理HiCar接口
    hicar_interface_deinit();
    
    // 清理UART适配器
    app_uart_deinit();

    // 清理中控控制器状态系统
    controller_status_ui_deinit();

    // 清理图标控制器系统
    icon_controller_system_deinit();
    
    // 清理事件系统
    app_event_deinit();
    
    printf("UART-HiCar桥接演示资源已清理\n");
}


#ifdef EXAMPLE_UART_HICAR_DEMO

static int g_running = 1;

// 信号处理函数
static void signal_handler(int sig) {
    g_running = 0;
}

// 主函数
int main(int argc, char *argv[]) {
    
    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化LVGL
    lv_init();
    
    // 初始化演示
    uart_hicar_demo_init();
    
    // 主循环
    while (g_running) {
        // 处理演示逻辑
        uart_hicar_demo_process();
        
        // 处理LVGL任务
        lv_task_handler();
        
        // 延时
        usleep(5000);
    }
    
    // 清理演示
    uart_hicar_demo_deinit();
    
    return 0;
} 

#endif