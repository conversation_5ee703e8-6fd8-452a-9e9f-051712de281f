/**
 * @file uart_hicar_bridge.h
 * @brief UART与HiCar之间的桥接模块，实现双向命令转换和UI更新
 * @version 1.0.0
 * @date 2025-06-18 19:32
 */

#ifndef UART_HICAR_BRIDGE_H
#define UART_HICAR_BRIDGE_H

#include <stdint.h>
#include "lvgl/lvgl.h"
#include "../include/app_event.h"
#include "../include/hicar_interface.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化UART-HiCar桥接模块
 * @return 0表示成功，非0表示失败
 */
int uart_hicar_bridge_init(void);

/**
 * @brief 处理UART-HiCar桥接模块的事件和逻辑
 */
void uart_hicar_bridge_process(void);

/**
 * @brief 清理UART-HiCar桥接模块的资源
 */
void uart_hicar_bridge_deinit(void);

/**
 * @brief 注册UI图标，用于状态显示
 * @param left_turn_icon 左转向灯图标
 * @param right_turn_icon 右转向灯图标
 * @param headlight_icon 大灯图标
 * @param screen_icon 屏幕图标
 * @param flash_icon 双闪图标（可选，可为NULL）
 */
void uart_hicar_bridge_register_icons(lv_obj_t *left_turn_icon, 
                                     lv_obj_t *right_turn_icon,
                                     lv_obj_t *headlight_icon,
                                     lv_obj_t *screen_icon,
                                     lv_obj_t *flash_icon);

#ifdef __cplusplus
}
#endif

#endif /* UART_HICAR_BRIDGE_H */ 