/**
 * @file hicar_interface.h
 * @brief 仪表UI与HiCar交互的接口定义
 */

#ifndef HICAR_INTERFACE_H
#define HICAR_INTERFACE_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 仪表UI与HiCar交互的数据类型（不是仪表与车机的交互）
 */
typedef enum {
    VC_CMD_SCREEN_ON,  // 屏幕开启
    VC_CMD_SCREEN_OFF, // 屏幕关闭
    VC_CMD_LIGHT_ON,   // 灯光开启
    VC_CMD_LIGHT_OFF,  // 灯光关闭
    VC_CMD_FLASH_ON,   // 闪光灯开启
    VC_CMD_FLASH_OFF,  // 闪光灯关闭
    VC_CMD_LTS_ON,     // 左转向灯开启
    VC_CMD_RTS_ON,     // 右转向灯开启
    VC_CMD_TS_OFF      // 转向灯关闭
} e_voice_ctrl_command;

/**
 * @brief 车机状态报告回调函数类型
 */
typedef void (*car_state_report_callback_t)(e_voice_ctrl_command state);

/**
 * @brief HiCar调用此接口去控制车机
 * 
 * @param cmd 控制指令
 */
void voice_ctrl_cmd(e_voice_ctrl_command cmd);

/**
 * @brief HiCar调用此接口向仪表UI系统注册回调函数
 * 
 * @param callback 车机状态报告回调函数
 */
void reg_car_state_report_callback(car_state_report_callback_t callback);

/**
 * @brief 初始化HiCar接口
 * 
 * @return 0表示成功，非0表示失败
 */
int hicar_interface_init(void);

/**
 * @brief 清理HiCar接口资源
 */
void hicar_interface_deinit(void);

/**
 * @brief 报告车机状态变化
 * 
 * @param state 车机状态
 */
void report_car_state(e_voice_ctrl_command state);

#ifdef __cplusplus
}
#endif

#endif /* HICAR_INTERFACE_H */ 