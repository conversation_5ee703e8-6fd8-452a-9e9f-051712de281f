/**
 * @file app_uart_adapter.h
 * @brief UART适配器头文件
 */

#ifndef APP_UART_ADAPTER_H
#define APP_UART_ADAPTER_H

#include <stdint.h>
#include "app_adapter.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化UART适配器
 * @param device 串口设备路径，如"/dev/ttyS0"
 * @param baud_rate 波特率
 * @return 0:成功 其他:失败
 */
int app_uart_init(const char *device, int baud_rate);

/**
 * @brief 发送UART数据
 * @param id 命令ID
 * @param need_ack 是否需要应答
 * @param data 数据指针
 * @param data_len 数据长度
 * @return 发送的字节数，错误返回负值
 */
int app_uart_send(uint8_t id, uint8_t need_ack, const uint8_t *data, uint8_t data_len);

/**
 * @brief 停止UART适配器
 * @return 0:成功 其他:失败
 */
int app_uart_deinit(void);

/**
 * @brief 获取UART适配器实例
 * @return UART适配器实例
 */
app_adapter_t* app_uart_get_adapter(void);

#ifdef __cplusplus
}
#endif

#endif /* APP_UART_ADAPTER_H */