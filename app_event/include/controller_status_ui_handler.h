/**
 * @file controller_status_ui_handler.h
 * @brief 中控控制器状态UI处理器头文件
 * @version 1.0.0
 * @date 2025-07-22
 */

#ifndef CONTROLLER_STATUS_UI_HANDLER_H
#define CONTROLLER_STATUS_UI_HANDLER_H

#include <stdint.h>
#include "ui_icon_controller.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化中控控制器状态UI处理器
 * @param icon_objects 包含各种图标LVGL对象的结构体指针
 * @return 0成功，负值失败
 */
int controller_status_ui_init(void *icon_objects);

/**
 * @brief 设置图标控制器指针
 * @param icon_type 图标类型字符串
 * @param controller 图标控制器指针
 * 
 * 支持的图标类型：
 * - "tcs"          : TCS状态图标
 * - "cruise"       : 巡航状态图标
 * - "eco_mode"     : ECO模式图标
 * - "power_mode"   : POWER模式图标
 * - "cruise_mode"  : CRUISE模式图标
 * - "brake"        : 制动状态图标
 * - "p_gear"       : P挡信号图标
 * - "charging"     : 充电状态图标
 * - "fault"        : 故障指示图标
 * - "battery_low"  : 低电量图标
 * - "reverse"      : 倒车状态图标
 * - "push"         : 推车状态图标
 */
void controller_status_ui_set_icon(const char *icon_type, icon_controller_t *controller);

/**
 * @brief 清理资源
 */
void controller_status_ui_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* CONTROLLER_STATUS_UI_HANDLER_H */
