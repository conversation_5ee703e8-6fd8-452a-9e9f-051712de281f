/**
 * @file app_event.h
 * @brief 事件处理系统头文件
 */

#ifndef APP_EVENT_H
#define APP_EVENT_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 事件类型定义
 */
typedef enum {
    // 系统事件 (0x1000-0x1FFF)
    APP_EVENT_SYS_START = 0x1000,
    APP_EVENT_SYS_TIMER,
    APP_EVENT_SYS_LOW_BATTERY,
    APP_EVENT_SYS_ERROR,
    APP_EVENT_SYS_SHUTDOWN,    // 系统关闭事件
    
    // 通信事件 (0x2000-0x2FFF)
    APP_EVENT_COMM_START = 0x2000,
    APP_EVENT_COMM_DATA_RECEIVED,
    APP_EVENT_COMM_CONNECTED,
    APP_EVENT_COMM_DISCONNECTED,
    
    // 业务事件 (0x3000-0x3FFF)
    APP_EVENT_BIZ_START = 0x3000,
    APP_EVENT_BIZ_VEHICLE_STATUS,
    APP_EVENT_BIZ_BATTERY_INFO,
    APP_EVENT_BIZ_SPEED_INFO,
    APP_EVENT_BIZ_MODE_CHANGED,
    APP_EVENT_BIZ_GEAR_CHANGED,
    APP_EVENT_BIZ_LIGHT_CHANGED,    // 灯光状态变更事件
    APP_EVENT_BIZ_TURN_SIGNAL,      // 转向灯事件
    APP_EVENT_BIZ_SCREEN_CONTROL,   // 屏幕控制事件
    APP_EVENT_BIZ_HICAR_COMMAND,    // HiCar命令事件
    
    // UI事件 (0x4000-0x4FFF)
    APP_EVENT_UI_START = 0x4000,
    APP_EVENT_UI_CLICK,
    APP_EVENT_UI_LONG_PRESS,
    APP_EVENT_UI_VALUE_CHANGED,
    
    // 桥接事件 (0x5000-0x5FFF)
    APP_EVENT_BRIDGE_START = 0x5000,
    APP_EVENT_BRIDGE_HICAR_TO_UART,
    APP_EVENT_BRIDGE_UART_TO_HICAR
} app_event_type_t;

/**
 * @brief 桥接事件数据结构体
 */
typedef struct {
    uint8_t light_ctrl;    // 灯光控制命令
    uint8_t screen_ctrl;   // 屏幕控制命令
} uart_hicar_bridge_event_data_t;

/**
 * @brief 事件结构体
 */
typedef struct {
    app_event_type_t type;     // 事件类型
    uint32_t timestamp;        // 时间戳
    void *sender;              // 事件发送者
    void *data;                // 事件数据
    uint16_t data_size;        // 数据大小
} app_event_t;

/**
 * @brief 事件处理回调函数类型
 */
typedef void (*app_event_handler_t)(app_event_t *event, void *user_data);

/**
 * @brief 初始化事件系统
 * @return 0:成功 其他:失败
 */
int app_event_init(void);

/**
 * @brief 注册事件处理器
 * @param type 事件类型
 * @param handler 处理函数
 * @param user_data 用户数据
 * @return 0:成功 其他:失败
 */
int app_event_register(app_event_type_t type, app_event_handler_t handler, void *user_data);

/**
 * @brief 注册事件处理器（app_event_register的别名）
 * @param type 事件类型
 * @param handler 处理函数
 * @param user_data 用户数据
 * @return 0:成功 其他:失败
 */
int app_event_register_handler(app_event_type_t type, app_event_handler_t handler, void *user_data);

/**
 * @brief 取消注册事件处理器
 * @param type 事件类型
 * @param handler 处理函数
 * @return 0:成功 其他:失败
 */
int app_event_unregister(app_event_type_t type, app_event_handler_t handler);

/**
 * @brief 发布事件
 * @param event 事件指针
 * @return 0:成功 其他:失败
 */
int app_event_post(app_event_t *event);

/**
 * @brief 同步发布事件
 * @param event 事件指针
 * @return 0:成功 其他:失败
 */
int app_event_post_sync(app_event_t *event);

/**
 * @brief 处理事件队列
 */
void app_event_process(void);

/**
 * @brief 清理事件系统资源
 */
void app_event_deinit(void);

/**
 * @brief 获取系统时间戳(毫秒)
 * @return 时间戳
 */
uint32_t app_get_time(void);

#ifdef __cplusplus
}
#endif

#endif /* APP_EVENT_H */ 