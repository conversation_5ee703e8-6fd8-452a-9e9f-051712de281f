/**
 * @file ui_icon_controller.h
 * @brief UI图标控制器接口，用于管理图标闪烁、定时显示等功能
 */

#ifndef UI_ICON_CONTROLLER_H
#define UI_ICON_CONTROLLER_H

#include <stdint.h>
#include <stdbool.h>
#include "lvgl/lvgl.h"
#include "app_event.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 图标状态定义
 */
typedef enum {
    ICON_STATE_OFF,           // 图标关闭
    ICON_STATE_ON,            // 图标常亮
    ICON_STATE_BLINK_SLOW,    // 图标慢闪
    ICON_STATE_BLINK_FAST,    // 图标快闪
    ICON_STATE_DOUBLE_BLINK,  // 图标双闪
    ICON_STATE_PULSE,         // 图标脉冲式闪烁
    ICON_STATE_CUSTOM         // 自定义闪烁模式
} icon_state_t;

/**
 * 图标闪烁模式定义
 */
typedef struct {
    uint16_t on_time;         // 亮的时间(ms)
    uint16_t off_time;        // 灭的时间(ms)
    uint8_t blink_count;      // 闪烁次数，0表示持续闪烁
    uint16_t group_interval;  // 组间隔(ms)，用于双闪等组合模式
    uint32_t total_time;      // 总持续时间(ms)，0表示无限期
} icon_blink_pattern_t;

/**
 * 图标控制器结构体
 */
typedef struct {
    lv_obj_t *icon;           // LVGL图标对象
    icon_state_t state;       // 当前状态
    icon_blink_pattern_t pattern;  // 闪烁模式
    uint32_t start_time;      // 开始时间
    uint32_t last_toggle;     // 最后一次切换时间
    uint8_t blinks_done;      // 已完成闪烁次数
    bool is_visible;          // 当前是否可见
    uint32_t timeout;         // 定时关闭时间(ms)，0表示无限期
    void *user_data;          // 用户数据
} icon_controller_t;

/**
 * @brief 初始化图标控制器系统
 * 
 * @return 0:成功 其他:失败
 */
int icon_controller_system_init(void);

/**
 * @brief 创建图标控制器
 * 
 * @param icon LVGL图标对象
 * @return icon_controller_t* 图标控制器指针，NULL表示失败
 */
icon_controller_t* icon_controller_create(lv_obj_t *icon);

/**
 * @brief 删除图标控制器
 * 
 * @param controller 图标控制器指针
 */
void icon_controller_delete(icon_controller_t *controller);

/**
 * @brief 设置图标状态
 * 
 * @param controller 图标控制器指针
 * @param state 目标状态
 * @return 0:成功 其他:失败
 */
int icon_set_state(icon_controller_t *controller, icon_state_t state);

/**
 * @brief 设置自定义闪烁模式
 * 
 * @param controller 图标控制器指针
 * @param pattern 闪烁模式参数
 * @return 0:成功 其他:失败
 */
int icon_set_custom_pattern(icon_controller_t *controller, 
                          icon_blink_pattern_t *pattern);

/**
 * @brief 设置图标定时关闭
 * 
 * @param controller 图标控制器指针
 * @param timeout_ms 关闭超时时间(ms)
 * @return 0:成功 其他:失败
 */
int icon_set_timeout(icon_controller_t *controller, uint32_t timeout_ms);

/**
 * @brief 图标控制器更新函数(在主循环中调用)
 */
void icon_controller_update(void);

/**
 * @brief 清理图标控制器系统资源
 */
void icon_controller_system_deinit(void);

/**
 * @brief 预定义闪烁模式 - 慢速闪烁
 */
extern const icon_blink_pattern_t BLINK_PATTERN_SLOW;

/**
 * @brief 预定义闪烁模式 - 快速闪烁
 */
extern const icon_blink_pattern_t BLINK_PATTERN_FAST;

/**
 * @brief 预定义闪烁模式 - 双闪
 */
extern const icon_blink_pattern_t BLINK_PATTERN_DOUBLE;

/**
 * @brief 预定义闪烁模式 - 警告闪烁
 */
extern const icon_blink_pattern_t BLINK_PATTERN_WARNING;

#ifdef __cplusplus
}
#endif

#endif /* UI_ICON_CONTROLLER_H */ 