/**
 * @file app_system.h
 * @brief 系统功能头文件
 */

#ifndef APP_SYSTEM_H
#define APP_SYSTEM_H

#include <stdint.h>
#include <stddef.h>
#include <stdarg.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 创建互斥锁
 * @return 互斥锁句柄
 */
void *app_mutex_create(void);

/**
 * @brief 销毁互斥锁
 * @param mutex 互斥锁句柄
 */
void app_mutex_destroy(void *mutex);

/**
 * @brief 锁定互斥锁
 * @param mutex 互斥锁句柄
 */
void app_mutex_lock(void *mutex);

/**
 * @brief 解锁互斥锁
 * @param mutex 互斥锁句柄
 */
void app_mutex_unlock(void *mutex);

/**
 * @brief 延时函数(毫秒)
 * @param ms 延时毫秒数
 */
void app_delay_ms(uint32_t ms);

/**
 * @brief 获取系统时间戳(毫秒)
 * @return 时间戳
 */
uint32_t app_get_time(void);

/**
 * @brief 系统休眠函数
 * @param ms 休眠毫秒数
 */
void app_sleep_ms(uint32_t ms);

/**
 * @brief 获取系统当前时间戳(毫秒)
 * @return 时间戳
 */
uint32_t app_system_time(void);

/**
 * @brief 系统内存分配
 * @param size 内存大小
 * @return 内存指针
 */
void* app_malloc(size_t size);

/**
 * @brief 系统内存释放
 * @param ptr 内存指针
 */
void app_free(void *ptr);

/**
 * @brief 系统日志打印
 * @param format 格式字符串
 * @param ... 可变参数
 */
void app_log(const char *format, ...);

#ifdef __cplusplus
}
#endif

#endif /* APP_SYSTEM_H */ 