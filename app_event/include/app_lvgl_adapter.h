/**
 * @file app_lvgl_adapter.h
 * @brief LVGL适配器头文件
 */

#ifndef APP_LVGL_ADAPTER_H
#define APP_LVGL_ADAPTER_H

#include "app_adapter.h"
#include "lvgl/lvgl.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief UI事件数据结构
 */
typedef struct {
    lv_obj_t *obj;              // LVGL对象
    lv_event_code_t event_code; // LVGL事件代码
    void *user_data;            // 用户数据
} ui_event_data_t;

/**
 * @brief LVGL适配器实例
 */
extern app_adapter_t lvgl_adapter;

/**
 * @brief 为LVGL对象注册事件处理
 * @param obj LVGL对象
 */
void app_ui_register_events(lv_obj_t *obj);

#ifdef __cplusplus
}
#endif

#endif /* APP_LVGL_ADAPTER_H */ 