/**
 * @file app_adapter.h
 * @brief 适配器模块头文件
 */

#ifndef APP_ADAPTER_H
#define APP_ADAPTER_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 适配器结构体
 */
typedef struct {
    const char *name;          // 适配器名称
    int (*init)(void);         // 初始化函数
    void (*process)(void);     // 处理函数
    void (*deinit)(void);      // 清理函数
} app_adapter_t;

/**
 * @brief 注册适配器
 * @param adapter 适配器指针
 * @return 0:成功 其他:失败
 */
int app_adapter_register(app_adapter_t *adapter);

/**
 * @brief 注销适配器
 * @param adapter 适配器指针
 * @return 0:成功 其他:失败
 */
int app_adapter_unregister(app_adapter_t *adapter);

/**
 * @brief 处理所有适配器
 */
void app_adapter_process_all(void);

/**
 * @brief 清理所有适配器
 */
void app_adapter_deinit_all(void);

/**
 * @brief HiCar适配器
 */
extern app_adapter_t hicar_adapter;

/**
 * @brief 测试HiCar功能
 */
void test_hicar_functions(void);

#ifdef __cplusplus
}
#endif

#endif /* APP_ADAPTER_H */ 