#
# app_v2.mk - 事件系统模块集成
#

# 定义app_v2目录
APP_EVENT := app_event

# 添加app_v2源文件
CSRCS += $(wildcard $(LVGL_DIR)/$(APP_EVENT)/src/*.c)
CSRCS += $(wildcard $(LVGL_DIR)/$(APP_EVENT)/adapters/*.c)
# 不包含所有examples目录下的文件，只包含需要的文件
CSRCS += $(LVGL_DIR)/$(APP_EVENT)/examples/app_examples.c
CSRCS += $(LVGL_DIR)/$(APP_EVENT)/examples/uart_hicar_demo.c
# 添加uart_hicar_bridge相关文件
CSRCS += $(LVGL_DIR)/app/fonts/PuHui16.c
CSRCS += $(LVGL_DIR)/app/utils/screen_utils.c

# 添加app_v2头文件目录
CFLAGS += -I$(LVGL_DIR)/$(APP_EVENT)/include \
		  		-I$(LVGL_DIR)/$(APP_EVENT)/examples \
		  		-I$(LVGL_DIR)/app/fonts \
				-I$(LVGL_DIR)/app/utils

# 事件系统库名称
EVENT_SYSTEM_LIB_NAME := libapp_event.a
EVENT_SYSTEM_OBJS := $(patsubst %.c,$(OBJ_DIR)/%.o,$(wildcard $(LVGL_DIR)/$(APP_EVENT)/src/*.c))
EVENT_SYSTEM_OBJS += $(patsubst %.c,$(OBJ_DIR)/%.o,$(wildcard $(LVGL_DIR)/$(APP_EVENT)/adapters/*.c))
