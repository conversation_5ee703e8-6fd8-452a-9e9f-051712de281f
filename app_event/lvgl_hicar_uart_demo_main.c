/**
 * @file lvgl_hicar_uart_demo_main.c
 * @brief LVGL HiCar UART综合演示主程序入口
 * @version 1.0.0
 * @date 2025-06-18 16:27
 */

#include <stdio.h>
#include <unistd.h>
#include <time.h>
#include <signal.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>

#include "lvgl/lvgl.h"
#include "lv_drivers/display/fbdev.h"
#include "lv_drivers/indev/evdev.h"


// 示例相关头文件
#include "./examples/app_examples.h"
#include "./include/app_event.h"
#include "./include/app_adapter.h"
#include "./include/app_system.h"

// UART-HiCar桥接演示函数声明
extern void uart_hicar_demo_init(void);
extern void uart_hicar_demo_process(void);
extern void uart_hicar_demo_deinit(void);

#define DISP_BUF_SIZE (1024 * 600)

// 测试运行标志
static int g_running = 1;
static char g_name[] = "LVGL UART";

/**
 * @brief 信号处理函数
 */
void signal_handler(int sig) {
    printf("接收到信号 %d，准备退出...\n", sig);
    g_running = 0;
}

/**
 * @brief 主函数
 */
int main(int argc, char *argv[])
{
    printf("%s演示程序启动...\n", g_name);
    fflush(stdout);
    
    // 注册信号处理函数
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    printf("信号处理器已注册\n");
    fflush(stdout);

    /*LittlevGL init*/
    lv_init();
    printf("LVGL核心初始化完成\n");
    fflush(stdout);

    /*Linux frame buffer device init*/
    fbdev_init();
    printf("帧缓冲设备初始化完成\n");
    fflush(stdout);

    /*A small buffer for LittlevGL to draw the screen's content*/
    static lv_color_t buf[DISP_BUF_SIZE];

    /*Initialize a descriptor for the buffer*/
    static lv_disp_draw_buf_t disp_buf;
    lv_disp_draw_buf_init(&disp_buf, buf, NULL, DISP_BUF_SIZE);
    printf("显示缓冲区初始化完成\n");
    fflush(stdout);

    /*Initialize and register a display driver*/
    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);
    disp_drv.draw_buf   = &disp_buf;
    disp_drv.flush_cb   = fbdev_flush;
    disp_drv.hor_res    = 1024;
    disp_drv.ver_res    = 600;
    lv_disp_drv_register(&disp_drv);
    printf("显示驱动注册完成\n");
    fflush(stdout);

	/* Linux input device init */
    evdev_init();
    printf("输入设备初始化完成\n");
    fflush(stdout);
	
    /* Initialize and register a display input driver */
    lv_indev_drv_t indev_drv;
    lv_indev_drv_init(&indev_drv);      /*Basic initialization*/

    indev_drv.type = LV_INDEV_TYPE_POINTER;
    indev_drv.read_cb = evdev_read;
    lv_indev_t * my_indev = lv_indev_drv_register(&indev_drv); 
    printf("输入驱动注册完成\n");
    fflush(stdout);

    /*Output prompt information to the console, you can also use printf() to print directly*/
    LV_LOG_USER("LVGL initialization completed!");
    
    // 初始化事件系统
    printf("准备初始化事件系统...\n");
    fflush(stdout);
    app_event_init();
    printf("事件系统初始化完成\n");
    fflush(stdout);

    /*Run App...*/
    // 初始化LVGL UART示例
    printf("准备初始化LVGL UART示例...\n");
    fflush(stdout);
    
    // 初始化UART-HiCar桥接演示
    uart_hicar_demo_init();
   
    printf("LVGL UART示例初始化完成\n");
    fflush(stdout);
    
    // 初始化全局样式
    printf("更新全局样式...\n");
    fflush(stdout);
    app_examples_init_style();
    printf("更新全局样式更新完成\n");
    fflush(stdout);
    
    printf("%s演示程序初始化完成，按Ctrl+C退出\n", g_name);
    fflush(stdout);

    // 添加一个短暂的延迟，确保所有初始化操作完成
    printf("等待系统稳定...\n");
    fflush(stdout);
    usleep(500000); // 500ms
    
    /*Handle LitlevGL tasks (tickless mode)*/
    printf("进入主循环\n");
    fflush(stdout);
    
    int loop_count = 0;
    while(g_running) {
        // 处理LVGL任务
        lv_task_handler();
        
        // 处理事件队列
        app_event_process();
        
        // 处理UART-HiCar桥接演示逻辑
        uart_hicar_demo_process();

        usleep(5000);
        
        // 每10000次循环打印一次心跳，确认程序还在运行
        loop_count++;
        if (loop_count % 10000 == 0) {
            printf("主循环心跳...\n");
            fflush(stdout);
        }
    }

    // 清理资源
    printf("准备清理资源...\n");
    fflush(stdout);
    
    // 清理UART-HiCar桥接演示资源
    uart_hicar_demo_deinit();
    
    // 清理所有适配器
    app_adapter_deinit_all();
    app_event_deinit();
    
    printf("%s演示程序结束\n", g_name);
    return 0;
}


/*Set in lv_conf.h as `LV_TICK_CUSTOM_SYS_TIME_EXPR`*/
uint32_t custom_tick_get(void)
{
    static uint64_t start_ms = 0;
    if(start_ms == 0) {
        struct timeval tv_start;
        gettimeofday(&tv_start, NULL);
        start_ms = (tv_start.tv_sec * 1000000 + tv_start.tv_usec) / 1000;
    }

    struct timeval tv_now;
    gettimeofday(&tv_now, NULL);
    uint64_t now_ms;
    now_ms = (tv_now.tv_sec * 1000000 + tv_now.tv_usec) / 1000;

    uint32_t time_ms = now_ms - start_ms;
    return time_ms;
}
