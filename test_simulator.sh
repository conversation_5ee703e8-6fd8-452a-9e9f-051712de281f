#!/bin/bash
# UART模拟器测试脚本
# 创建时间: 2025-07-31 17:45 GMT+8

echo "=== UART模拟器测试脚本 ==="
echo "创建时间: 2025-07-31 17:45 GMT+8"
echo ""

# 检查模拟器库是否存在
if [ ! -f "libserial_utils_simulator.so" ]; then
    echo "❌ 错误: 模拟器库不存在，请先编译"
    echo "运行: cd app_simulator && make install"
    exit 1
fi

echo "✅ 模拟器库存在: libserial_utils_simulator.so"

# 检查主程序是否存在
if [ ! -f "bin/ebike_x1" ]; then
    echo "❌ 错误: 主程序不存在，请先编译"
    echo "运行: make -j8"
    exit 1
fi

echo "✅ 主程序存在: bin/ebike_x1"
echo ""

# 显示使用方法
echo "=== 使用方法 ==="
echo ""
echo "1. 使用真实串口运行:"
echo "   ./bin/ebike_x1"
echo ""
echo "2. 使用模拟器运行:"
echo "   LD_PRELOAD=./libserial_utils_simulator.so ./bin/ebike_x1"
echo ""
echo "3. 部署到目标设备并使用模拟器:"
echo "   LD_PRELOAD=./libserial_utils_simulator.so make remote-run"
echo ""

# 询问用户是否要测试
read -p "是否要在本地测试模拟器? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo ""
    echo "=== 启动模拟器测试 ==="
    echo "注意: 程序将在模拟器模式下运行"
    echo "按 Ctrl+C 退出"
    echo ""
    
    # 设置环境变量并运行
    export LD_PRELOAD=./libserial_utils_simulator.so
    echo "环境变量设置: LD_PRELOAD=$LD_PRELOAD"
    echo ""
    
    # 运行程序
    echo "启动程序..."
    ./bin/ebike_x1
else
    echo ""
    echo "=== 测试跳过 ==="
    echo "您可以稍后手动运行测试"
fi

echo ""
echo "=== 测试脚本结束 ==="
