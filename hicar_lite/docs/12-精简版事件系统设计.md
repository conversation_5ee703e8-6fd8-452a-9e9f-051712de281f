# 精简版事件系统设计

**文档创建时间**: 2025-07-29 16:45  
**设计人员**: <PERSON> (全栈开发者)  
**设计版本**: v2.0 (精简版)  
**目标代码量**: ~200行  

## 1. 设计目标

### 1.1 核心目标
- **极简设计**: 专注于事件系统核心功能
- **高性能**: 最小化内存占用和处理延迟
- **零依赖**: 不依赖外部模块，自包含实现
- **API兼容**: 完整实现增强版事件系统的16个API

### 1.2 精简原则
- **移除冗余抽象**: 只保留一套API接口
- **去除复杂功能**: 移除插件管理、模块注册等
- **简化数据结构**: 使用最简单有效的数据结构
- **内联优化**: 小函数使用内联减少调用开销

## 2. 核心架构设计

### 2.1 精简架构
```
┌─────────────────────────────────────────────────────────┐
│                Event Bus Core                           │
│                (事件总线核心)                            │
│  ┌─────────────────┐    ┌─────────────────────────────┐ │
│  │   Event Queue   │    │    Subscriber Registry     │ │
│  │   (事件队列)    │    │     (订阅者注册表)         │ │
│  └─────────────────┘    └─────────────────────────────┘ │
│  ┌─────────────────┐    ┌─────────────────────────────┐ │
│  │  Event Dispatch │    │      Statistics            │ │
│  │   (事件分发)    │    │       (统计信息)           │ │
│  └─────────────────┘    └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 2.2 数据结构设计

#### 2.2.1 事件结构 (~20行)
```c
typedef struct {
    uint16_t id;                       /**< 事件ID */
    void *data;                        /**< 事件数据 */
    uint32_t timestamp;                /**< 时间戳 */
} event_t;
```

#### 2.2.2 订阅者结构 (~20行)
```c
typedef struct subscriber {
    uint16_t event_id;                 /**< 订阅的事件ID */
    event_cb_t callback;               /**< 回调函数 */
    void *context;                     /**< 用户上下文 */
    struct subscriber *next;           /**< 链表指针 */
} subscriber_t;
```

#### 2.2.3 事件总线结构 (~30行)
```c
typedef struct {
    // 事件队列
    event_t queue[EVENT_QUEUE_SIZE];
    uint16_t head;
    uint16_t tail;
    uint16_t count;
    
    // 订阅者链表
    subscriber_t *subscribers;
    
    // 统计信息
    uint32_t published_count;
    uint32_t lost_count;
    
    // 状态标志
    bool initialized;
} event_bus_t;
```

## 3. 16个API的精简实现

### 3.1 核心事件API (8个) ~80行
```c
// 1. 初始化事件总线
bool eb_init(void);

// 2. 清理事件总线
void eb_deinit(void);

// 3. 订阅事件
bool eb_subscribe(uint16_t id, event_cb_t cb, void *ctx);

// 4. 取消订阅
bool eb_unsubscribe(uint16_t id, event_cb_t cb);

// 5. 发布事件
bool eb_publish(uint16_t id, void *data);

// 6. 中断安全发布
bool eb_publish_isr(uint16_t id, void *data);

// 7. 分发事件
void eb_dispatch(void);

// 8. 获取统计信息
uint32_t eb_stats_lost(void);
```

### 3.2 插件管理API (3个) ~30行
```c
// 9. 注册插件 (简化为回调注册)
bool eb_plug_register(const char *name, void (*init)(void), void (*deinit)(void));

// 10. 卸载插件
bool eb_plug_unregister(const char *name);

// 11. 启动所有插件
void eb_plug_start_all(void);
```

### 3.3 HAL抽象API (5个) ~40行
```c
// 12. HAL初始化 (空实现)
bool eb_hal_init(void);

// 13. UART发送 (空实现)
void eb_hal_uart_send(const uint8_t *buf, uint16_t len);

// 14. 定时器启动 (空实现)
bool eb_hal_timer_start(uint32_t ms, bool repeat, void (*cb)(void));

// 15. 定时器停止 (空实现)
void eb_hal_timer_stop(void);

// 16. 中断级事件投递
bool eb_hal_post_from_isr(uint16_t id, void *data);
```

## 4. 性能优化设计

### 4.1 内存优化
- **静态分配**: 所有数据结构静态分配，避免malloc
- **环形队列**: 使用环形队列避免内存移动
- **链表池**: 预分配订阅者节点池

### 4.2 时间优化
- **O(1)队列操作**: 环形队列的入队出队都是O(1)
- **快速查找**: 订阅者按事件ID排序，支持快速查找
- **内联函数**: 关键路径函数使用内联

### 4.3 空间优化
- **位操作**: 使用位操作优化状态管理
- **紧凑结构**: 结构体字段排序优化内存对齐
- **最小配置**: 可配置的最小队列和订阅者数量

## 5. 配置参数

### 5.1 编译时配置
```c
#define EVENT_QUEUE_SIZE        32      /**< 事件队列大小 */
#define MAX_SUBSCRIBERS         16      /**< 最大订阅者数量 */
#define MAX_PLUGINS             4       /**< 最大插件数量 */
```

### 5.2 功能开关
```c
#define ENABLE_EVENT_TIMESTAMP  1       /**< 启用事件时间戳 */
#define ENABLE_STATISTICS       1       /**< 启用统计功能 */
#define ENABLE_DEBUG_INFO       0       /**< 启用调试信息 */
```

## 6. 使用示例

### 6.1 基本使用
```c
// 初始化
eb_init();

// 订阅事件
eb_subscribe(0x1001, my_callback, NULL);

// 发布事件
eb_publish(0x1001, &my_data);

// 处理事件
eb_dispatch();

// 清理
eb_deinit();
```

### 6.2 中断使用
```c
// 在中断服务程序中
void timer_isr(void) {
    eb_publish_isr(0x2001, NULL);
}

// 在主循环中
while (running) {
    eb_dispatch();
}
```

## 7. 代码量估算

| 模块 | 预估行数 | 功能描述 |
|------|----------|----------|
| 数据结构定义 | ~30行 | 事件、订阅者、总线结构 |
| 核心事件API | ~80行 | 8个核心事件处理API |
| 插件管理API | ~30行 | 3个简化插件管理API |
| HAL抽象API | ~40行 | 5个HAL抽象API |
| 工具函数 | ~20行 | 内部辅助函数 |
| **总计** | **~200行** | **完整16个API实现** |

## 8. 关键特性

### 8.1 零依赖设计
- 不依赖任何外部模块
- 不需要配置文件
- 不需要HAL层支持

### 8.2 高性能保证
- 事件发布: O(1)时间复杂度
- 事件分发: O(n)，n为订阅者数量
- 内存占用: 固定大小，可预测

### 8.3 线程安全
- 支持中断安全的事件发布
- 简单的临界区保护
- 无锁环形队列设计

### 8.4 易于集成
- 单文件实现
- 清晰的API接口
- 最小化的配置需求

## 9. 与原版对比

| 特性 | 原版 | 精简版 |
|------|------|--------|
| 代码量 | ~366行 | ~200行 |
| 依赖模块 | 3个 | 0个 |
| API数量 | 24个 | 16个 |
| 内存占用 | 动态 | 固定 |
| 复杂度 | 高 | 低 |
| 性能 | 中等 | 高 |

## 10. 总结

精简版事件系统设计的核心优势：

1. **极简设计**: 200行代码实现完整功能
2. **零依赖**: 完全自包含，易于集成
3. **高性能**: 优化的数据结构和算法
4. **API兼容**: 完整支持16个增强版API
5. **易维护**: 简单清晰的代码结构

这个设计将为后续的实现提供清晰的指导，确保我们能够创建一个真正精简高效的事件系统核心。

---
**设计完成时间**: 2025-07-29 16:45  
**下一步**: 开始实现精简版事件系统
