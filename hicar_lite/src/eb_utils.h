/**
 * @file eb_utils.h
 * @brief 事件总线内部工具函数声明
 * @date 2025-07-29 17:22
 * <AUTHOR> (全栈开发者)
 */

#ifndef EB_UTILS_H
#define EB_UTILS_H

#include "eb_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/*==============================================================================
 * 内部工具函数声明
 *============================================================================*/

/**
 * @brief 查找订阅者
 * @param event_id 事件ID
 * @param callback 回调函数
 * @return 找到返回订阅者指针，否则返回NULL
 */
subscriber_t *eb_utils_find_subscriber(uint16_t event_id, event_cb_t callback);

/**
 * @brief 分配订阅者节点
 * @return 成功返回订阅者指针，失败返回NULL
 */
subscriber_t *eb_utils_alloc_subscriber(void);

/**
 * @brief 释放订阅者节点
 * @param sub 订阅者指针
 */
void eb_utils_free_subscriber(subscriber_t *sub);

/**
 * @brief 查找插件
 * @param name 插件名称
 * @return 找到返回插件指针，否则返回NULL
 */
plugin_t *eb_utils_find_plugin(const char *name);

/**
 * @brief 获取事件总线实例
 * @return 事件总线指针
 */
event_bus_t *eb_utils_get_instance(void);

/**
 * @brief 检查事件总线是否已初始化
 * @return 已初始化返回true，否则返回false
 */
bool eb_utils_is_initialized(void);

/**
 * @brief 重置统计信息
 */
void eb_utils_reset_stats(void);

/**
 * @brief 获取队列使用率
 * @return 队列使用率 (0-100)
 */
uint8_t eb_utils_get_queue_usage(void);

#ifdef __cplusplus
}
#endif

#endif /* EB_UTILS_H */
