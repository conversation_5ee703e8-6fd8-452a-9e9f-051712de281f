/**
 * @file test_basic.c
 * @brief 事件总线基础功能测试
 * @date 2025-07-29 17:30
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~150行
 */

#include <stdio.h>
#include <stdbool.h>

#include "eb_types.h"
#include "eb_core.h"
#include "eb_plugin.h"

/*==============================================================================
 * 测试变量
 *============================================================================*/

static int g_test_counter = 0;
static int g_callback_count = 0;

/*==============================================================================
 * 测试回调函数
 *============================================================================*/

/**
 * @brief 基础测试回调函数
 */
static void basic_test_callback(uint16_t id, void *data, void *ctx) {
    g_callback_count++;
    printf("[Test] Basic callback: event 0x%04X, count=%d\n", id, g_callback_count);
    
    if (data) {
        int *value = (int*)data;
        printf("[Test] Data value: %d\n", *value);
    }
    
    (void)ctx; // 避免未使用警告
}

/**
 * @brief 系统测试回调函数
 */
static void system_test_callback(uint16_t id, void *data, void *ctx) {
    printf("[Test] System callback: event 0x%04X\n", id);
    (void)data; (void)ctx;
}

/*==============================================================================
 * 测试插件
 *============================================================================*/

static void test_plugin_init(void) {
    printf("[Test Plugin] Initialized\n");
}

static void test_plugin_deinit(void) {
    printf("[Test Plugin] Deinitialized\n");
}

/*==============================================================================
 * 基础功能测试
 *============================================================================*/

/**
 * @brief 测试事件总线初始化
 */
bool test_eb_init(void) {
    printf("\n--- Testing Event Bus Initialization ---\n");
    
    if (!eb_init()) {
        printf("❌ Event bus initialization failed\n");
        return false;
    }
    
    printf("✅ Event bus initialized successfully\n");
    return true;
}

/**
 * @brief 测试事件订阅
 */
bool test_eb_subscribe(void) {
    printf("\n--- Testing Event Subscription ---\n");
    
    // 测试基础事件订阅
    if (!eb_subscribe(EVENT_TEST_BASIC, basic_test_callback, NULL)) {
        printf("❌ Basic event subscription failed\n");
        return false;
    }
    
    // 测试系统事件订阅
    if (!eb_subscribe(EVENT_SYSTEM_READY, system_test_callback, NULL)) {
        printf("❌ System event subscription failed\n");
        return false;
    }
    
    // 测试重复订阅 (应该成功)
    if (!eb_subscribe(EVENT_TEST_BASIC, basic_test_callback, NULL)) {
        printf("❌ Duplicate subscription failed\n");
        return false;
    }
    
    printf("✅ Event subscription tests passed\n");
    return true;
}

/**
 * @brief 测试事件发布和分发
 */
bool test_eb_publish_dispatch(void) {
    printf("\n--- Testing Event Publishing and Dispatching ---\n");
    
    g_callback_count = 0;
    
    // 发布基础事件
    if (!eb_publish(EVENT_TEST_BASIC, NULL)) {
        printf("❌ Basic event publishing failed\n");
        return false;
    }
    
    // 发布带数据的事件
    int test_data = 42;
    if (!eb_publish(EVENT_TEST_DATA, &test_data)) {
        printf("❌ Data event publishing failed\n");
        return false;
    }
    
    // 发布系统事件
    if (!eb_publish(EVENT_SYSTEM_READY, NULL)) {
        printf("❌ System event publishing failed\n");
        return false;
    }
    
    printf("✅ Events published successfully\n");
    
    // 分发事件
    eb_dispatch();
    
    if (g_callback_count == 0) {
        printf("❌ No callbacks were triggered\n");
        return false;
    }
    
    printf("✅ Event dispatching completed, %d callbacks triggered\n", g_callback_count);
    return true;
}

/**
 * @brief 测试插件管理
 */
bool test_eb_plugin(void) {
    printf("\n--- Testing Plugin Management ---\n");
    
    // 注册测试插件
    if (!eb_plug_register("test_plugin", test_plugin_init, test_plugin_deinit)) {
        printf("❌ Plugin registration failed\n");
        return false;
    }
    
    // 检查插件是否存在
    if (!eb_plug_exists("test_plugin")) {
        printf("❌ Plugin existence check failed\n");
        return false;
    }
    
    // 启动所有插件
    eb_plug_start_all();
    
    // 检查插件是否激活
    if (!eb_plug_is_active("test_plugin")) {
        printf("❌ Plugin activation check failed\n");
        return false;
    }
    
    printf("✅ Plugin management tests passed\n");
    return true;
}

/**
 * @brief 测试统计和健康检查
 */
bool test_eb_stats(void) {
    printf("\n--- Testing Statistics and Health Check ---\n");
    
    // 打印统计信息
    eb_print_stats();
    
    // 检查健康状态
    if (!eb_is_healthy()) {
        printf("⚠️ Event bus health check failed\n");
        // 不返回false，因为这可能是正常的
    } else {
        printf("✅ Event bus is healthy\n");
    }
    
    // 检查丢失事件统计
    uint32_t lost_count = eb_stats_lost();
    printf("Lost events: %u\n", lost_count);
    
    return true;
}

/**
 * @brief 运行所有基础测试
 */
bool run_basic_tests(void) {
    printf("\n=== Event Bus Basic Tests ===\n");
    
    bool all_passed = true;
    
    all_passed &= test_eb_init();
    all_passed &= test_eb_subscribe();
    all_passed &= test_eb_publish_dispatch();
    all_passed &= test_eb_plugin();
    all_passed &= test_eb_stats();
    
    if (all_passed) {
        printf("\n✅ All basic tests passed!\n");
    } else {
        printf("\n❌ Some basic tests failed!\n");
    }
    
    return all_passed;
}
