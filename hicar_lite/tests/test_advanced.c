/**
 * @file test_advanced.c
 * @brief 事件总线高级功能测试
 * @date 2025-07-29 17:32
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~100行
 */

#include <stdio.h>
#include <stdbool.h>

#include "eb_types.h"
#include "eb_core.h"
#include "eb_plugin.h"
#include "eb_hal.h"

/*==============================================================================
 * 高级功能测试
 *============================================================================*/

/**
 * @brief 测试HAL抽象接口
 */
bool test_hal_interfaces(void) {
    printf("\n--- Testing HAL Interfaces ---\n");
    
    // 测试HAL初始化
    if (!eb_hal_init()) {
        printf("❌ HAL initialization failed\n");
        return false;
    }
    
    // 测试UART发送
    // uint8_t test_data[] = {0x01, 0x02, 0x03, 0x04};
    // eb_hal_uart_send(test_data, sizeof(test_data));
    
    // 测试定时器
    if (!eb_hal_timer_start(1000, true, NULL)) {
        printf("❌ Timer start failed\n");
        return false;
    }
    
    eb_hal_timer_stop();
    
    // 测试中断级事件投递
    if (!eb_hal_post_from_isr(EVENT_TIMER_TICK, NULL)) {
        printf("❌ ISR event posting failed\n");
        return false;
    }
    
    printf("✅ HAL interface tests passed\n");
    return true;
}

/**
 * @brief 测试事件取消订阅
 */
bool test_unsubscribe(void) {
    printf("\n--- Testing Event Unsubscription ---\n");
    
    // 先订阅一个事件
    static int callback_called = 0;

    void test_callback(uint16_t id, void *data, void *ctx) {
        callback_called++;
        printf("[Test] Unsubscribe test callback called\n");
        (void)id; (void)data; (void)ctx;
    }
    
    if (!eb_subscribe(EVENT_TEST_BASIC, test_callback, NULL)) {
        printf("❌ Subscription for unsubscribe test failed\n");
        return false;
    }
    
    // 发布事件并分发
    eb_publish(EVENT_TEST_BASIC, NULL);
    eb_dispatch();
    
    if (callback_called == 0) {
        printf("❌ Callback was not called before unsubscribe\n");
        return false;
    }
    
    // 取消订阅
    if (!eb_unsubscribe(EVENT_TEST_BASIC, test_callback)) {
        printf("❌ Event unsubscription failed\n");
        return false;
    }
    
    // 再次发布事件
    int old_count = callback_called;
    eb_publish(EVENT_TEST_BASIC, NULL);
    eb_dispatch();
    
    if (callback_called != old_count) {
        printf("❌ Callback was called after unsubscribe\n");
        return false;
    }
    
    printf("✅ Event unsubscription test passed\n");
    return true;
}

/**
 * @brief 测试队列满的情况
 */
bool test_queue_overflow(void) {
    printf("\n--- Testing Queue Overflow ---\n");
    
    // 发布大量事件，超过队列容量
    int published_count = 0;
    for (int i = 0; i < EVENT_QUEUE_SIZE + 5; i++) {
        if (eb_publish(EVENT_TEST_BASIC, NULL)) {
            published_count++;
        }
    }
    
    printf("Published %d events (queue size: %d)\n", published_count, EVENT_QUEUE_SIZE);
    
    // 检查是否有事件丢失
    uint32_t lost_before = eb_stats_lost();
    
    // 分发所有事件
    eb_dispatch();
    
    uint32_t lost_after = eb_stats_lost();
    
    if (lost_after > lost_before) {
        printf("✅ Queue overflow handled correctly, %u events lost\n", 
               lost_after - lost_before);
    } else {
        printf("⚠️ No events lost (queue might not be full)\n");
    }
    
    return true;
}

/**
 * @brief 测试插件卸载
 */
bool test_plugin_unregister(void) {
    printf("\n--- Testing Plugin Unregistration ---\n");
    
    // 注册一个临时插件
    static bool plugin_init_called = false;
    static bool plugin_deinit_called = false;
    
    void temp_plugin_init(void) {
        plugin_init_called = true;
        printf("[Temp Plugin] Initialized\n");
    }
    
    void temp_plugin_deinit(void) {
        plugin_deinit_called = true;
        printf("[Temp Plugin] Deinitialized\n");
    }
    
    if (!eb_plug_register("temp_plugin", temp_plugin_init, temp_plugin_deinit)) {
        printf("❌ Temporary plugin registration failed\n");
        return false;
    }
    
    // 启动插件
    eb_plug_start_all();
    
    if (!plugin_init_called) {
        printf("❌ Plugin init was not called\n");
        return false;
    }
    
    // 卸载插件
    if (!eb_plug_unregister("temp_plugin")) {
        printf("❌ Plugin unregistration failed\n");
        return false;
    }
    
    if (!plugin_deinit_called) {
        printf("❌ Plugin deinit was not called\n");
        return false;
    }
    
    // 检查插件是否已不存在
    if (eb_plug_exists("temp_plugin")) {
        printf("❌ Plugin still exists after unregistration\n");
        return false;
    }
    
    printf("✅ Plugin unregistration test passed\n");
    return true;
}

/**
 * @brief 运行所有高级测试
 */
bool run_advanced_tests(void) {
    printf("\n=== Event Bus Advanced Tests ===\n");
    
    bool all_passed = true;
    
    all_passed &= test_hal_interfaces();
    all_passed &= test_unsubscribe();
    all_passed &= test_queue_overflow();
    all_passed &= test_plugin_unregister();
    
    if (all_passed) {
        printf("\n✅ All advanced tests passed!\n");
    } else {
        printf("\n❌ Some advanced tests failed!\n");
    }
    
    return all_passed;
}
