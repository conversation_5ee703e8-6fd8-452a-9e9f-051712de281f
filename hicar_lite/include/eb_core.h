/**
 * @file eb_core.h
 * @brief 事件总线核心API声明
 * @date 2025-07-29 17:17
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~50行
 */

#ifndef EB_CORE_H
#define EB_CORE_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include "eb_types.h"

#ifdef __cplusplus
extern "C" {
#endif

/*==============================================================================
 * 核心事件API (8个)
 *============================================================================*/

/**
 * @brief 初始化事件总线
 * @return 成功返回true，失败返回false
 */
bool eb_init(void);

/**
 * @brief 清理事件总线
 */
void eb_deinit(void);

/**
 * @brief 订阅事件
 * @param id 事件ID
 * @param cb 回调函数
 * @param ctx 用户上下文
 * @return 成功返回true，失败返回false
 */
bool eb_subscribe(uint16_t id, event_cb_t cb, void *ctx);

/**
 * @brief 取消订阅事件
 * @param id 事件ID
 * @param cb 回调函数
 * @return 成功返回true，失败返回false
 */
bool eb_unsubscribe(uint16_t id, event_cb_t cb);

/**
 * @brief 发布事件
 * @param id 事件ID
 * @param data 事件数据
 * @return 成功返回true，失败返回false
 */
bool eb_publish(uint16_t id, void *data);

/**
 * @brief 中断安全发布事件
 * @param id 事件ID
 * @param data 事件数据
 * @return 成功返回true，失败返回false
 */
bool eb_publish_isr(uint16_t id, void *data);

/**
 * @brief 分发事件
 */
void eb_dispatch(void);

/**
 * @brief 获取丢失事件统计
 * @return 丢失的事件数量
 */
uint32_t eb_stats_lost(void);

/*==============================================================================
 * 调试和统计接口
 *============================================================================*/

/**
 * @brief 获取事件总线统计信息
 */
void eb_print_stats(void);

/**
 * @brief 检查事件总线状态
 * @return 健康返回true，否则返回false
 */
bool eb_is_healthy(void);

/*==============================================================================
 * 常用事件ID定义
 *============================================================================*/

#define EVENT_TEST_BASIC        0x0001  /**< 基础测试事件 */
#define EVENT_TEST_DATA         0x0002  /**< 数据测试事件 */
#define EVENT_SYSTEM_READY      0x1000  /**< 系统就绪事件 */
#define EVENT_SYSTEM_ERROR      0x1001  /**< 系统错误事件 */
#define EVENT_USER_INPUT        0x2000  /**< 用户输入事件 */
#define EVENT_TIMER_TICK        0x3000  /**< 定时器滴答事件 */

/*==============================================================================
 * 便利宏定义
 *============================================================================*/

 /**
 * @brief 发布简单事件 (无数据)
 */
#define PUBLISH_EVENT(id)           eb_publish((id), NULL)

/**
 * @brief 发布数据事件
 */
#define PUBLISH_EVENT_DATA(id, ptr) eb_publish((id), (ptr))

#ifdef __cplusplus
}
#endif

#endif /* EB_CORE_H */
