/**
 * @file main.c
 * @brief 事件总线测试主程序
 * @date 2025-07-29 17:33
 * <AUTHOR> (全栈开发者)
 * @note 目标代码量: ~80行
 */

#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <stdbool.h>

#include "eb_types.h"

/*==============================================================================
 * 外部测试函数声明
 *============================================================================*/

extern bool run_basic_tests(void);
extern bool run_advanced_tests(void);

/*==============================================================================
 * 全局变量
 *============================================================================*/

static volatile bool g_running = true;

/*==============================================================================
 * 私有函数声明
 *============================================================================*/

static void signal_handler(int sig);
static void print_startup_info(void);

/*==============================================================================
 * 主程序入口
 *============================================================================*/

/**
 * @brief 主程序入口
 * @param argc 参数个数
 * @param argv 参数数组
 * @return 程序退出码
 */
int main(int argc, char *argv[])
{
    (void)argc; (void)argv;  // 避免未使用警告

    // 打印启动信息
    print_startup_info();

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 运行基础测试
    bool basic_passed = run_basic_tests();

    // 运行高级测试
    bool advanced_passed = run_advanced_tests();

    // 清理事件总线
    eb_deinit();

    // 输出测试结果
    if (basic_passed && advanced_passed) {
        printf("\n🎉 All tests passed successfully!\n");
        return EXIT_SUCCESS;
    } else {
        printf("\n❌ Some tests failed!\n");
        return EXIT_FAILURE;
    }
}

/*==============================================================================
 * 私有函数实现
 *============================================================================*/

/**
 * @brief 信号处理函数
 * @param sig 信号号
 */
static void signal_handler(int sig)
{
    printf("\nReceived signal %d, shutting down...\n", sig);
    g_running = false;
}

/**
 * @brief 打印启动信息
 */
static void print_startup_info(void)
{
    printf("\n");
    printf("========================================\n");
    printf("   模块化事件系统测试程序\n");
    printf("   Enhanced Event Bus v2.0\n");
    printf("========================================\n");
    printf("Build Date: %s %s\n", __DATE__, __TIME__);
    printf("Architecture: Modular Design\n");
    printf("Features: 16 Core APIs\n");
    printf("Queue Size: %d events\n", EVENT_QUEUE_SIZE);
    printf("Max Subscribers: %d\n", MAX_SUBSCRIBERS);
    printf("Max Plugins: %d\n", MAX_PLUGINS);
    printf("========================================\n\n");
}
